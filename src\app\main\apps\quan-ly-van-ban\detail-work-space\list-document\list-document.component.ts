import {
  Component,
  <PERSON>ementRef,
  <PERSON>L<PERSON><PERSON>,
  OnInit,
  SecurityContext,
  ViewChild,
  ViewEncapsulation,
  TemplateRef,
} from "@angular/core";
import { FormControl } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import {
  NgbActiveModal,
  NgbModal,
  NgbPanelChangeEvent,
} from "@ng-bootstrap/ng-bootstrap";
import { WebSocketService } from "app/auth/service/webSocket.service";
import { ViewDetailFileService } from "app/layout/components/view-detail-file/view-detail-file.service";
import { DocumentStatus } from "app/models/DocumentStatus";
import { FormType } from "app/models/FormType";
import { ShowContent } from "app/models/ShowContent";
import { TypeDocument } from "app/models/TypeDocument";
import { ProgressToastStore } from "app/shared/progress-toast.shared";
import { environment } from "environments/environment";
import { DragulaService } from "ng2-dragula";
import { FileUploader } from "ng2-file-upload";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import {
  debounceTime,
  distinctUntilChanged,
  skip,
  takeUntil,
  finalize
} from "rxjs/operators";
import Swal from "sweetalert2";
import { ChatbotService } from "../chatbot/chatbot.service";
import { ComnpareClauseChatbotService } from "../compare-clause-chatbot/comnpare-clause-chatbot.service";
import { DetailClauseService } from "../detail-clause/detail-clause.service";
import { DetailWorkSpaceService } from "../detail-work-space.service";
import { ListDocumentService } from "./list-document.service";
import { LoaiVanBan } from "app/models/LoaiVanBan";
import { DomSanitizer, SafeHtml } from "@angular/platform-browser";
import { Chips } from "primeng/chips";
import { ChipGroupType } from "app/models/ChipGroupType";
import { TrangThaiHieuLuc } from "app/models/TrangThaiHieuLuc";
import { FlatpickrOptions } from 'ng2-flatpickr';
import { Ng2FlatpickrComponent } from 'ng2-flatpickr';
import { LegalProgress } from "app/auth/models/status";

interface RaSoatConditions {
  so_hieu: string[];
  van_ban_dich: string[];
  van_ban_nguon: string[];
  loai_van_ban: string[];
  tinh_trang_hieu_luc: string[];
  ngay_ban_hanh_start: string;
  ngay_ban_hanh_end: string;
  ngay_co_hieu_luc_start: string;
  ngay_co_hieu_luc_end: string;
}


@Component({
  selector: "app-list-document",
  templateUrl: "./list-document.component.html",
  styleUrls: ["./list-document.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class ListDocumentComponent implements OnInit {
  @ViewChild("editDocumentNameModal") editDocumentNameModal!: any;
  @ViewChild("clauseModal") clauseModal!: any;
  @ViewChild("scrollMe") scrollMe: ElementRef;
  @ViewChild("modalRaSoatCuThe", { read: TemplateRef }) modalRaSoatCuThe!: TemplateRef<any>;
  @ViewChild("chipsRef") chipsRef!: Chips;
  @ViewChild('pickerNBH') pickerNBH!: Ng2FlatpickrComponent;
  @ViewChild('pickerNCHL') pickerNCHL!: Ng2FlatpickrComponent;
  public scrollTop: number = null;
  public isLoading = false;
  isFetchingDocuments = false;
  isFetchingDocumentContent = false;
  isFetchingClauseOfFile: boolean = false;
  isUploading: boolean = false;
  public checkTop: boolean = true;
  public valuesSoHieuCanRaSoat: string[];
  public valuesVanBanNguon: string[];
  public valuesVanBanDich: string[];
  public valuesLoaiVanBan: string[];
  public loaiVanBanOptions = LoaiVanBan.filter(
    (o) => (o.value ?? "").trim().length > 0
  );
  public valuesTinhTrangHieuLuc: string[];
  public page = 1;
  public typeDocument = TypeDocument;
  public workSpaceId: string = "";
  public listDocument: any = [];
  public listDocumentFilter: any = [];
  isSidebarExpanded = true;
  contextMenuVisible = false;
  contextMenuClause = false;
  contextMenuPosition = { x: 0, y: 0 };
  contextMenuItem: any = null;
  contextClauseItem: any = null;
  public selectedFile: any;
  public fileExtension: string = "";
  public fileName: FormControl = new FormControl();
  public DocumentStatus = DocumentStatus;
  public listDieuKhoan = [];
  public listDieuKhoanOriginal = [];
  public listDieuKhoanSearch = [];
  activeIds: string[] = [];
  public row: any;
  public title: string;
  public type: FormType;
  public searchDocument: FormControl = new FormControl("");
  public selectedClause: any;
  private _unSubAll: Subject<any> = new Subject();
  public total = 0;
  public sortFile = ["Tên", "Thời gian tạo", "Hiệu lực"];
  public valueSort: string;
  public hasFileFromSearch: boolean = false;
  public fileTermFormSearch: any = []; // file hiển thị đang được tìm kiếm
  public isHaveFileIdInUrl: boolean = true; // kiểm tra xem có fileId trong url hay không
  public typeDocumentURL: string;
  public hoveredItem: any = null;
  public listConditionRaSoat: any = {};
  private countNonEmpty(arr?: string[]): number {
    if (!Array.isArray(arr)) return 0;
    return arr.filter(v => (v ?? '').trim().length > 0).length;
  }

  private countConditions(raw: any): number {
    const c = this.ensureConditionShape(raw);
    let count = 0;

    // Đếm theo số mục cho các nhóm chips
    count += this.countNonEmpty(c.so_hieu);
    count += this.countNonEmpty(c.van_ban_dich);
    count += this.countNonEmpty(c.van_ban_nguon);
    count += this.countNonEmpty(c.loai_van_ban);
    count += this.countNonEmpty(c.tinh_trang_hieu_luc);

    // Mỗi date range chỉ tính 1 điều kiện
    const hasNBH = !!(c.ngay_ban_hanh_start || c.ngay_ban_hanh_end);
    if (hasNBH) count += 1;

    const hasNCHL = !!(c.ngay_co_hieu_luc_start || c.ngay_co_hieu_luc_end);
    if (hasNCHL) count += 1;

    return count;
  }
  public countCoundition: number = 0;
  public modelNgayBanHanh: Date[] = [];
  public modelNgayCoHieuLuc: Date[] = [];
  private parseYMD(s: string): Date | null {
    const m = /^(\d{4})-(\d{2})-(\d{2})$/.exec(s || '');
    if (!m) return null;
    return new Date(+m[1], +m[2] - 1, +m[3]);
  }
  public uploader: FileUploader = new FileUploader({
    url: `${environment.apiUrl}/ocr/documents`,
    isHTML5: true,
    allowedMimeType: [
      "application/pdf",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
    ],
    maxFileSize:
      JSON.parse(localStorage.getItem("package")).max_file_size_per_upload *
      1024 *
      1024, // 10MB
  });
  public termHistory = [];
  public items;
  public showSearch = false;
  public searchText = "";
  states = this.progressToastStore.states$;
  private destroy$ = new Subject<void>();
  safeHtml: SafeHtml;
  // public htmlString = "";
  public soHieuOptions = ["11/2022/TT-BTC", "24/2023/NĐ-CP", "31/2024/QH15"];
  public chipsGroupType = ChipGroupType;
  public listTrangThaiHieuLuc: any = TrangThaiHieuLuc;
  public graphData: any = null;
  isSmallScreen = false;
  public valuesNgayBanHanhStart: string = '';
  public valuesNgayBanHanhEnd: string = '';
  public valuesNgayCoHieuLucStart: string = '';
  public valuesNgayCoHieuLucEnd: string = '';

  public dateRangeOpts: FlatpickrOptions = {
    mode: 'range',
    altInput: true,
    altFormat: 'd-m-Y',
    dateFormat: 'Y-m-d',
    allowInput: false,
    locale: { rangeSeparator: ' đến ' },
    wrap: true,
    onReady: (selectedDates, _str, instance: any) => {
      if ((!selectedDates || selectedDates.length === 0) && instance) {
        if (instance.altInput) instance.altInput.placeholder = 'Tất cả';
        const wrapInput: HTMLInputElement | null =
          instance?.element?.querySelector?.('[data-input]') ?? null;
        if (wrapInput) wrapInput.placeholder = 'Tất cả';
      }
    },
    
  };

  public dateRangeNBHConfig: FlatpickrOptions = { ...this.dateRangeOpts };
  public dateRangeNCHLConfig: FlatpickrOptions = { ...this.dateRangeOpts };

  constructor(
    private toastService: ToastrService,
    private documentService: ListDocumentService,
    private route: ActivatedRoute,
    private modalService: NgbModal,
    private viewDetailFile: ViewDetailFileService,
    private router: Router,
    private workSpace: DetailWorkSpaceService,
    private compareChatbotService: ComnpareClauseChatbotService,
    private webSocketService: WebSocketService,
    private detailClauseSerive: DetailClauseService,
    private dragulaService: DragulaService,
    private chatbotService: ChatbotService,
    private _toastrService: ToastrService,
    private progressToastStore: ProgressToastStore,
    private sanitizer: DomSanitizer
  ) {
    // Khi file không hợp lệ do sai loại hoặc quá dung lượng
    this.uploader.onWhenAddingFileFailed = (item, filter, options) => {
      if (filter.name === "mimeType") {
        this.toastService.error(
          `Tài liệu không đúng định dạng PDF,DOC hoặc DOCX ❌`,
          "Sai định dạng 🚫",
          {
            toastClass: "toast ngx-toastr",
            positionClass: "toast-top-right",
            closeButton: true,
          }
        );
      }
      if (filter.name === "fileSize") {
        this.toastService.error(
          `Tài liệu quá lớn (> ${
            JSON.parse(localStorage.getItem("package"))
              .max_file_size_per_upload / 1000
          }MB) ❌`,
          "Lỗi",
          {
            toastClass: "toast ngx-toastr",
            positionClass: "toast-top-right",
            closeButton: true,
          }
        );
      }
    };
    if (!dragulaService.find("handle-list")) {
      dragulaService.createGroup("handle-list", {
        moves: function (el, container, handle) {
          return handle.classList.contains("handle");
        },
      });
    }
  }
  getAllSoHieuVBCC() {
    const fileID = this.selectedFile.id ?? "";
    if (fileID) {
      this.viewDetailFile.getLuocDo(fileID).subscribe(
        (res) => {
          this.soHieuOptions = (res.van_ban_can_cu as any[]).map(
            (e) => e.so_hieu
          );
        },
        (error) => {
          this.soHieuOptions = [];
        }
      );
    }
  }
  onChipAdd(type: string, raw: string) {
    const v = (raw ?? "").trim();
    if (!v) return;
    this.pushUnique(type, v);
    this.refocusChips();
  }
  addSoHieu() {
    const before = (this.valuesSoHieuCanRaSoat ?? []).length;

    for (const v of this.soHieuOptions) {
      this.pushUnique(this.chipsGroupType.so_hieu, v);
    }

    const after = (this.valuesSoHieuCanRaSoat ?? []).length;
    const added = after - before;
    if (added === 0) {
      this.toastService.info("Tất cả số hiệu đã có sẵn", "Không có mục mới", {
        toastClass: "toast ngx-toastr",
        positionClass: "toast-top-right",
        closeButton: true,
      });
    }
    this.refocusChips();
  }
  private pushUnique(type: string, v: string) {
    const norm = (s: string) =>
      (s ?? "").trim().replace(/\s+/g, " ").toLowerCase();
    const key = norm(v);
    let arr: any = [];
    if (type === this.chipsGroupType.so_hieu) {
      arr = this.valuesSoHieuCanRaSoat ?? [];
    }
    if (type === this.chipsGroupType.van_ban_dich) {
      arr = this.valuesVanBanDich ?? [];
    }
    if (type === this.chipsGroupType.tinh_trang_hieu_luc) {
      arr = this.valuesTinhTrangHieuLuc ?? [];
    }
    const matchesIdx: number[] = [];
    for (let i = 0; i < arr.length; i++) {
      if (norm(arr[i]) === key) matchesIdx.push(i);
    }

    if (matchesIdx.length === 0) {
      if (type === this.chipsGroupType.so_hieu) {
        this.valuesSoHieuCanRaSoat = [...arr, v];
      }
      if (type === this.chipsGroupType.van_ban_dich) {
        this.valuesVanBanDich = [...arr, v];
      }
      if (type === this.chipsGroupType.tinh_trang_hieu_luc) {
        this.valuesTinhTrangHieuLuc = [...arr, v];
      }
      return;
    }

    const lastMatch = matchesIdx[matchesIdx.length - 1];
    const isLastElement = lastMatch === arr.length - 1;
    const hasPrevious = matchesIdx.length >= 2;
    if (isLastElement && hasPrevious) {
      if (type === this.chipsGroupType.so_hieu) {
        this.valuesSoHieuCanRaSoat = arr.slice(0, -1);
      }
      if (type === this.chipsGroupType.van_ban_dich) {
        this.valuesVanBanDich = arr.slice(0, -1);
      }
      if (type === this.chipsGroupType.tinh_trang_hieu_luc) {
        this.valuesTinhTrangHieuLuc = arr.slice(0, -1);
      }
    }
  }

  private refocusChips() {
    setTimeout(() => {
      try {
        const host: HTMLElement | null =
          (this.chipsRef as any)?.el?.nativeElement ?? null;
        const input: HTMLInputElement | null =
          host?.querySelector("input") ?? null;
        input?.focus();
      } catch {}
    }, 0);
  }

  handleItemSwap(list: any[], fromIndex: number, toIndex: number) {
    const item1 = list[toIndex];
    const item2 = list[fromIndex];
    // // console.log("Item được hoán đổi:", item1, item2);
  }
  // Tuỳ chọn: nếu bạn muốn xử lý thêm khi drop
  onFileDrop(fileList: FileList) {
    const listFile = Array.from(fileList);
    const allowedTypes = [
      "application/pdf", //pdf
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
      "application/msword", // .doc
    ];
    const fileConvert = listFile;
    const validFiles = listFile.filter((file) =>
      allowedTypes.includes(file.type)
    );
    this.isUploading = true;
    for (let i = 0; i < validFiles.length; i++) {
      const file = fileConvert[i];

      // Nếu hợp lệ, thêm vào formData và gửi lên server
      const formData = new FormData();
      formData.append("origin", file);
      formData.append("workspace_id", this.workSpaceId);

      this.documentService.addDocument(formData)
        .pipe(finalize(() => { this.isUploading = false; this.isFetchingDocuments = true; }))
        .subscribe(() => {
          formData.delete("origin");
          formData.delete("workspace_id");
          setTimeout(() => {
            this.getAllDocumentInWorkSpace("", null);
          }, 1000);
        }
      );
    }
  }
  private checkScreenSize(): void {
    this.isSmallScreen = window.innerWidth < 1200; // hoặc breakpoint bạn muốn
  }
  private toYMD(date: Date | undefined): string {
    if (!date) return '';
    const y = date.getFullYear();
    const m = String(date.getMonth() + 1).padStart(2, '0');
    const d = String(date.getDate()).padStart(2, '0');
    return `${y}-${m}-${d}`;
  }
  private setDatesWhenPickersReady() {
    const trySet = () => {
      const fpNBH  = this.pickerNBH?.flatpickr as any;
      const fpNCHL = this.pickerNCHL?.flatpickr as any;

      if (!fpNBH || !fpNCHL) {
        // còn chưa mount xong -> thử lại ở frame kế tiếp
        return setTimeout(trySet, 0);
      }

      // NBH
      if (this.modelNgayBanHanh?.length === 2) {
        fpNBH.setDate(this.modelNgayBanHanh, true);
      } else {
        fpNBH.clear();
      }
      // console.log('[RSD][setDate] NBH selectedDates:', fpNBH.selectedDates);

      // NCHL
      if (this.modelNgayCoHieuLuc?.length === 2) {
        fpNCHL.setDate(this.modelNgayCoHieuLuc, true);
      } else {
        fpNCHL.clear();
      }
      // console.log('[RSD][setDate] NCHL selectedDates:', fpNCHL.selectedDates);
    };

    // bắt đầu vòng chờ
    setTimeout(trySet, 0);
  }
  onNgayBanHanhModel(val: any) {
    const fp: any = this.pickerNBH?.flatpickr;

    // Không có ngày nào (xoá hết trong input hoặc clear)
    if (!val || (Array.isArray(val) && val.length === 0)) {
      this.valuesNgayBanHanhStart = '';
      this.valuesNgayBanHanhEnd   = '';

      // Cập nhật placeholder "Tất cả"
      if (fp?.altInput && !fp.altInput.value) {
        fp.altInput.placeholder = 'Tất cả';
      }
      const wrapInput =
        fp?.element?.querySelector?.('[data-input]') as HTMLInputElement | null;
      if (wrapInput && !wrapInput.value) {
        wrapInput.placeholder = 'Tất cả';
      }
      return;
    }

    // Có chọn range
    const [start, end] = Array.isArray(val) ? val : [];
    this.valuesNgayBanHanhStart = this.toYMD(start);
    this.valuesNgayBanHanhEnd   = this.toYMD(end ?? start ?? undefined);
  }


  onNgayCoHieuLucModel(val: any) {
    const fp: any = this.pickerNCHL?.flatpickr;

    if (!val || (Array.isArray(val) && val.length === 0)) {
      this.valuesNgayCoHieuLucStart = '';
      this.valuesNgayCoHieuLucEnd   = '';

      if (fp?.altInput && !fp.altInput.value) {
        fp.altInput.placeholder = 'Tất cả';
      }
      const wrapInput =
        fp?.element?.querySelector?.('[data-input]') as HTMLInputElement | null;
      if (wrapInput && !wrapInput.value) {
        wrapInput.placeholder = 'Tất cả';
      }
      return;
    }

    const [start, end] = Array.isArray(val) ? val : [];
    this.valuesNgayCoHieuLucStart = this.toYMD(start);
    this.valuesNgayCoHieuLucEnd   = this.toYMD(end ?? start ?? undefined);
  }

  clearNBH(evt?: Event) {
    evt?.preventDefault();
    const fp: any = this.pickerNBH?.flatpickr;
    if (fp) {
      fp.clear();
      if (fp.altInput) fp.altInput.placeholder = 'Tất cả';
    } else {
      this.modelNgayBanHanh = [];
    }
    this.valuesNgayBanHanhStart = '';
    this.valuesNgayBanHanhEnd   = '';
  }

  clearNCHL(evt?: Event) {
    evt?.preventDefault();
    const fp: any = this.pickerNCHL?.flatpickr;
    if (fp) {
      fp.clear();
      if (fp.altInput) fp.altInput.placeholder = 'Tất cả';
    } else {
      this.modelNgayCoHieuLuc = [];
    }
    this.valuesNgayCoHieuLucStart = '';
    this.valuesNgayCoHieuLucEnd   = '';
  }

  onPickNgayBanHanh(dates: Date[]) {
    const [start, end] = dates || [];
    this.valuesNgayBanHanhStart = this.toYMD(start);
    this.valuesNgayBanHanhEnd   = this.toYMD(end ?? start ?? undefined);
    // console.log('[RSD][onPickNBH][close]', { dates, start, end, 
    //   startYMD: this.valuesNgayBanHanhStart, endYMD: this.valuesNgayBanHanhEnd });
  }
  onPickNgayCoHieuLuc(dates: Date[]) {
    const [start, end] = dates || [];
    this.valuesNgayCoHieuLucStart = this.toYMD(start);
    this.valuesNgayCoHieuLucEnd   = this.toYMD(end ?? start ?? undefined);
  }
  private asDefaultDate(val: (Date | string)[] | undefined): any {
    return (val && val.length) ? val : undefined;
  }
  ngOnInit(): void {
    const current_user = JSON.parse(localStorage.getItem("current_User"));
    this.checkScreenSize();
    window.addEventListener("resize", () => this.checkScreenSize());
    const params = {
      user_id: current_user.id,
    };
    this.documentService.getActiveCompareJobs(params).subscribe(
      ({ jobs }) => {
        for (const j of jobs) {
          const d = j.data;
          this.progressToastStore.upsertFromServer(d.job_id, d);
        }
      },
      (error) => {
        // console.log("resjob", error);
      }
    );
    this.webSocketService.messageSubject
      .pipe(takeUntil(this._unSubAll))
      .subscribe((res) => {
        if (!res) return;
        console.log(res);
        if (res.data.event == LegalProgress.RA_SOAT_DIEU_KHOAN) {
          this.getClauseOfFile(this.selectedFile);
        } else {
          const idFile = res.data.data.document_id || res.data.data.id;
          const item = this.listDocumentFilter.find(
            (file) => file.id === idFile
          );

          if (item) {
            const percentage = res.data.percentage;
            item.percentage = percentage;
            item.status_display = res.data.data.status_display;
          }

          if (
            // res.data.data.status == DocumentStatus.STATUS_SUCCESS &&
            res.data.event == LegalProgress.LUU_TRU_NOI_DUNG || // trạng thái thành công
            res.data.data.status === DocumentStatus.STATUS_FAILED
          ) {
            this.getAllDocumentInWorkSpace("", null);
          }
        }
        const eventName = res?.data?.event || res?.event;
        const data = res?.data?.data || res?.data || {};
        const jobId = this.progressToastStore.resolveJobId(data);
        const pct = this.progressToastStore.clampPercent(
          res?.data?.percentage ?? res?.percentage ?? data?.percent ?? 0
        );
        if (!jobId) return;
        switch (eventName) {
          case LegalProgress.EXPORT_FILE_DOING: {
            const s = this.progressToastStore.ensureState(jobId);
            s.active = true;
            s.status = "processing";
            s.percent = Math.max(s.percent ?? 0, pct);
            s.msg = data?.message || "Đang xử lý...";
            s.title = data?.title || "";
            break;
            // this._toastrService.success(`${data?.message}`, "Thông báo", {
            //   closeButton: true,
            //   positionClass: "toast-top-right",
            //   toastClass: "toast ngx-toastr",
            //   enableHtml: true,
            // });
          }
          case LegalProgress.EXPORT_FILE_DONE: {
            const s = this.progressToastStore.ensureState(jobId);
            s.active = true;
            s.status = "success";
            s.percent = Math.max(s.percent ?? 0, pct);
            s.msg = data?.message || "Hoàn tất";
            s.title = data?.title || "";
            // this._toastrService.success(`${data?.message}`, "Thông báo", {
            //   closeButton: true,
            //   positionClass: "toast-top-right",
            //   toastClass: "toast ngx-toastr",
            //   enableHtml: true,
            // });
            if (s.autoHide)
              setTimeout(
                () => this.progressToastStore.remove(jobId),
                s.autoHideDelayMs ?? 4000
              );
            const url = data?.download_url;
            const fileName = data?.file_name || "auto_compare_report.xlsx";
            if (url) {
              const a = document.createElement("a");
              a.href = url;
              a.download = fileName;
              document.body.appendChild(a);
              a.click();
              document.body.removeChild(a);
            }
            break;
          }
          case LegalProgress.EXPORT_FILE_ERROR: {
            this.progressToastStore.remove(jobId);
            this._toastrService.error(`${data?.message}`, "Thất bại", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
              enableHtml: true,
            });
            break;
          }
          default:
            break;
        }
      });
    this.hasFileFromSearch = false;
    this.workSpaceId = this.route.snapshot.params.id;
    this.route.queryParams.subscribe((params) => {
      this.typeDocumentURL = params["type"];
      if (params.fileId) {
        this.isHaveFileIdInUrl = false;
      } else {
        this.isHaveFileIdInUrl = true;
      }
    });

    this.workSpace.isSaveFileFromSearch
      .pipe(takeUntil(this._unSubAll))
      .subscribe((res) => {
        if (res) {
          // this.page = 1;
          // this.checkTop = true;
          // this.isLoading = false;
          this.getAllDocumentInWorkSpace("", null);
        }
      });
    this.documentService.refreshClause
      .pipe(takeUntil(this._unSubAll))
      .subscribe((res) => {
        if (res) {
          if (this.type == FormType.Create) {
            this.getClauseOfFile(this.row);
          } else {
            this.getClauseOfFile(this.selectedFile);
          }
        }
      });
    this.searchDocument.valueChanges
      .pipe(
        takeUntil(this._unSubAll),
        debounceTime(500),
        distinctUntilChanged()
      )
      .subscribe((res) => {
        // this.page = 1;
        // this.checkTop = true;
        // this.isLoading = false;
        this.getAllDocumentInWorkSpace(res, null, true);
      });

    // Khởi tạo mảng lưu lịch sử term
    this.termHistory = []; // Lưu các term đã tìm kiếm

    this.documentService.FileSearchTemp.pipe(
      takeUntil(this._unSubAll),
      skip(1)
    ).subscribe((res) => {
      if (res !== "Xoá") {
        if (res == null) {
          this.fileTermFormSearch = [];
          this.termHistory = [];
        } else {
          // Lưu term mới vào lịch sử
          this.termHistory.push(res);

          // Chuyển đổi dữ liệu file từ kết quả tìm kiếm
          const fileFromSearch = this.convertFileFromSearch(res);

          // fileTermFormSearch luôn chỉ có 1 phần tử
          this.fileTermFormSearch = [fileFromSearch];
          this.hasFileFromSearch = true;
          this.selectedFile = fileFromSearch;

          // Các xử lý khác như cũ
          this.activeIds = [];
          if (fileFromSearch.doc_id) {
            if (this.selectedClause)
              this.selectedClause.clause_id = fileFromSearch.ID;
            setTimeout(() => {
              this.viewDetailFile.clauseId.next(fileFromSearch.term_id);
            }, 1000);
          }
          this.getClauseOfFile(fileFromSearch);
        }
      } else if (res === "Xoá") {
        // Xoá term mới nhất khỏi lịch sử
        if (this.termHistory.length > 0) {
          this.termHistory.pop();
        }

        // Lấy term gần nhất (nếu còn) để trả về fileTermFormSearch
        if (this.termHistory.length > 0) {
          const lastTerm = this.termHistory[this.termHistory.length - 1];
          const fileFromSearch = this.convertFileFromSearch(lastTerm);

          this.fileTermFormSearch = [fileFromSearch];
          this.selectedFile = fileFromSearch;
          this.hasFileFromSearch = true;

          // Các xử lý khác như cũ
          this.activeIds = [];
          if (fileFromSearch.doc_id) {
            if (this.selectedClause)
              this.selectedClause.clause_id = fileFromSearch.ID;
            setTimeout(() => {
              this.viewDetailFile.clauseId.next(fileFromSearch.term_id);
            }, 1000);
          }
          this.getClauseOfFile(fileFromSearch);
        } else {
          // Nếu không còn term nào, reset trạng thái
          this.fileTermFormSearch = [];
          this.selectedFile = null;
          this.hasFileFromSearch = false;
          this.activeIds = [];
          // Reset các biến khác nếu cần
        }
      }
    });
    this.dragulaService
      .dropModel("handle-list")
      .subscribe(({ targetModel }) => {
        // // console.log("targetModel:", targetModel);
        this.documentService.updateOrder(targetModel).subscribe({
          next: (res) => this.getAllDocumentInWorkSpace("", null),
          error: (err) => console.error("Lỗi cập nhật order:", err),
        });
      });

    this.getAllDocumentInWorkSpace("", null, true);
  }
  getDocumentNoPaging() {
    this.documentService
      .getAllDocumentInWorkSpace({
        no_paginate: true,
        workspace_id: this.workSpaceId,
        search: "",
      })
      .subscribe((res) => {
        this.chatbotService.listDocument.next(res);
      });
  }
  getDocumentForChatbot() {
    this.documentService
      .getAllDocumentInWorkSpace({
        for_chatbot: true,
        workspace_id: this.workSpaceId,
      })
      .subscribe((res) => {
        this.chatbotService.listDocument.next(res);
      });
  }
  convertFileFromSearch(file) {
    return {
      id: file.id || file.ID,
      origin: null,
      convert: null,
      status: DocumentStatus.STATUS_SUCCESS, // hoặc 0 nếu muốn hiển thị là 'Thất bại'
      status_display: "Thành công", // hoặc "Thất bại"
      created_at: new Date().toISOString().replace("T", " ").substring(0, 19),
      name: file.doc_id ? file.trich_yeu : file.title,
      is_convert: true,
      percentage: 100,
      workspace_id: null,
      types: TypeDocument.FromSearch,
      ID: file.id,
      title: file.title || null,
      dia_danh: null,
      ngay_ban_hanh: file.ngay_ban_hanh || null,
      ngay_co_hieu_luc: file.ngay_co_hieu_luc || null,
      ngay_dang_cong_bao: file.ngay_dang_cong_bao || null,
      ngay_het_hieu_luc: file.ngay_het_hieu_luc || null,
      ngay_het_hieu_luc_mot_phan: file.ngay_het_hieu_luc_mot_phan || null,
      so_hieu: file.so_hieu || null,
      toan_van: file.toan_van || null,
      trich_yeu: file.trich_yeu || null,
      tinh_trang_hieu_luc: file.tinh_trang_hieu_luc || null,
      co_quan_ban_hanh: file.co_quan_ban_hanh || null,
      nguoi_ky: file.nguoi_ky || null,
      loai_van_ban: file.loai_van_ban || null,
      es_id: null,
      doc_id: file.doc_id || null,
      term_id: file.term_id || null,
      document_data: file.document_data,
    };
  }
  addDocumentInWorkSpace(event) {
    this.isUploading = true;
    const input = event.target as HTMLInputElement;
    if (!input.files) return;

    const files: FileList = input.files;
    const allowedExtensions = ["pdf", "docx", "doc"];
    const maxFileSizeMB = JSON.parse(
      localStorage.getItem("package")
    ).max_file_size_per_upload; // Giới hạn dung lượng file

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const fileSizeMB = file.size / (1024 * 1024); // Đổi đơn vị MB
      const fileExtension = file.name.split(".").pop().toLowerCase();

      if (fileSizeMB > maxFileSizeMB / 1000) {
        this.toastService.error(
          `Tài liệu ${file.name} quá lớn (> ${maxFileSizeMB / 1000}MB) ❌`,
          "Lỗi",
          {
            toastClass: "toast ngx-toastr",
            positionClass: "toast-top-right",
            closeButton: true,
          }
        );
        continue; // Bỏ qua file này, tiếp tục với file khác
      }

      if (!allowedExtensions.includes(fileExtension)) {
        this.toastService.error(
          `Tài liệu ${file.name} không đúng định dạng PDF,DOC hoặc DOCX ❌`,
          "Sai định dạng 🚫",
          {
            toastClass: "toast ngx-toastr",
            positionClass: "toast-top-right",
            closeButton: true,
          }
        );
        continue; // Bỏ qua file này, tiếp tục với file khác
      }

      // Nếu hợp lệ, thêm vào formData và gửi lên server
      const formData = new FormData();
      formData.append("origin", file);
      formData.append("workspace_id", this.workSpaceId);

      this.documentService.addDocument(formData)
        .pipe(finalize(() => { this.isUploading = false }))
        .subscribe(() => {
          formData.delete("origin");
          formData.delete("workspace_id");
          setTimeout(() => {
            this.getAllDocumentInWorkSpace("", null);
          }, 1000);
        }
      );
    }

    // Reset giá trị input file để có thể chọn lại file trùng nhau
    event.target.value = "";
  }
  getAllDocumentInWorkSpace(search, sort_field, showLoading = false) {
    const params: any = {
      page: this.page,
      workspace_id: this.workSpaceId,
      search: search,
      page_size: 12,
    };

    if (sort_field) {
      params.sort_field = sort_field;
    }
    this.isFetchingDocuments = showLoading;
    this.documentService.getAllDocumentInWorkSpace(params)
    .pipe(finalize(() => { this.isFetchingDocuments = false; }))
    .subscribe({
      next: (res) => {
        this.listDocument = res.results;
        this.listDocumentFilter = res.results;
        this.total = res.count;
      },
      error: (err) => {
        console.error("Lỗi khi lấy danh sách tài liệu:", err);
      }
    });
    this.getDocumentForChatbot();
  }
  toggleSidebar() {
    this.isSidebarExpanded = !this.isSidebarExpanded;
    this.documentService.contentValue.next(
      this.isSidebarExpanded ? ShowContent.Search : ShowContent.Document
    );
  }
  toggleSearch() {
    this.showSearch = !this.showSearch;
    if (!this.showSearch) {
      this.searchText = "";
      this.listDieuKhoan = [...this.listDieuKhoanOriginal];
    }
  }
  onSearchChange() {
    const text = this.searchText?.toLowerCase().trim();

    if (!text) {
      this.listDieuKhoan = [...this.listDieuKhoanOriginal];
      return;
    }

    this.listDieuKhoan = this.listDieuKhoanOriginal.filter((item: any) => {
      const title = (item.title || "").toLowerCase();
      const position = (item.position || "").toLowerCase();
      // const clauseText = (item.clause_text || "").toLowerCase();
      return (
        title.includes(text) ||
        position.includes(text) 
        // || clauseText.includes(text)
      );
    });
  }
  onRightClickFile(event: MouseEvent, item: any): void {
    event.preventDefault();
    this.contextMenuClause = false;
    this.contextMenuVisible = true;
    this.contextMenuPosition = { x: event.clientX, y: event.clientY };
    this.contextMenuItem = item;
  }
  onRightClickClause(event: MouseEvent, item: any): void {
    event.preventDefault();
    this.contextMenuVisible = false;
    this.contextMenuClause = true;
    this.contextMenuPosition = { x: event.clientX, y: event.clientY };
    this.contextClauseItem = item;
  }
  closeContextMenu(): void {
    this.contextMenuVisible = false;
    this.contextMenuClause = false;
  }
  @HostListener("document:click")
  onDocumentClick(): void {
    this.closeContextMenu();
  }
  renameDocument(item: any) {
    this.closeContextMenu();
    this.selectedFile = item;
    this.openEditModal(item);
    // Mở dialog đổi tên
  }
  openEditModal(document: any) {
    this.selectedFile = { ...document };
    // Lấy tên file và tách phần mở rộng
    const fileName = this.selectedFile.name;
    const lastDotIndex = fileName.lastIndexOf(".");

    if (lastDotIndex !== -1) {
      this.fileExtension = fileName.substring(lastDotIndex); // Lấy phần mở rộng
      // this.selectedDocument.file.name = fileName.substring(0, lastDotIndex); // Lấy phần tên gốc
      this.fileName.patchValue(fileName.substring(0, lastDotIndex));
    } else {
      this.fileExtension = ""; // Trường hợp không có dấu chấm
      // this.selectedDocument.file.name = fileName;
      this.fileName.patchValue(fileName);
    }

    this.modalService.open(this.editDocumentNameModal, {
      centered: true,
      size: "lg",
    });
  }
  updateName(modal: any) {
    const newFileName = this.fileName.value + this.fileExtension;
    if (!this.fileName.value.trim()) {
      this.toastService.warning(
        "Tên tài liệu không được để trống!",
        "Cảnh báo",
        {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        }
      );
      return;
    }

    this.documentService
      .updateDocumentName(this.selectedFile.id, newFileName)
      .subscribe({
        next: () => {
          this.toastService.success(
            "Cập nhật tên tài liệu thành công!",
            "Thành công",
            {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            }
          );
          this.getAllDocumentInWorkSpace("", null);
          modal.close();
        },
        error: () => {
          this.toastService.error("Có lỗi xảy ra khi cập nhật!", "Lỗi", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
        },
      });
  }
  deleteDocument(item: any) {
    this.closeContextMenu();
    Swal.fire({
      title: "Bạn có chắc chắn muốn xóa?",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",
    }).then((result) => {
      if (result.isConfirmed) {
        this.isFetchingDocuments = true;
        this.documentService.deleteDocument(item.id).subscribe((res) => {
          this.toastService.success("Đã xoá tài liệu", "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          setTimeout(() => {
            this.getAllDocumentInWorkSpace("", null);
          }, 1000);
        });
      }
    });
  }
  getOfficeIcon(document) {
    if (document?.types == TypeDocument.CSDL) {
      return "assets/images/icons/file-search.svg";
    } else if (document?.types == TypeDocument.FromSearch) {
      return "assets/images/icons/search.svg";
    } else {
      const fileName = document?.name;
      let fileExtension = null;

      if (typeof fileName === "string" && fileName.lastIndexOf(".") !== -1) {
        fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
      }

      if (fileExtension == "docx") return "assets/images/icons/docx.svg";
      if (fileExtension == "pdf") return "assets/images/icons/pdf.svg";
      if (fileExtension == "doc") return "assets/images/icons/doc.svg";
      return "assets/images/icons/docx.svg";
    }
  }
  rerunFile(item) {
    this.documentService.rerunDocumemt(item.id).subscribe((res) => {
      setTimeout(() => {
        this.getAllDocumentInWorkSpace("", null);
        // const item = this.listDocumentFilter.findIndex(
        //   (item) => item.id == item.id
        // );
        // item.status = DocumentStatus.STATUS_PROCESSING;
      }, 1000);
    });
  }
  viewDocument(file) {
    this.selectedFile = file;
    this.getAllSoHieuVBCC();
    if (file.status == DocumentStatus.STATUS_SUCCESS) {
      this.documentService.setBehavior(ShowContent.Document);
      this.chatbotService.textBoiDen.next(null);
      this.viewDetailFile.fileInfor.next(file);
      this.isSidebarExpanded = false;
      this.router.navigate([], {
        queryParams: {
          fileId: file.id,
          es_id: file.es_id,
          tabs: "toanvan",
          luocdo: null, // xoá đi khi xem tài liệu khác, tránh gọi đến lược đồ es
          type:
            file.types == TypeDocument.CSDL
              ? "search"
              : file.types == TypeDocument.FromSearch
              ? "searching"
              : "upload",
          time: new Date().getTime(),
          fileName: file.name,
          save: false,
        },
        queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
      });
      this.viewDetailFile.clauseId.next(null); // Để xoá id điều khoản đang xem
      this.getClauseOfFile(file, true);
    } else {
      return;
    }
  }
  // private fillClauseTextFromATag(html: string): void {
  //   const doc = new DOMParser().parseFromString(html || "", "text/html");

  //   const normalize = (t: string) =>
  //     (t || "")
  //       .replace(/\u00A0/g, " ") // &nbsp;
  //       .replace(/\s+/g, " ") // gộp khoảng trắng
  //       .trim();

  //   for (const it of this.listDieuKhoan) {
  //     // 1) Lấy MẢNG <a> có id kết thúc bằng it.clause_id
  //     const anchors = Array.from(
  //       doc.querySelectorAll<HTMLAnchorElement>("a[id]")
  //     ).filter((a) => a.id?.endsWith(it.clause_id));
  //     const pieces: string[] = [];
  //     anchors.map((a) => {
  //       const bTags = Array.from(a.querySelectorAll("b"));
  //       if (bTags.length === 0) {
  //         const txt = normalize(a.textContent || "");
  //         if (txt) pieces.push(txt);
  //       } else {
  //         for (const b of bTags) {
  //           const txt = normalize(b.textContent || "");
  //           if (txt) pieces.push(txt);
  //         }
  //       }
  //     });
  //     // 3) Cộng (nối) toàn bộ text lại
  //     it.clause_text = pieces.join(" ").trim();
  //   }
  // }

  parsePosition(pos: string) {
    const parts = pos.split('/').map(p => p.trim());

    let dieu = 0, muc = 0, chuong = 0;

    parts.forEach(p => {
      if (p.startsWith("Điều")) {
        dieu = parseInt(p.replace(/\D+/g, ""));
      }
      if (p.startsWith("Mục")) {
        muc = parseInt(p.replace(/\D+/g, ""));
      }
      if (p.startsWith("Chương")) {
        chuong = parseInt(p.replace(/\D+/g, ""));
      }
    });

    return { dieu, muc, chuong };
  }

  getClauseOfFile(file, showLoading: boolean = false) {
    // console.log("file", file);
    this.isFetchingClauseOfFile = showLoading;
    if (file.types == TypeDocument.CSDL || file.types == TypeDocument.UPLOAD) {
      this.documentService.getDanhSachDieuKhoan(file.id)
        .pipe(finalize(() => this.isFetchingClauseOfFile = false))
        .subscribe({
          next: (res) => {
            res.clauses.sort((a, b) => {
              const A = this.parsePosition(a.position);
              const B = this.parsePosition(b.position);

              if (A.dieu !== B.dieu) return A.dieu - B.dieu;
              if (A.muc !== B.muc) return A.muc - B.muc;
              return A.chuong - B.chuong;
            });

            this.listDieuKhoan = res.clauses;
            // this.fillClauseTextFromATag(this.htmlString);
            this.listDieuKhoanOriginal = [...this.listDieuKhoan];
            this.onSearchChange();

            this.viewDetailFile.listDieuKhoanSearch.next(res.clauses);

            if (res.condition) {
              try {
                this.listConditionRaSoat = JSON.parse(res.condition);
              } catch {
                this.listConditionRaSoat = JSON.parse(res.condition.replace(/'/g, '"'));
              }

              this.countCoundition = Object.values(this.listConditionRaSoat ?? {}).reduce<number>((sum, val) => {
                if (Array.isArray(val)) return sum + val.length;
                if (typeof val === "string") return sum + val.split(",").map(s=>s.trim()).filter(Boolean).length;
                return sum;
              }, 0);
              this.countCoundition = this.countConditions(this.listConditionRaSoat);
            } else {
              this.listConditionRaSoat = {};
              this.countCoundition = 0;
            }
          },
          error: (error) => {
            this.viewDetailFile.listDieuKhoanSearch.next([]);
            console.error("Lỗi khi lấy điều khoản:", error);
          }
        }
      );
    } else {
      if (file.doc_id) {
        this.documentService.getDanhSachDieuKhoanSearch(file.doc_id)
          .pipe(finalize(() => this.isFetchingClauseOfFile = false))
          .subscribe({
            next: (res) => {
              this.listDieuKhoanSearch = res;
              this.viewDetailFile.listDieuKhoanSearch.next(res);
            },
            error: (error) => {
              this.listDieuKhoanSearch = [];
              this.viewDetailFile.listDieuKhoanSearch.next([]);
            }
          }
        );
      } else {
        this.documentService
          .getDanhSachDieuKhoanSearch(file.id || file.ID)
          .pipe(finalize(() => this.isFetchingClauseOfFile = false))
          .subscribe({
            next: (res) => {
              this.listDieuKhoanSearch = res;
              this.viewDetailFile.listDieuKhoanSearch.next(res);
            },
            error: (error) => {
              this.listDieuKhoanSearch = [];
              this.viewDetailFile.listDieuKhoanSearch.next([]);
            }
          });
      }
    }
  }

  resetSearch() {
    this.showSearch = false;
    this.searchText = "";
  }

  onPanelChange(event: NgbPanelChangeEvent, file) {
    this.resetSearch();
    const type = file.types === TypeDocument.CSDL ? "search" : "upload";
    // this.viewDetailFile.getDetailFile(file.id, type).subscribe((result) => {
    //   this.safeHtml = this.sanitizer.bypassSecurityTrustHtml(
    //     `${result?.toan_van ?? ""}`
    //   );
    //   this.htmlString =
    //     this.sanitizer.sanitize(SecurityContext.HTML, this.safeHtml) ?? "";
    // });
    this.documentService.checkEmbeddingStatus(file.id).subscribe(
      (res) => {
        // // console.log("file_hehe", res);
      },
      (error) => {
        // // console.log("file_hehe", error);
      }
    );
    // Cho phép mở
    if (event.nextState) {
      // mở panel
      this.listDieuKhoan = []; // Reset danh sách điều khoản

      this.viewDocument(file);
      this.activeIds = [event.panelId]; // Chỉ mở panel hiện tại
      this.fileTermFormSearch = [];
    } else {
      this.activeIds = []; // Đóng nếu người dùng click lại để đóng
    }
  }
  onPanelChangeTerm(event: NgbPanelChangeEvent, file) {
    // Cho phép mở
    if (event.nextState) {
      // mở panel
      this.listDieuKhoan = []; // Reset danh sách điều khoản
      this.viewDocument(file);
      this.activeIds = [event.panelId]; // Chỉ mở panel hiện tại
    } else {
      this.activeIds = []; // Đóng nếu người dùng click lại để đóng
    }
  }

  private ensureConditionShape(raw: any): RaSoatConditions {
    return {
      so_hieu: Array.isArray(raw?.so_hieu) ? raw.so_hieu : [],
      van_ban_dich: Array.isArray(raw?.van_ban_dich) ? raw.van_ban_dich : [],
      van_ban_nguon: Array.isArray(raw?.van_ban_nguon) ? raw.van_ban_nguon : [],
      loai_van_ban: Array.isArray(raw?.loai_van_ban) ? raw.loai_van_ban : [],
      tinh_trang_hieu_luc: Array.isArray(raw?.tinh_trang_hieu_luc) ? raw.tinh_trang_hieu_luc : [],
      ngay_ban_hanh_start: raw?.ngay_ban_hanh_start ?? '',
      ngay_ban_hanh_end: raw?.ngay_ban_hanh_end ?? '',
      ngay_co_hieu_luc_start: raw?.ngay_co_hieu_luc_start ?? '',
      ngay_co_hieu_luc_end: raw?.ngay_co_hieu_luc_end ?? '',
    };
  }
  
  addDieuKhoan(file) {
    this.modalService.open(this.clauseModal, {
      centered: true,
      size: "lg",
    });
    this.title = "Thêm điều khoản";
    this.type = FormType.Create;
    this.row = file;
  }
updateClause(clause) {
  this.isLoading = true;

  const fileId = this.route.snapshot.queryParamMap.get('fileId');

  this.documentService
    .getDanhSachDieuKhoanByClauseID(fileId, clause.clause_id)
    .subscribe({
      next: (res) => {

        // console.log("res", res.clauses);

        // Gán dữ liệu điều khoản vào form/state
        this.row = res.clauses;

        // Sau khi có dữ liệu → mới mở modal
        this.closeContextMenu();
        this.modalService.open(this.clauseModal, {
          centered: true,
          size: "lg",
        });

        this.title = "Cập nhật điều khoản";
        this.type = FormType.Update;

        this.isLoading = false;
      },
      error: (err) => {
        console.error("Lỗi khi lấy chi tiết điều khoản:", err);
        this.isLoading = false;
      }
    });
}

  deleteClasue(clause) {
    this.closeContextMenu();
    Swal.fire({
      title: "Bạn có chắc chắn muốn xóa?",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",

      preConfirm: async () => {
        return this.documentService.deleteClause(clause.id).subscribe(
          (res) => {
            this.toastService.success("Đã xoá điều khoản", "Thành công", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
            this.getClauseOfFile(this.selectedFile);
          },
          (err) => {
            this.toastService.error(
              "Có lỗi xảy ra khi xoá điều khoản!",
              "Lỗi",
              {
                closeButton: true,
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
              }
            );
          }
        );
      },

      allowOutsideClick: () => {
        return !Swal.isLoading();
      },
    });
  }

  raSoatDieuKhoan(clause) {
    const formData = new FormData();
    formData.append("ids", clause.id);
    formData.append(
      "list_indentifier_document",
      JSON.stringify(this.listConditionRaSoat)
    );
    this.detailClauseSerive.textSearch.next(null);

    const callApi = () => {
      this.documentService
        .raSoatDieuKhoan(this.selectedFile.id, formData)
        .subscribe(
          (res) => {
            setTimeout(() => {
              this.getClauseOfFile(this.selectedFile);
            }, 300);
          },
          (error) => {
            this.toastService.error(
              "Có lỗi xảy ra khi rà soát điều khoản!",
              "Lỗi",
              {
                closeButton: true,
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
              }
            );
          }
        );
    };

    if (clause.status === DocumentStatus.STATUS_SUCCESS) {
      Swal.fire({
        title: "Điều khoản đã được rà soát thành công",
        text: "Bạn có chắc chắn muốn rà soát lại không?",
        icon: "question",
        showCancelButton: true,
        confirmButtonText: "Có, rà soát lại",
        cancelButtonText: "Không",
        reverseButtons: true,
        confirmButtonColor: "#008fd3",
        cancelButtonColor: "#EEE",
        customClass: {
          confirmButton: "swal-confirm",
          cancelButton: "swal-cancel",
        },
      }).then((result) => {
        if (result.isConfirmed) {
          formData.append("force", "true");
          callApi();
        }
      });
    } else {
      callApi();
    }
  }
  // handleAdd(event: any) {
  //   const lastIndex = this.valuesVanBanDich.length - 1;
  //   if (lastIndex >= 0) {
  //     let val = this.valuesVanBanDich[lastIndex];
  //     val = val.replace(/,/g, " ");
  //     this.valuesVanBanDich[lastIndex] = val.trim();
  //   }
  // }
  raSoatAll() {
    const formData = new FormData();
    const ids = this.listDieuKhoan.map((item) => item.id).join(",");
    formData.append("ids", ids); // Dạng ids=1,2,3,...
    formData.append(
      "list_indentifier_document",
      JSON.stringify(this.listConditionRaSoat)
    );
    
    // Always force re-process everything with latest conditions
    formData.append("force", "true");
    
    Swal.fire({
      title: "Rà soát tất cả",
      html: `
    <div class="d-flex align-items-center justify-content-center ${
      this.countCoundition === 0 ? "flex-row" : "flex-column"
    }">${
        this.countCoundition === 0
          ? `<p class="mb-0">&nbsp;Thực hiện rà soát toàn bộ điều khoản trong văn bản này&nbsp;</p>`
          : `Thực hiện rà soát với các văn bản có số hiệu:
             <h5 class="mb-0">&nbsp;${this.listConditionRaSoat.so_hieu.join(
               ", "
             )}&nbsp;</h5>`
      }
    </div>`,
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Xác nhận",
      cancelButtonText: "Huỷ",
      reverseButtons: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
    }).then((result) => {
      if (result.isConfirmed) {
        this.documentService
          .raSoatDieuKhoan(this.selectedFile.id, formData)
          .subscribe(
            (res) => {
              this.toastService.success(
                "Thực hiện thành công! Vui lòng đợi",
                "Thành công",
                {
                  closeButton: true,
                  positionClass: "toast-top-right",
                  toastClass: "toast ngx-toastr",
                }
              );
              setTimeout(() => {
                this.getClauseOfFile(this.selectedFile);
              }, 1000);
            },
            (error) => {
              this.toastService.error(
                "Có lỗi xảy ra khi rà soát điều khoản!",
                "Lỗi",
                {
                  closeButton: true,
                  positionClass: "toast-top-right",
                  toastClass: "toast ngx-toastr",
                }
              );
            }
          );
      }
    });
  }
  selectClause(clause) {
    this.detailClauseSerive.textSearch.next(null);

    if (clause.status == DocumentStatus.STATUS_SUCCESS) {
      this.detailClauseSerive.currentPage.next(1);
      this.documentService.setBehavior(ShowContent.Clause);
      this.documentService.clauseValue.next(clause);
      this.selectedClause = clause;
      this.compareChatbotService.clauseInfo1.next(clause);
      this.workSpace.isNotesCollapsed.next(true); //Đóng chatbot compare vào tránh lỗi
    } else {
      return;
    }
  }
  onPageChange(event) {
    this.page = event;
    this.getAllDocumentInWorkSpace("", null, true);
  }
  sortDocumet(event) {
    this.page = 1;
    this.sort_field = event;
    // this.checkTop = true;
    // this.isLoading = false;
    switch (event) {
      case "Tên":
        this.getAllDocumentInWorkSpace(this.searchDocument.value, "name");
        this.valueSort = "name";
        break;
      case "Thời gian tạo":
        this.getAllDocumentInWorkSpace(this.searchDocument.value, "created_at");
        this.valueSort = "created_at";

        break;
      case "Hiệu lực":
        this.getAllDocumentInWorkSpace(
          this.searchDocument.value,
          "tinh_trang_hieu_luc"
        );
        this.valueSort = "tinh_trang_hieu_luc";

        break;
    }
  }
  scrollToClauseInDocument(clause) {
    this.selectedClause = clause;
    this.viewDetailFile.clauseId2.next(clause.clause_id); // để scroll đến điều khoản trong toàn văn
  }
  downloadFile(file, type) {
    this.documentService.saveFile(file, type);
  }
  escapeHtml(str: any) {
    return str.replace(
      /[&<>"']/g,
      (s) =>
        ({
          "&": "&amp;",
          "<": "&lt;",
          ">": "&gt;",
          '"': "&quot;",
          "'": "&#39;",
        }[s]!)
    );
  }
  getFileReport(doc_id, isAll = true) {
    const current_user = JSON.parse(localStorage.getItem("current_User"));
    const params = {
      type: "cap_van_ban",
      user_id: current_user.id,
      is_all: isAll,
    };
    this.documentService.getFileReport(doc_id, params).subscribe(
      (res) => {},
      (error) => {}
    );
  }
  onScroll(event: any) {
    // const scrollElement = event.target;
    // const scrollTop = scrollElement.scrollTop;
    // const scrollHeight = scrollElement.scrollHeight;
    // const clientHeight = scrollElement.clientHeight;
    // // Check if scrolled to the bottom
    // if (
    //   scrollTop + clientHeight >= scrollHeight &&
    //   !this.isLoading &&
    //   this.checkTop
    // ) {
    //   this.isLoading = true;
    //   // Save the current scroll position
    //   const savedScrollTop = scrollElement.scrollTop;
    //   this.fetchData(this.searchDocument.value, this.valueSort)
    //     .then(() => {
    //       // Restore the scroll position after new data is loaded
    //       setTimeout(() => {
    //         scrollElement.scrollTop = savedScrollTop;
    //       }, 0);
    //     })
    //     .catch(() => {
    //       // Ensure the loading state is reset in case of an error
    //       this.isLoading = false;
    //     })
    //     .finally(() => {
    //       this.isLoading = false;
    //     });
    // }
  }
  fetchData(search, sort_field): Promise<void> {
    this.page++;
    return new Promise<void>((resolve, reject) => {
      const params: any = {
        page: this.page,
        workspace_id: this.workSpaceId,
        search: search,
      };

      if (sort_field) {
        params.sort_field = sort_field;
      }

      this.documentService
        .getAllDocumentInWorkSpace(params)
        .subscribe((res) => {
          if (res.next == null) {
            this.checkTop = false;
          }
          if (Array.isArray(res.results)) {
            this.listDocumentFilter.push(...res.results);
          }

          resolve();
        });
    });
  }
  public typeSort: string = "arrow-down-circle";
  public sort_field: string = "Tên";
  loaiSapXep() {
    this.page = 1;
    if (this.typeSort == "arrow-down-circle") {
      this.typeSort = "arrow-up-circle";
      switch (this.sort_field) {
        case "Tên":
          this.getAllDocumentInWorkSpace(this.searchDocument.value, "-name");
          this.valueSort = "-name";
          break;
        case "Thời gian tạo":
          this.getAllDocumentInWorkSpace(
            this.searchDocument.value,
            "-created_at"
          );
          this.valueSort = "-created_at";

          break;
        case "Hiệu lực":
          this.getAllDocumentInWorkSpace(
            this.searchDocument.value,
            "-tinh_trang_hieu_luc"
          );
          this.valueSort = "-tinh_trang_hieu_luc";

          break;
      }
    } else {
      this.typeSort = "arrow-down-circle";
      switch (this.sort_field) {
        case "Tên":
          this.getAllDocumentInWorkSpace(this.searchDocument.value, "name");
          this.valueSort = "name";
          break;
        case "Thời gian tạo":
          this.getAllDocumentInWorkSpace(
            this.searchDocument.value,
            "created_at"
          );
          this.valueSort = "created_at";

          break;
        case "Hiệu lực":
          this.getAllDocumentInWorkSpace(
            this.searchDocument.value,
            "tinh_trang_hieu_luc"
          );
          this.valueSort = "tinh_trang_hieu_luc";

          break;
      }
    }
  }
  modalOpen(modalSM, size: string) {
    this.modalService.open(modalSM, {
      centered: true,
      size: size,
    });
  }
  resetValueModalRaSoatCuThe() {
    this.valuesSoHieuCanRaSoat = [];
    this.valuesVanBanDich = [];
    this.valuesLoaiVanBan = [];
    this.valuesVanBanNguon = [];
    this.valuesTinhTrangHieuLuc = [];
  }

  // private parseYMD(s?: string): Date | null {
  //   if (!s) return null;
  //   const [y, m, d] = s.split('-').map(Number);
  //   return (y && m && d) ? new Date(y, m - 1, d) : null;
  // }
  // public valuesNgayBanHanhStart: string = '';
  // public valuesNgayBanHanhEnd: string   = '';
  // public valuesNgayCoHieuLucStart: string = '';
  // public valuesNgayCoHieuLucEnd: string   = '';
  showModalRaSoatCuThe() {
    this.resetValueModalRaSoatCuThe();
    // console.log("this.listConditionRaSoat", this.listConditionRaSoat);
    const cond = this.ensureConditionShape(this.listConditionRaSoat || {});
    this.valuesSoHieuCanRaSoat = cond.so_hieu;
    this.valuesVanBanDich      = cond.van_ban_dich;
    this.valuesVanBanNguon     = cond.van_ban_nguon;
    this.valuesLoaiVanBan      = cond.loai_van_ban;
    this.valuesTinhTrangHieuLuc= cond.tinh_trang_hieu_luc;

    this.valuesNgayBanHanhStart   = cond.ngay_ban_hanh_start || '';
    this.valuesNgayBanHanhEnd     = cond.ngay_ban_hanh_end || '';
    this.valuesNgayCoHieuLucStart = cond.ngay_co_hieu_luc_start || '';
    this.valuesNgayCoHieuLucEnd   = cond.ngay_co_hieu_luc_end || '';
    // [RSD][open] snapshot điều kiện hiện có
    // console.log('[RSD][showModal] listConditionRaSoat:', this.listConditionRaSoat);

    // console.log('[RSD][showModal] values:',
    //   {
    //     so_hieu: this.valuesSoHieuCanRaSoat,
    //     loai_van_ban: this.valuesLoaiVanBan,
    //     tinh_trang_hieu_luc: this.valuesTinhTrangHieuLuc,
    //   }
    // );

    // console.log('[RSD][showModal] day values before setDate:', {
    //   nbh_s: this.valuesNgayBanHanhStart,
    //   nbh_e: this.valuesNgayBanHanhEnd,
    //   chl_s: this.valuesNgayCoHieuLucStart,
    //   chl_e: this.valuesNgayCoHieuLucEnd,
    //   modelNBH: this.modelNgayBanHanh,
    //   modelCHL: this.modelNgayCoHieuLuc
    // });

    const nbh_s = cond.ngay_ban_hanh_start;
    const nbh_e = cond.ngay_ban_hanh_end;
    const chl_s = cond.ngay_co_hieu_luc_start;
    const chl_e = cond.ngay_co_hieu_luc_end;

    this.modelNgayBanHanh   = (nbh_s && nbh_e) ? [this.parseYMD(nbh_s), this.parseYMD(nbh_e)] : [];
    this.modelNgayCoHieuLuc = (chl_s && chl_e) ? [this.parseYMD(chl_s), this.parseYMD(chl_e)] : [];
    this.dateRangeNBHConfig  = {
      ...this.dateRangeOpts,
      defaultDate: this.asDefaultDate(this.modelNgayBanHanh as any)
    } as any;

    this.dateRangeNCHLConfig = {
      ...this.dateRangeOpts,
      defaultDate: this.asDefaultDate(this.modelNgayCoHieuLuc as any)
    } as any;

    // console.log('[RSD][showModal] defaultDate NBH:', this.dateRangeNBHConfig['defaultDate']);
    // console.log('[RSD][showModal] defaultDate NCHL:', this.dateRangeNCHLConfig['defaultDate']);

    this.modelNgayBanHanh   = (cond.ngay_ban_hanh_start && cond.ngay_ban_hanh_end)
      ? [cond.ngay_ban_hanh_start, cond.ngay_ban_hanh_end] as any
      : [];
    this.modelNgayCoHieuLuc = (cond.ngay_co_hieu_luc_start && cond.ngay_co_hieu_luc_end)
      ? [cond.ngay_co_hieu_luc_start, cond.ngay_co_hieu_luc_end] as any
      : [];

    // console.log('[RSD][showModal] day values before setDate:', {
    //   nbh_s: cond.ngay_ban_hanh_start,
    //   nbh_e: cond.ngay_ban_hanh_end,
    //   chl_s: cond.ngay_co_hieu_luc_start,
    //   chl_e: cond.ngay_co_hieu_luc_end,
    //   modelNBH: this.modelNgayBanHanh,
    //   modelCHL: this.modelNgayCoHieuLuc
    // });

    this.modalOpen(this.modalRaSoatCuThe, "lg");
    this.setDatesWhenPickersReady();
    setTimeout(() => {
      const fpNBH  = this.pickerNBH?.flatpickr as any;
      const fpNCHL = this.pickerNCHL?.flatpickr as any;

      if (fpNBH) {
        if (this.modelNgayBanHanh?.length === 2) {
          fpNBH.setDate(this.modelNgayBanHanh, true);
        } else {
          fpNBH.clear();
        }
        // console.log('[RSD][setDate] NBH selectedDates:', fpNBH?.selectedDates);
      }
      if (fpNCHL) {
        if (this.modelNgayCoHieuLuc?.length === 2) {
          fpNCHL.setDate(this.modelNgayCoHieuLuc, true);
        } else {
          fpNCHL.clear();
        }
        // console.log('[RSD][setDate] NCHL selectedDates:', fpNCHL?.selectedDates);
      }
    }, 0);
  }
  
  submitRaSoatCuThe() {
    const formData = new FormData();
    const newConditions = {
      so_hieu: this.valuesSoHieuCanRaSoat,
      van_ban_dich: this.valuesVanBanDich,
      van_ban_nguon: this.valuesVanBanNguon,
      loai_van_ban: this.valuesLoaiVanBan,
      tinh_trang_hieu_luc: this.valuesTinhTrangHieuLuc,
      ngay_ban_hanh_start:   this.valuesNgayBanHanhStart   || '',
      ngay_ban_hanh_end:     this.valuesNgayBanHanhEnd     || '',
      ngay_co_hieu_luc_start:this.valuesNgayCoHieuLucStart || '',
      ngay_co_hieu_luc_end:  this.valuesNgayCoHieuLucEnd   || '',
    };

    const normalized = this.ensureConditionShape(newConditions);
    formData.set("list_indentifier_document", JSON.stringify(normalized));

    this.documentService.raSoatDieuKhoan(this.selectedFile.id, formData).subscribe(
      (res) => {
        this.listConditionRaSoat = normalized;
        this.countCoundition = this.countConditions(this.listConditionRaSoat);

        this.toastService.success("Cập nhật điều kiện rà soát!", "Thành công", {
          closeButton: true, positionClass: "toast-top-right", toastClass: "toast ngx-toastr"
        });

        setTimeout(() => this.getClauseOfFile(this.selectedFile), 300);
        this.modalService.dismissAll();
      },
      (error) => {
        console.error('[RSD][submit][ERR]:', error);
        this.toastService.error(error.error, "Lỗi", {
          closeButton: true, positionClass: "toast-top-right", toastClass: "toast ngx-toastr"
        });
      }
    );
  }

  ngOnDestroy(): void {
    this.documentService.contentValue.next(ShowContent.Search);
    this._unSubAll.next(null);
    this._unSubAll.complete();
  }
}
