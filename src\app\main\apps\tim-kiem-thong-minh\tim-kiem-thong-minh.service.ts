import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Params } from "@angular/router";
import { InterceptorSkipHeader } from "@core/components/loading/loading.interceptor";
import { environment } from "environments/environment";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";

@Injectable({
  providedIn: "root",
})
export class TimKiemThongMinhService {
  constructor(private _http: HttpClient) {}
  searchDieuKhoan(params: {
    limit?: number;
    filter?: string;
    search?: string;
    listId?: any[];
    valueCoQuanBanHanh?: any[];
    valueCoQuanQuanly?: any[];
    valueLoaiVanBan?: any[];
    valueTrangThai?: any[];
    page?: number;
    search_norm?: boolean;
    search_only_legal_title?: boolean;
    ngay_ban_hanh?: string;
    ngay_co_hieu_luc?: string;
    must?: string;
    should?: string;
    not?: string;
    ten_van_ban?: string;
    so_hieu?: string;
    threshold?: number;
    sat_nhap_tinh?: boolean;
    loai_hinh_tai_lieu?: string;
    boMoi?: boolean;
    linh_vuc?: string[];
    sort?: string;
    order?: string;
  }): Observable<any> {
    const headers = new HttpHeaders({
      "Content-Type": "application/json",
    }).set(InterceptorSkipHeader, "");
    const [start, end] = params.ngay_ban_hanh
      ? params.ngay_ban_hanh.split("đến").map((date) => date.trim())
      : [null, null];
    const [startCoHieuLuc, endCoHieuLuc] = params.ngay_co_hieu_luc
      ? params.ngay_co_hieu_luc.split("đến").map((date) => date.trim())
      : [null, null];
    const body: any = {
      limit: params.limit,
      filter: params.filter,
      query: params.search,
      domain_list: (params.listId || []).join(","),
      loai_van_ban: (params.valueLoaiVanBan || []).join(","),
      co_quan_ban_hanh: (params.valueCoQuanBanHanh || []).join(","),
      don_vi: (params.valueCoQuanQuanly || []).join(","),
      tinh_trang_hieu_luc: (params.valueTrangThai || []).join(","),
      page: params.page,
      ngay_ban_hanh_start: start,
      ngay_ban_hanh_end: end,
      ngay_co_hieu_luc_start: startCoHieuLuc,
      ngay_co_hieu_luc_end: endCoHieuLuc,
      must: params.must,
      should: params.should,
      must_not: params.not,
      statistic: true,
      search_legal_term: params.search_norm,
      search_only_legal_title:params.search_only_legal_title,
      ten_van_ban: params.ten_van_ban,
      so_hieu: params.so_hieu,
      threshold: params.threshold,
      sat_nhap_tinh: params.sat_nhap_tinh,
      loai_hinh_tai_lieu: params.loai_hinh_tai_lieu,
      sat_nhap_bo: params.boMoi,
      linh_vuc: (params.linh_vuc || []).join(","),
      sort: params.sort,
      order: params.order,
    };

    return this._http
      .post<any>(
        `${environment.apiUrl}/legal_search/by_query`,
        JSON.stringify(body),
        { headers: headers }
      )
      .pipe(
        map((response) => {
          try {
            return typeof response === "string"
              ? JSON.parse(response)
              : response;
          } catch (error) {
            console.error("Error parsing response:", error);
            return response;
          }
        })
      );
  }
  fastSearchDieuKhoan(params: {
    limit?: number;
    filter?: string;
    search?: string;
    listId?: any[];
    valueCoQuanBanHanh?: any[];
    valueCoQuanQuanly?: any[];
    valueLoaiVanBan?: any[];
    valueTrangThai?: any[];
    page?: number;
    search_norm?: boolean;
    search_only_legal_title?: boolean;
    ngay_ban_hanh?: string;
    ngay_co_hieu_luc?: string;
    must?: string;
    should?: string;
    not?: string;
    ten_van_ban?: string;
    so_hieu?: string;
    threshold?: number;
    sat_nhap_tinh?: boolean;
    loai_hinh_tai_lieu?: string;
    boMoi?: boolean;
    linh_vuc?: string[];
    sort?: string;
    order?: string;
  }): Observable<any> {
    const headers = new HttpHeaders({
      "Content-Type": "application/json",
      [InterceptorSkipHeader]: "",
    }); // kh có k chạy được
    const [start, end] = params.ngay_ban_hanh
      ? params.ngay_ban_hanh.split("đến").map((date) => date.trim())
      : [null, null];
    const [startCoHieuLuc, endCoHieuLuc] = params.ngay_co_hieu_luc
      ? params.ngay_co_hieu_luc.split("đến").map((date) => date.trim())
      : [null, null];
    const body: any = {
      limit: params.limit,
      filter: params.filter,
      query: params.search,
      domain_list: (params.listId || []).join(","),
      loai_van_ban: (params.valueLoaiVanBan || []).join(","),
      co_quan_ban_hanh: (params.valueCoQuanBanHanh || []).join(","),
      don_vi: (params.valueCoQuanQuanly || []).join(","),
      tinh_trang_hieu_luc: (params.valueTrangThai || []).join(","),
      page: params.page,
      ngay_ban_hanh_start: start,
      ngay_ban_hanh_end: end,
      ngay_co_hieu_luc_start: startCoHieuLuc,
      ngay_co_hieu_luc_end: endCoHieuLuc,
      must: params.must,
      should: params.should,
      must_not: params.not,
      statistic: true,
      search_legal_term: params.search_norm,
      search_only_legal_title: params.search_only_legal_title,
      ten_van_ban: params.ten_van_ban,
      so_hieu: params.so_hieu,
      threshold: params.threshold,
      sat_nhap_tinh: params.sat_nhap_tinh,
      loai_hinh_tai_lieu: params.loai_hinh_tai_lieu,
      sat_nhap_bo: params.boMoi,
      linh_vuc: (params.linh_vuc || []).join(","),
    };
    if (params.sort) body["sort"] = params.sort;
    if (params.order) body["order"] = params.order;
    return this._http
      .post<any>(
        `${environment.apiUrl}/legal_search/by_query`,
        JSON.stringify(body),
        { headers: headers }
      )
      .pipe(
        map((response) => {
          try {
            return typeof response === "string"
              ? JSON.parse(response)
              : response;
          } catch (error) {
            console.error("Error parsing response:", error);
            return response;
          }
        })
      );
  }
  searchDieuKhoan2(
    limit: number,
    filter: string,
    search: string,
    listId: any,
    valueCoQuanBanHanh,
    valueCoQuanQuanly,
    valueLoaiVanBan,
    valueTrangThai,
    page,
    search_norm,
    ngay_ban_hanh,
    must: string,
    should: string,
    not: string,
    ten_van_ban,
    so_hieu,
    threshold,
    sat_nhap_tinh,
    loai_hinh_tai_lieu,
    page_size,
    boMoi,
    sort?,
    order?
  ): Observable<any> {
    const [start, end] = ngay_ban_hanh
      ? ngay_ban_hanh.split("đến").map((date) => date.trim())
      : [null, null];
    const body = {
      limit: limit,
      filter: filter,
      query: search,
      domain_list: listId.join(","),
      loai_van_ban: valueLoaiVanBan.join(","),
      co_quan_ban_hanh: valueCoQuanBanHanh.join(","),
      don_vi: valueCoQuanQuanly.join(","),
      tinh_trang_hieu_luc: valueTrangThai.join(","),
      page: page,
      ngay_ban_hanh_start: start,
      ngay_ban_hanh_end: end,
      must: must,
      should: should,
      must_not: not,
      statistic: true,
      search_legal_term: search_norm,
      ten_van_ban: ten_van_ban,
      so_hieu: so_hieu,
      threshold: threshold,
      sat_nhap_tinh: sat_nhap_tinh,
      loai_hinh_tai_lieu: loai_hinh_tai_lieu,
      page_size: page_size,
      sat_nhap_bo: boMoi,
    };
    // Chỉ thêm `sort` và `order` nếu chúng có giá trị

    if (sort) {
      body["sort"] = sort;
    }
    if (order) {
      body["order"] = order;
    }
    return this._http
      .post<any>(
        `${environment.apiUrl}/legal_search/by_query`,
        JSON.stringify(body) // Explicitly stringify the body
      )
      .pipe(
        map((response) => {
          // Parse the response if needed
          try {
            return typeof response === "string"
              ? JSON.parse(response)
              : response;
          } catch (error) {
            console.error("Error parsing response:", error);
            return response;
          }
        })
      );
  }
  exportFile(body): Observable<Blob> {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.post(`${environment.apiUrl}/export/normal`, body, {
      headers: headers,
      responseType: "blob",
    });
  }
  getTypeChuDePhapDien() {
    return this._http.get(`${environment.apiUrl}/utils/keywords/domains`);
  }
  getLoaiVanBan() {
    return this._http.get(`${environment.apiUrl}/utils/keywords/loai_van_ban`);
  }
  getCoQuanBanHanh() {
    return this._http.get(
      `${environment.apiUrl}/utils/keywords/co_quan_ban_hanh`
    );
  }
  getTrangThaiHieuLuc() {
    return this._http.get(
      `${environment.apiUrl}/utils/keywords/tinh_trang_hieu_luc`
    );
  }
  saveResult(body) {
    return this._http.post(
      `${environment.apiUrl}/legal_search/save_history-search`,
      body
    );
  }
  /** Lấy toàn bộ lịch sử tìm kiếm (GET) */
  getHistory(): Observable<any> {
    return this._http.get(
      `${environment.apiUrl}/legal_search/save_history-search`
    );
  }

  /** Xóa một lịch sử tìm kiếm theo ID (DELETE) */
  deleteHistory(historyId: number): Observable<any> {
    return this._http.delete(
      `${environment.apiUrl}/legal_search/save_history-search/${historyId}/`
    );
  }

  /** Tìm kiếm lịch sử theo từ khóa (SEARCH) */
  searchHistory(query: string): Observable<any> {
    return this._http.get(
      `${environment.apiUrl}/legal_search/save_history-search/search`,
      {
        params: { keyword: query },
      }
    );
  }
  getDetailFile(fileId) {
    const params: Params = {
      document_id: fileId,
    };
    return this._http.get<any>(`${environment.apiUrl}/legal_search/by_query`, {
      params,
    });
  }
  downloadFile(docId: number, fileType: string): Observable<Blob> {
    return this._http.get(
      `${environment.apiUrl}/document/download?doc_id=${docId}&type=${fileType}`,
      {
        responseType: "blob", // Để nhận file nhị phân
      }
    );
  }
  saveFile(docId: number, fileType: string): void {
    this.downloadFile(docId, fileType).subscribe(
      (blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `document.${fileType}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      },
      (error) => {
        console.error("Lỗi khi tải file:", error);
        alert("Tải file thất bại!");
      }
    );
  }

  suggestSearchPhrase(query: string): Observable<any> {
    const headers = new HttpHeaders({
      "Content-Type": "application/json",
    }).set(InterceptorSkipHeader, "");

    return this._http.get(`${environment.apiUrl}/legal_search/suggest`, {
      params: { suggest: query },
      headers: headers,
    });
  }
  getThongKeVanBan() {
    const headers = new HttpHeaders({
      "Content-Type": "application/json",
    }).set(InterceptorSkipHeader, "");
    const body = {};
    return this._http.post(
      `${environment.apiUrl}/dashboard/es-loai-van-ban-stats/`,
      body,
      {
        headers: headers,
      }
    );
  }
  getLinhVucTraCuu() {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.get<any>(
      `${environment.apiUrl}/dashboard/get-field-list/`,
      { headers: headers }
    );
  }
}
