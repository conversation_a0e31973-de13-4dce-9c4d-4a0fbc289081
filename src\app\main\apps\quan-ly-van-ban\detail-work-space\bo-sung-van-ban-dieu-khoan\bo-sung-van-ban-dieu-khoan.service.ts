import { HttpClient, HttpHeaders } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Params } from "@angular/router";
import { InterceptorSkipHeader } from "@core/components/loading/loading.interceptor";
import { environment } from "environments/environment";
import { BehaviorSubject, Observable } from "rxjs";
import { map } from "rxjs/operators";

@Injectable({
  providedIn: "root",
})
export class BoSungVanBanDieuKhoanService {
  public typeSearch: BehaviorSubject<boolean> = new BehaviorSubject(false);
  public typeAddFile: BehaviorSubject<string> = new BehaviorSubject(null);
  constructor(private _http: HttpClient) {}
  searchDieuKhoan(
    limit: number,
    filter: string,
    search: string,
    listId: any,
    valueCoQuanBanHanh,
    valueCoQuanQuanly,
    valueLoaiVanBan,
    valueTrangThai,
    page,
    search_norm,
    ngay_ban_hanh,
    must: string,
    should: string,
    not: string,
    sort?,
    order?
  ): Observable<any> {
    const headers = new HttpHeaders({
      "Content-Type": "application/json",
    }).set(InterceptorSkipHeader, "");
    const [start, end] = ngay_ban_hanh
      ? ngay_ban_hanh.split("đến").map((date) => date.trim())
      : [null, null];
    const body = {
      limit: limit,
      filter: filter,
      query: search,
      domain_list: listId.join(","),
      loai_van_ban: valueLoaiVanBan.join(","),
      co_quan_ban_hanh: valueCoQuanBanHanh.join(","),
      don_vi: valueCoQuanQuanly.join(","),
      tinh_trang_hieu_luc: valueTrangThai.join(","),
      page: page,
      ngay_ban_hanh_start: start,
      ngay_ban_hanh_end: end,
      must: must,
      should: should,
      must_not: not,
      statistic: true,
      search_legal_term: search_norm,
    };
    // Chỉ thêm `sort` và `order` nếu chúng có giá trị

    if (sort) {
      body["sort"] = sort;
    }
    if (order) {
      body["order"] = order;
    }
    return this._http
      .post<any>(
        `${environment.apiUrl}/legal_search/by_query`,
        JSON.stringify(body), // Explicitly stringify the body
        { headers: headers }
      )
      .pipe(
        map((response) => {
          // Parse the response if needed
          try {
            return typeof response === "string"
              ? JSON.parse(response)
              : response;
          } catch (error) {
            console.error("Error parsing response:", error);
            return response;
          }
        })
      );
  }
  exportFile(body): Observable<Blob> {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.post(`${environment.apiUrl}/export/normal`, body, {
      responseType: "blob",
      headers: headers
    });
  }
  getTypeChuDePhapDien() {
    return this._http.get(`${environment.apiUrl}/utils/keywords/domains`);
  }
  getLoaiVanBan() {
    return this._http.get(`${environment.apiUrl}/utils/keywords/loai_van_ban`);
  }
  getCoQuanBanHanh() {
    return this._http.get(
      `${environment.apiUrl}/utils/keywords/co_quan_ban_hanh`
    );
  }
  getTrangThaiHieuLuc() {
    return this._http.get(
      `${environment.apiUrl}/utils/keywords/tinh_trang_hieu_luc`
    );
  }
  saveResult(body) {
    return this._http.post(
      `${environment.apiUrl}/legal_search/save_history-search`,
      body
    );
  }
  /** Lấy toàn bộ lịch sử tìm kiếm (GET) */
  getHistory(): Observable<any> {
    return this._http.get(
      `${environment.apiUrl}/legal_search/save_history-search`
    );
  }

  /** Xóa một lịch sử tìm kiếm theo ID (DELETE) */
  deleteHistory(historyId: number): Observable<any> {
    return this._http.delete(
      `${environment.apiUrl}/legal_search/save_history-search/${historyId}/`
    );
  }

  /** Tìm kiếm lịch sử theo từ khóa (SEARCH) */
  searchHistory(query: string): Observable<any> {
    return this._http.get(
      `${environment.apiUrl}/legal_search/save_history-search/search`,
      {
        params: { keyword: query },
      }
    );
  }
  getDetailFile(fileId) {
    const params: Params = {
      document_id: fileId,
    };
    return this._http.get<any>(`${environment.apiUrl}/legal_search/by_query`, {
      params,
    });
  }
  downloadFile(docId: number, fileType: string): Observable<Blob> {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.get(
      `${environment.apiUrl}/document/download?doc_id=${docId}&type=${fileType}`,
      {
        responseType: "blob", // Để nhận file nhị phân
        headers: headers
      }
    );
  }
  saveFile(docId: number, fileType: string): void {
    this.downloadFile(docId, fileType).subscribe(
      (blob) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `document.${fileType}`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      },
      (error) => {
        console.error("Lỗi khi tải file:", error);
        alert("Tải file thất bại!");
      }
    );
  }
  addClause(idClause, body) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    return this._http.post<any>(
      `${environment.apiUrl}/ocr/law_clauses/${idClause}/add_related_clauses`,
      body,
      { headers }
    );
  }
  addRelateDocument(body, document_id) {
    const headers = new HttpHeaders().set(InterceptorSkipHeader, "");
    body.id = document_id;
    return this._http.post<any>(
      `${environment.apiUrl}/ocr/document-related`,
      body,
      { headers }
    );
  }
}
