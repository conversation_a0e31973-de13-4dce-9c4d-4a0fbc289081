import { DOCUMENT } from "@angular/common";
import {
  Compo<PERSON>,
  ElementRef,
  Inject,
  On<PERSON>estroy,
  OnInit,
  Renderer2,
} from "@angular/core";
import { Title } from "@angular/platform-browser";

import { TranslateService } from "@ngx-translate/core";
import * as Waves from "node-waves";
import { Subject, Subscription } from "rxjs";
import { BehaviorSubject, takeUntil, combineLatest, filter, map } from 'rxjs';
import { CoreMenuService } from "@core/components/core-menu/core-menu.service";
import { CoreSidebarService } from "@core/components/core-sidebar/core-sidebar.service";
import { CoreConfigService } from "@core/services/config.service";
import { ThemeService } from "@core/services/theme.service";
import { CoreLoadingScreenService } from "@core/services/loading-screen.service";
import { CoreTranslationService } from "@core/services/translation.service";

import { locale as menuEnglish } from "app/menu/i18n/en";
import { locale as menuVietnam } from "app/menu/i18n/vi";

import { Router, NavigationStart, NavigationEnd, NavigationCancel, NavigationError } from "@angular/router";
import { NetworkStatusService } from "@core/services/network-status.service";
import { NgSelectConfig } from "@ng-select/ng-select";
import { menu } from "app/menu/menu";
import { environment } from "environments/environment";
import flatpickr from "flatpickr";
import Default from "flatpickr/dist/l10n/default.js";
import { ToastrService } from "ngx-toastr";
import { AuthenticationService } from "./auth/service";
import { ChangeDataService } from "./auth/service/change-data.service";
import { WebSocketService } from "./auth/service/webSocket.service";
import { DocumentStatus } from "./models/DocumentStatus";
import { getLogoImage, getLogoFavicon } from "app/shared/image.helper";
import { LoadingService } from "app/shared/loading.service";
// var WebSocketClient = require("websocket").w3cwebsocket;

@Component({
  selector: "app-root",
  templateUrl: "./app.component.html",
  styleUrls: ["./app.component.scss"],
})
export class AppComponent implements OnInit, OnDestroy {
  coreConfig: any;
  menu: any;
  defaultLanguage: "vi"; // This language will be used as a fallback when a translation isn't found in the current language
  appLanguage: "vi"; // Set application default language i.e fr
  public client: any;
  public Vie = require("flatpickr/dist/l10n/vn").default.vn;
  private messageSubscription!: Subscription;
  // Private
  private _unsubscribeAll: Subject<any>;
  readonly logoSrc = getLogoImage();
  private minShowMs = 250;
  private maxShowMs = 800; 
  private loaderStartedAt = 0;
  private maxTimer?: any;
  isLoaderActive = false;
  private articleTransition$ = new BehaviorSubject<boolean>(false);
  private norm(u: string): string {
    try {
      return (u || '').split('#')[0].split('?')[0];
    } catch { return u || ''; }
  }
private tryCloseLoader() {
  const elapsed = performance.now() - this.loaderStartedAt;
  const remain = Math.max(this.minShowMs - elapsed, 0);
  setTimeout(() => {
    // chỉ tắt khi: gate đã đóng HOẶC không còn HTTP
    if (!this.loading.articleGate$.value && !this.loading.httpLoading$.value) {
      clearTimeout(this.maxTimer);
      this.isLoaderActive = false;
    }
  }, remain);
}
  /** Có phải trang chi tiết bài viết? (ví dụ: /pages/articles/:slug) */
  private isArticleUrl(u: string): boolean {
    const p = this.norm(u);
    return p.includes('/pages/articles');
  }

  /** Có phải Landing? (route gốc của app) */
  private isLandingUrl(u: string): boolean {
    const p = this.norm(u);
    return (
      p === '/' ||
      p === '' ||
      p === '/home' ||
      p === '/landing' ||
      p === '/pages/landing'
    );
  }

  // 👉 ADD: helper đảm bảo sidebar luôn bật nếu có CMS role đang active
  private hasActiveCms(): boolean {
    try {
      const raw = localStorage.getItem("cms_roles") || "[]";
      const rows = JSON.parse(raw);
      return (rows || []).some((r: any) => r?.is_active);
    } catch {
      return false;
    }
  }
  private forceShowSidebarIfCms() {
    if (this.hasActiveCms()) {
      this._coreConfigService.setConfig(
        { layout: { type: "vertical", menu: { hidden: false } } },
        { emitEvent: true }
      );
    }
  }
  // -----

  /**
   * Constructor
   *
   * @param {Title} _title
   * @param {Renderer2} _renderer
   * @param {ElementRef} _elementRef
   * @param {CoreConfigService} _coreConfigService
   * @param {CoreSidebarService} _coreSidebarService
   * @param {CoreLoadingScreenService} _coreLoadingScreenService
   * @param {CoreMenuService} _coreMenuService
   * @param {CoreTranslationService} _coreTranslationService
   * @param {TranslateService} _translateService
   */
  constructor(
    @Inject(DOCUMENT) private document: any,
    private _title: Title,
    private _renderer: Renderer2,
    private _elementRef: ElementRef,
    public _coreConfigService: CoreConfigService,
    private _coreSidebarService: CoreSidebarService,
    private _coreLoadingScreenService: CoreLoadingScreenService,
    private _coreMenuService: CoreMenuService,
    private _coreTranslationService: CoreTranslationService,
    private _translateService: TranslateService,
    private changeData: ChangeDataService,
    private toast: ToastrService,
    private ngSelectConfig: NgSelectConfig,
    private webSocketService: WebSocketService,
    private networkService: NetworkStatusService,
    private route: Router,
    private autheService: AuthenticationService,
    private _themeService: ThemeService,
    private loading: LoadingService,
  ) {
    // gloabl config locale ng2-flatpickr
    const Vie = require("flatpickr/dist/l10n/vn").default.vn;
    flatpickr.localize(Vie); // default locale is now Russian
    this._translateService.onLangChange.subscribe((event) => {
      if (event.lang == "vi") {
        flatpickr.localize(Vie);
        this.ngSelectConfig.notFoundText = "Không có dữ liệu";
      } else {
        flatpickr.localize(Default);
        this.ngSelectConfig.notFoundText = "No items found";
      }
    });

  let currentUrl = this.route.url || '/';

  this.route.events
    .pipe(filter(e =>
      e instanceof NavigationStart ||
      e instanceof NavigationEnd ||
      e instanceof NavigationCancel ||
      e instanceof NavigationError
    ))
    .subscribe(evt => {
      if (evt instanceof NavigationStart) {
        const nextUrl = evt.url || '/';
        const isLandingToArticle = this.isLandingUrl(currentUrl) && this.isArticleUrl(nextUrl);
        const isArticleToLanding = this.isArticleUrl(currentUrl) && this.isLandingUrl(nextUrl);
        const shouldGate = isLandingToArticle || isArticleToLanding;

        this.loading.articleGate$.next(shouldGate);
        if (shouldGate) {
          // bật ngay, set mốc thời gian + max timer
          this.loaderStartedAt = performance.now();
          this.isLoaderActive = true;
          clearTimeout(this.maxTimer);
          this.maxTimer = setTimeout(() => {
            this.isLoaderActive = false;
            this.loading.articleGate$.next(false);
          }, this.maxShowMs);
        }
        this.loading.navigating$.next(true);
      } else {
        // End/Cancel/Error → tắt navigating, và thử đóng theo minShow + HTTP
        this.loading.navigating$.next(false);
        this.tryCloseLoader();
      }

      if (evt instanceof NavigationEnd || evt instanceof NavigationCancel || evt instanceof NavigationError) {
        currentUrl = (evt as any).url || currentUrl;
      }
    });

  this.loading.httpLoading$.subscribe(() => this.tryCloseLoader());

    // Get the application main menu
    this.menu = menu;

    // Register the menu to the menu service
    this._coreMenuService.register("main", this.menu);

    // Set the main menu as our current menu
    this._coreMenuService.setCurrentMenu("main");

    window.addEventListener("cms_roles_updated", () => {
      this.forceShowSidebarIfCms();
    });

    this.route.events
      .pipe(filter((e) => e instanceof NavigationEnd))
      .subscribe(() => {
        this.forceShowSidebarIfCms();
        setTimeout(() => this.forceShowSidebarIfCms(), 0);
        setTimeout(() => this.forceShowSidebarIfCms(), 100);
      });

    // Add languages to the translation service
    this._translateService.addLangs(["en", "vi"]);

    // This language will be used as a fallback when a translation isn't found in the current language
    this._translateService.setDefaultLang("vi");

    // Set the translations for the menu
    this._coreTranslationService.translate(menuEnglish, menuVietnam);

    // Set the private defaults
    this._unsubscribeAll = new Subject();
    this.networkService.online$.subscribe((status) => {
      if (status) {
        // this.toast.info("Đã có kết nối lại", "Vui lòng sử dụng lại dịch vụ!", {
        //   closeButton: true,
        //   positionClass: "toast-top-right",
        //   toastClass: "toast ngx-toastr",
        // });
      } else {
        this.toast.error(
          "Vui lòng kiểm tra lại hệ thống mạng của bạn",
          "Mất kết nối ",
          {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          }
        );
      }
    });
    const token = localStorage.getItem("token");
    if (token) {
      this.webSocketService.connect(
        `${environment.apiSocket}/notifications?token=${token}`
      );
      this.messageSubscription = this.webSocketService.messageSubject
        .pipe(takeUntil(this._unsubscribeAll))
        .subscribe((message) => {
          var dataMsg = message?.data; // Because message structure is {data: {data, event}, event}
          this.changeData.changeData.next(true);
          if (dataMsg) {
            if (
              dataMsg.type !== "compare_notify" &&
              dataMsg.type !== "campaign_notify" &&
              dataMsg.event !== "BDCDVB"
            ) {
              if (!(dataMsg.data.status == DocumentStatus.STATUS_FAILED)) {
                const isSpecial =
                  dataMsg.event === "CDVB" ||
                  dataMsg.event === "BDCDVB" ||
                  dataMsg.event === "IE_DONE" ||
                  dataMsg.event === "TQND";
                if (isSpecial) {
                  this.toast.success(
                    `Quá trình ${message.event} tài liệu ${dataMsg.data.name} thành công`,
                    dataMsg.data.status_display || "Thành công",
                    {
                      closeButton: true,
                      positionClass: "toast-top-right",
                      toastClass: `ngx-toastr toast ${
                        isSpecial ? "toast-blue-icon" : ""
                      }`,
                      titleClass: `toast-title ${
                        isSpecial ? "toast-title-blue" : ""
                      }`,
                    }
                  );
                } else {
                  this.toast.success(
                    `Quá trình ${message.event} tài liệu ${dataMsg.data.name} thành công`,
                    dataMsg.data.status_display || "Thành công",
                    {
                      closeButton: true,
                      positionClass: "toast-top-right",
                      toastClass: `ngx-toastr toast`,
                      titleClass: `toast-title`,
                    }
                  );
                }
              } else if (
                dataMsg.data.status == DocumentStatus.STATUS_PROCESSING ||
                dataMsg.data.status == DocumentStatus.STATUS_OCR ||
                dataMsg.data.status == DocumentStatus.STATUS_IE ||
                dataMsg.data.status == DocumentStatus.STATUS_CONFLICT
              ) {
                this.toast.info(
                  `Đang xử lý ${message.event} tài liệu ${dataMsg.data.name}`,
                  dataMsg.data.status_display,
                  {
                    closeButton: true,
                    positionClass: "toast-top-right",
                    toastClass: "toast ngx-toastr",
                  }
                );
              } else {
                // // console.log("dataMsgeror", dataMsg);

                this.toast.error(
                  `Quá trình ${message.event} tài liệu ${dataMsg.data.name} thất bại`,
                  dataMsg.data.status_display,
                  {
                    closeButton: true,
                    positionClass: "toast-top-right",
                    toastClass: "toast ngx-toastr",
                  }
                );
              }
              //this.changeData.changeData.next(true);
            }
          }
        });
    }
    // const role = this.autheService.currentUserValue.role;
    // if (role == Role.Super_admin) {
    //   this.route.navigate(["/super-admin"]);
    // } else {
    //   this.route.navigate(["/"]);
    // }
  }

  /**
   * On init
   */
  ngOnInit(): void {
    // Init wave effect (Ripple effect)
    Waves.init();

    // Set app logo image at runtime from localStorage (fallback handled in helper)
    this._coreConfigService.setConfig(
      { app: { appLogoImage: getLogoImage() } },
      { emitEvent: true }
    );

    // Set favicon based on current logo at startup
    const faviconEl = this.document.querySelector(
      'link[rel*="icon"]'
    ) as HTMLLinkElement;
    if (faviconEl) {
      this._renderer.setAttribute(faviconEl, "href", getLogoFavicon());
    }

    // Subscribe to config changes
    this._coreConfigService.config
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((config) => {
        this.coreConfig = config;

        // Keep favicon in sync with the current app logo
        const favicon = this.document.querySelector(
          'link[rel*="icon"]'
        ) as HTMLLinkElement;
        if (favicon) {
          this._renderer.setAttribute(favicon, "href", getLogoFavicon());
        }

        // this.coreConfig.layout.footer.hidden = true;
        // this.coreConfig.layout.menu.hidden = true;
        // Set application default language.

        // Change application language? Read the ngxTranslate Fix

        // ? Use app-config.ts file to set default language
        const appLanguage = this.coreConfig.app.appLanguage || "vi";
        this._translateService.use(appLanguage);

        // ? OR
        // ? User the current browser lang if available, if undefined use 'en'
        // const browserLang = this._translateService.getBrowserLang();
        // this._translateService.use(browserLang.match(/en|fr|de|pt/) ? browserLang : 'en');

        /**
         * ! Fix : ngxTranslate
         * ----------------------------------------------------------------------------------------------------
         */

        /**
         *
         * Using different language than the default ('en') one i.e French?
         * In this case, you may find the issue where application is not properly translated when your app is initialized.
         *
         * It's due to ngxTranslate module and below is a fix for that.
         * Eventually we will move to the multi language implementation over to the Angular's core language service.
         *
         **/

        // Set the default language to 'en' and then back to 'fr'.

        setTimeout(() => {
          this._translateService.setDefaultLang("vi");
          this._translateService.setDefaultLang(appLanguage);
        });

        /**
         * !Fix: ngxTranslate
         * ----------------------------------------------------------------------------------------------------
         */

        // Layout
        //--------

        // Remove default classes first
        this._elementRef.nativeElement.classList.remove(
          "vertical-layout",
          "vertical-menu-modern",
          "horizontal-layout",
          "horizontal-menu"
        );
        // Add class based on config options
        if (this.coreConfig.layout.type === "vertical") {
          this._elementRef.nativeElement.classList.add(
            "vertical-layout",
            "vertical-menu-modern"
          );
        } else if (this.coreConfig.layout.type === "horizontal") {
          this._elementRef.nativeElement.classList.add(
            "horizontal-layout",
            "horizontal-menu"
          );
        }

        // Navbar
        //--------

        // Remove default classes first
        this._elementRef.nativeElement.classList.remove(
          "navbar-floating",
          "navbar-static",
          "navbar-sticky",
          "navbar-hidden"
        );

        // Add class based on config options
        if (this.coreConfig.layout.navbar.type === "navbar-static-top") {
          this._elementRef.nativeElement.classList.add("navbar-static");
        } else if (this.coreConfig.layout.navbar.type === "fixed-top") {
          this._elementRef.nativeElement.classList.add("navbar-sticky");
        } else if (this.coreConfig.layout.navbar.type === "floating-nav") {
          this._elementRef.nativeElement.classList.add("navbar-floating");
        } else {
          this._elementRef.nativeElement.classList.add("navbar-hidden");
        }

        // Footer
        //--------

        // Remove default classes first
        this._elementRef.nativeElement.classList.remove(
          "footer-fixed",
          "footer-static",
          "footer-hidden"
        );

        // Add class based on config options
        if (this.coreConfig.layout.footer.type === "footer-sticky") {
          this._elementRef.nativeElement.classList.add("footer-fixed");
        } else if (this.coreConfig.layout.footer.type === "footer-static") {
          this._elementRef.nativeElement.classList.add("footer-static");
        } else {
          this._elementRef.nativeElement.classList.add("footer-hidden");
        }

        // Blank layout
        if (
          this.coreConfig.layout.menu.hidden &&
          this.coreConfig.layout.navbar.hidden &&
          this.coreConfig.layout.footer.hidden
        ) {
          this._elementRef.nativeElement.classList.add("blank-page");
          // ! Fix: Transition issue while coming from blank page
          this._renderer.setAttribute(
            this._elementRef.nativeElement.getElementsByClassName(
              "app-content"
            )[0],
            "style",
            "transition:none"
          );
        } else {
          this._elementRef.nativeElement.classList.remove("blank-page");
          // ! Fix: Transition issue while coming from blank page
          setTimeout(() => {
            this._renderer.setAttribute(
              this._elementRef.nativeElement.getElementsByClassName(
                "app-content"
              )[0],
              "style",
              "transition:300ms ease all"
            );
          }, 0);
          // If navbar hidden
          if (this.coreConfig.layout.navbar.hidden) {
            this._elementRef.nativeElement.classList.add("navbar-hidden");
          }
          // Menu (Vertical menu hidden)
          if (this.coreConfig.layout.menu.hidden) {
            this._renderer.setAttribute(
              this._elementRef.nativeElement,
              "data-col",
              "1-column"
            );
          } else {
            this._renderer.removeAttribute(
              this._elementRef.nativeElement,
              "data-col"
            );
          }
          // Footer
          if (this.coreConfig.layout.footer.hidden) {
            this._elementRef.nativeElement.classList.add("footer-hidden");
          }
        }

        // Skin Class (Adding to body as it requires highest priority)
        if (
          this.coreConfig.layout.skin !== "" &&
          this.coreConfig.layout.skin !== undefined
        ) {
          this.document.body.classList.remove(
            "default-layout",
            "bordered-layout",
            "dark-layout",
            "semi-dark-layout"
          );
          this.document.body.classList.add(
            this.coreConfig.layout.skin + "-layout"
          );
        }
      });

    // Set the application page title
    this._title.setTitle(this.coreConfig.app.appTitle);

    // Color Setting
    const savedColor = localStorage.getItem("color_code");
    if (savedColor && savedColor !== "null" && savedColor !== "undefined") {
      this._themeService.setTheme(savedColor);
    }

    // 👉 ADD: đảm bảo bật sidebar ngay lần đầu vào app nếu đã có CMS role active
    this.forceShowSidebarIfCms();
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
    //this.webSocketService.disconnect(); // Đóng kết nối khi component bị hủy
    //this.messageSubscription.unsubscribe();
  }

  // Public methods
  // -----------------------------------------------------------------------------------------------------

  /**
   * Toggle sidebar open
   *
   * @param key
   */
  toggleSidebar(key): void {
    this._coreSidebarService.getSidebarRegistry(key).toggleOpen();
  }
}
