<div class="detail-work-space-header d-flex align-items-center">
  <span
    class="cursor-pointer text-primary-theme mx-1"
    [routerLink]="role == 'USER' ? '/quan-ly-van-ban' : '/super-admin/kgda'"
    ><i data-feather="arrow-left-circle" size="24"></i
  ></span>
  <h3 class="m-0 one-line">{{ workSpaceName }}</h3>
  <ul class="nav navbar-nav align-items-center ml-auto">
    <li ngbDropdown class="nav-item dropdown-user">
      <a
        class="mr-2 nav-link dropdown-user-link d-flex"
        id="dropdown-user"
        ngbDropdownToggle
        id="navbarUserDropdown"
        aria-haspopup="true"
        aria-expanded="false"
      >
        <div class="user-nav d-none d-flex flex-column mr-1">
          <span class="user-name font-weight-bolder">{{
            userName == "" ? "CLS User" : userName
          }}</span>
          <span class="user-status text-right">{{
            role === "ADMIN"
              ? "ADMIN"
              : role === "SUPER_ADMIN"
              ? "SUPER_ADMIN"
              : "USER"
          }}</span>
        </div>

        <span class="avatar"
          ><img
            class="round"
            [src]="
              currentUser.avatar
                ? currentUser.avatar
                : 'assets/images/portrait/small/users.png'
            "
            (error)="refreshLinkAvatar($event)"
            alt="avatar"
            height="40"
            width="40" /><span class="avatar-status-online"></span
        ></span>
      </a>
      <div
        ngbDropdownMenu
        aria-labelledby="navbarUserDropdown"
        class="detail-work-space-dropdown dropdown-menu dropdown-menu-right"
      >
        <a ngbDropdownItem (click)="editUser()">
          <span [data-feather]="'user'" class="mr-50"></span> Thông tin
        </a>
        <a
          ngbDropdownItem
          (click)="addAccount()"
          *ngIf="role === 'ADMIN' || role == 'SUPER_ADMIN'"
        >
          <span [data-feather]="'user-plus'" class="mr-50"></span>
          {{ "AddAccount" | translate }}
        </a>
        <a ngbDropdownItem (click)="changePass()" *ngIf="!isADUser">
          <span [data-feather]="'lock'" class="mr-50"></span> Đổi mật khẩu
        </a>
        <a
          *ngIf="role == 'SUPER_ADMIN'"
          ngbDropdownItem
          [routerLink]="['/cau-hinh']"
        >
          <span [data-feather]="'settings'" class="mr-50"></span> Cấu hình
        </a>
        <a ngbDropdownItem (click)="report()">
          <span [data-feather]="'alert-triangle'" class="mr-50"></span> Báo cáo
        </a>
        <a
          ngbDropdownItem
          [routerLink]="['/pages/authentication/login-v2']"
          (click)="logout()"
        >
          <span [data-feather]="'power'" class="mr-50"></span> Đăng xuất
        </a>
      </div>
    </li>
  </ul>
</div>
<div
  class="detail-work-space-container container-fluid p-0 d-flex work-space px-1"
>
  <!-- Sidebar Desktop (≥1024px) -->
  <div class="card sidebar mb-50 d-none d-xl-block" *ngIf="!isSmallScreenVar ws-docs-list">
    <app-list-document></app-list-document>
  </div>

  <!-- Mobile Document Toggle Button (<1024px) -->
  <div class="mobile-document-toggle d-xl-none">
    <button
      class="btn btn-primary btn-icon rounded-circle"
      type="button"
      (click)="toggleMobileDocumentPanel()"
      [ngbTooltip]="
        isMobileDocumentPanelOpen
          ? 'Ẩn danh sách tài liệu'
          : 'Hiện danh sách tài liệu'
      "
      container="body"
      placement="right"
    >
      <span [data-feather]="'book-open'"></span>
    </button>
  </div>

  <!-- Mobile Document Panel Overlay -->
  <div
    class="mobile-document-overlay"
    [class.show]="isMobileDocumentPanelOpen"
    (click)="closeMobileDocumentPanel()"
    *ngIf="isSmallScreenVar"
  >
    <div class="mobile-document-panel" (click)="$event.stopPropagation()">
      <div class="mobile-document-header">
        <h3 class="mb-0 font-weight-bolder font-medium-2">
          {{ workSpaceName }}
        </h3>
        <button
          class="btn btn-icon btn-sm"
          type="button"
          (click)="closeMobileDocumentPanel()"
        >
          <span [data-feather]="'x'"></span>
        </button>
      </div>
      <div class="mobile-document-content">
        <app-list-document></app-list-document>
      </div>
    </div>
  </div>

  <!-- Main Content (Hiển thị danh sách tài liệu) -->
  <div
    class="detail-work-space-main-content card flex-grow-1 mx-1 content-detail mb-50"
  >
    <div class="card-body p-0">
      <ng-container *ngIf="showContentValue == showContent.Document">
        <app-view-detail-file></app-view-detail-file>
      </ng-container>
      <ng-container *ngIf="showContentValue == showContent.Search">
        <app-tim-kiem-ai></app-tim-kiem-ai>
      </ng-container>
      <ng-container *ngIf="showContentValue == showContent.Clause">
        <app-detail-clause></app-detail-clause>
      </ng-container>
    </div>
  </div>

  <!-- Notes (Có thể thu nhỏ) -->
  <div
    class="card notes mb-50 tour-show-chatbot"
    [ngClass]="{ collapsed: isNotesCollapsed }"
    *ngIf="
      showSideBarValue == showSidebar.Note ||
      showSideBarValue == showSidebar.CompareClause ||
      showSideBarValue == showSidebar.Chatbot
    "
  >
    <div
      class="card-body p-1 d-flex flex-column"
      [ngClass]="{ 'p-xxl-1 p-xl-50': isNotesCollapsed }"
    >
      <div
        class="h-100"
        [ngClass]="{
          'd-xxl-block d-lg-flex align-items-center flex-column':
            isNotesCollapsed
        }"
      >
        <ul
          ngbNav
          #nav="ngbNav"
          class="nav-tabs align-items-center justify-content-center"
          [activeId]="activeTabs"
          (navChange)="onNavNoteChange($event)"
        >
          <!-- <li ngbNavItem *ngIf="!isNotesCollapsed" [ngbNavItem]="'ghichu'">
            <a ngbNavLink>Ghi chú</a>
            <ng-template ngbNavContent>
              <app-take-note></app-take-note>
            </ng-template>
          </li>
          <li ngbNavItem *ngIf="!isNotesCollapsed" [ngbNavItem]="'chatbot'">
            <a ngbNavLink>{{
              showSideBarValue == showSidebar.CompareClause
                ? "So sánh điều khoản"
                : "Chatbot"
            }}</a>
            <ng-template ngbNavContent>
              <ng-container *ngIf="showSideBarValue == showSidebar.Chatbot">
                <app-chatbot></app-chatbot>
              </ng-container>
              <ng-container
                *ngIf="showSideBarValue == showSidebar.CompareClause"
              >
                <app-compare-clause-chatbot></app-compare-clause-chatbot>
              </ng-container>
            </ng-template>
          </li> -->
          <li ngbNavItem *ngIf="!isNotesCollapsed" [ngbNavItem]="'ghichu'">
            <a
              ngbNavLink
              ngbTooltip="Ghi chú"
              container="body"
              class="d-flex align-items-center gap-1"
            >
              <!-- Icon chỉ hiện trên màn hình nhỏ (lg trở xuống) -->
              <img class="d-lg-none" src="assets/images/icons/note2.svg" />
              <!-- Text chỉ hiện trên màn hình lớn -->
              <span class="d-none d-lg-inline">Ghi chú</span>
            </a>
            <ng-template ngbNavContent>
              <app-take-note></app-take-note>
            </ng-template>
          </li>

          <li ngbNavItem *ngIf="!isNotesCollapsed" [ngbNavItem]="'chatbot'">
            <a
              ngbNavLink
              [ngbTooltip]="
                showSideBarValue == showSidebar.CompareClause
                  ? 'So sánh điều khoản'
                  : 'Chatbot'
              "
              container="body"
              class="d-flex align-items-center gap-1"
            >
              <!-- Icon chỉ hiện với màn hình nhỏ -->
              <img class="d-lg-none" src="assets/images/icons/chatbot.svg" />

              <!-- Text chỉ hiện với màn hình lớn -->
              <span class="d-none d-lg-inline">
                {{
                  showSideBarValue == showSidebar.CompareClause
                    ? "So sánh điều khoản"
                    : "Chatbot"
                }}
              </span>
            </a>
            <ng-template ngbNavContent>
              <ng-container *ngIf="showSideBarValue == showSidebar.Chatbot">
                <app-chatbot></app-chatbot>
              </ng-container>
              <ng-container
                *ngIf="showSideBarValue == showSidebar.CompareClause"
              >
                <app-compare-clause-chatbot></app-compare-clause-chatbot>
              </ng-container>
            </ng-template>
          </li>

          <li [ngClass]="{ 'ml-auto': !isNotesCollapsed }">
            <img
              *ngIf="showButtonMaximize && !isNotesCollapsed"
              class="cursor-pointer expand-button mr-50"
              (click)="maximumChatbot()"
              src="assets/images/icons/maximum.svg"
              alt="maximum"
              ngbTooltip="Phóng to"
              container="body"
            />
            <img
              class="cursor-pointer d-none d-md-inline-block"
              [ngClass]="{ 'mr-50': !isMaximized && showSideBarValue == showSidebar.CompareClause && !isNotesCollapsed }"
              (click)="toggleNotes()"
              [ngbTooltip]="isNotesCollapsed ? 'Mở rộng' : 'Thu gọn'"
              container="body"
              [src]="
                isNotesCollapsed
                  ? 'assets/images/icons/expand.svg'
                  : 'assets/images/icons/collab.svg'
              "
              alt="collab"
            />
            <img
              *ngIf="!isMaximized && showSideBarValue == showSidebar.CompareClause && !isNotesCollapsed"
              class="cursor-pointer"
              (click)="closeCompareChatbot()"
              src="assets/images/icons/x.svg"
              alt="x"
              ngbTooltip="Đóng chatbot so sánh"
            />
          </li>
          <span class="detail-work-space-collapsed-hr" *ngIf="isNotesCollapsed">
            <hr />
          </span>
          <li
            *ngIf="isNotesCollapsed"
            ngbTooltip="Ghi chú"
            container="body"
            placement="left"
          >
            <div
              class="cursor-pointer icon-hidden my-1"
              (click)="handleGhiChuClick()"
            >
              <img src="assets/images/icons/note2.svg" alt="search-circle" />
            </div>
          </li>
          <li
            *ngIf="isNotesCollapsed"
            (click)="showChatbot()"
            ngbTooltip="Chatbot"
            container="body"
            placement="left"
          >
            <div class="cursor-pointer icon-hidden">
              <img src="assets/images/icons/chatbot.svg" alt="search-circle" />
            </div>
          </li>
        </ul>
        <div
          [ngbNavOutlet]="nav"
          class="detail-work-space-nav-outlet"
          style="min-height: 0"
        ></div>
      </div>
    </div>
  </div>
  <div
    class="card notes mb-50"
    *ngIf="showSideBarValue == showSidebar.AddClause"
  >
    <app-bo-sung-van-ban-dieu-khoan></app-bo-sung-van-ban-dieu-khoan>
  </div>
  <!-- <div
    class="card notes"
    *ngIf="showSideBarValue == showSidebar.CompareClause"
  >
    <div class="card-body p-1 h-100">
      <div class="h-100">
        <app-compare-clause-chatbot></app-compare-clause-chatbot>
      </div>
    </div>
  </div> -->
</div>

<ng-template #editUserModal let-modal>
  <app-user-profile [modal]="modal"></app-user-profile>
</ng-template>
<ng-template #reportModal let-modal>
  <app-report [modal]="modal"></app-report>
</ng-template>
<ng-template #changePassModal let-modal>
  <div class="modal-body" tabindex="0" ngbAutofocus>
    <div class="form-group">
      <label
        for="basicTextarea"
        class="w-100 align-items-center d-flex justify-content-between"
        >Đổi mật khẩu
        <div class="">
          <button
            class="btn btn-sm ml-auto p-0"
            (click)="modal.dismiss('Cross click')"
          >
            <img src="assets/images/icons/x.svg" alt="x" />
          </button></div
      ></label>
    </div>
    <form [formGroup]="formChangePass" (ngSubmit)="onSubmit()">
      <div class="row">
        <div class="col-12">
          <div class="form-group">
            <label for="currentPassword">
              Mật khẩu cũ <span class="text-danger">*</span>
            </label>
            <div class="input-group form-password-toggle mb-50">
              <input
                [type]="showOldPass ? 'text' : 'password'"
                placeholder="Nhập mật khẩu cũ"
                formControlName="current_password"
                class="form-control"
                id="basic-default-password"
                aria-describedby="basic-default-password"
              />
              <div
                class="input-group-append"
                (click)="showOldPass = !showOldPass"
              >
                <span class="input-group-text cursor-pointer"
                  ><i
                    class="feather"
                    [ngClass]="{
                      'icon-eye-off': showOldPass,
                      'icon-eye': !showOldPass
                    }"
                  ></i
                ></span>
              </div>
            </div>
            <div
              class="text-danger"
              *ngIf="
                fc.current_password.invalid &&
                (fc.current_password.dirty || fc.current_password.touched)
              "
            >
              <div *ngIf="fc.current_password.errors?.required">
                Mật khẩu cũ là bắt buộc.
              </div>
            </div>
          </div>
        </div>
        <div class="col-12">
          <div class="form-group">
            <label for="newPassword">
              Mật khẩu mới <span class="text-danger">*</span>
            </label>

            <div class="input-group form-password-toggle mb-50">
              <input
                [type]="showNewPass ? 'text' : 'password'"
                placeholder="Nhập mật khẩu mới"
                formControlName="new_password"
                class="form-control"
                id="basic-default-password"
                aria-describedby="basic-default-password"
              />
              <div
                class="input-group-append"
                (click)="showNewPass = !showNewPass"
              >
                <span class="input-group-text cursor-pointer"
                  ><i
                    class="feather"
                    [ngClass]="{
                      'icon-eye-off': showNewPass,
                      'icon-eye': !showNewPass
                    }"
                  ></i
                ></span>
              </div>
            </div>
            <div
              class="text-danger"
              *ngIf="
                fc.new_password.invalid &&
                (fc.new_password.dirty || fc.new_password.touched)
              "
            >
              <div *ngIf="fc.new_password.errors?.required">
                Mật khẩu mới là bắt buộc.
              </div>

              <div *ngIf="fc.new_password.errors?.minlength">
                Mật khẩu mới phải có ít nhất 6 ký tự.
              </div>

              <div *ngIf="fc.new_password.errors?.pattern">
                Mật khẩu phải có ít nhất 1 chữ hoa, 1 số và 1 ký tự đặc biệt.
              </div>
            </div>
          </div>
        </div>
        <div class="col-12">
          <div class="form-group">
            <label for="confirmPassword">
              Xác nhận mật khẩu <span class="text-danger">*</span>
            </label>

            <div class="input-group form-password-toggle mb-50">
              <input
                [type]="showConfirmPass ? 'text' : 'password'"
                placeholder="Xác nhận mật khẩu mới"
                formControlName="confirm_password"
                class="form-control"
                id="confirm-password"
                aria-describedby="confirm-password"
              />
              <div
                class="input-group-append"
                (click)="showConfirmPass = !showConfirmPass"
              >
                <span class="input-group-text cursor-pointer">
                  <i
                    class="feather"
                    [ngClass]="{
                      'icon-eye-off': showConfirmPass,
                      'icon-eye': !showConfirmPass
                    }"
                  ></i>
                </span>
              </div>
            </div>

            <div
              class="text-danger"
              *ngIf="
                fc.confirm_password.invalid &&
                (fc.confirm_password.dirty || fc.confirm_password.touched)
              "
            >
              <div *ngIf="fc.confirm_password.errors?.required">
                Xác nhận mật khẩu là bắt buộc.
              </div>
              <div *ngIf="fc.confirm_password.errors?.mustMatch">
                Mật khẩu xác nhận không khớp.
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button
      rippleEffect
      class="btn btn-secondary mr-1"
      (click)="modal.close('Cross click')"
    >
      Huỷ
    </button>
    <button
      rippleEffect
      class="btn btn-primary"
      (click)="submitChangePass()"
      [disabled]="!formChangePass.valid"
    >
      Xác nhận
    </button>
  </div>
</ng-template>
<ng-template #addAccountModal let-modal>
  <div class="modal-body" tabindex="0" ngbAutofocus>
    <div class="form-group">
      <label
        for="basicTextarea"
        class="w-100 align-items-center d-flex justify-content-between"
        >Thêm tài khoản
        <div class="">
          <button
            class="btn btn-sm ml-auto p-0"
            (click)="modal.dismiss('Cross click')"
          >
            <img src="assets/images/icons/x.svg" alt="x" />
          </button></div
      ></label>
    </div>
    <form [formGroup]="formAddAccount" (ngSubmit)="onSubmit()">
      <div class="form-group">
        <label for="fullName">
          Họ và tên <span class="text-danger">*</span>
        </label>
        <input
          type="text"
          class="form-control"
          placeholder="Họ và tên"
          formControlName="fullName"
        />
        <div
          class="text-danger"
          *ngIf="
            getFormControl('fullName').invalid &&
            getFormControl('fullName').touched
          "
        >
          <div *ngIf="getFormControl('fullName').errors?.required">
            Không được bỏ trống
          </div>
        </div>
      </div>
      <div class="form-group">
        <label for="email"> Email <span class="text-danger">*</span> </label>
        <input
          type="text"
          class="form-control"
          placeholder="Email"
          formControlName="email"
        />
        <div
          class="text-danger"
          *ngIf="
            getFormControl('email').invalid && getFormControl('email').touched
          "
        >
          <div *ngIf="getFormControl('email').errors?.required">
            Không được bỏ trống
          </div>
          <div *ngIf="getFormControl('email').errors?.email">
            Email sai định dạng
          </div>
        </div>
      </div>
      <div class="form-group">
        <label for="password">
          Mật khẩu <span class="text-danger">*</span>
        </label>

        <div class="input-group form-password-toggle mb-50">
          <input
            [type]="showPassWord ? 'text' : 'password'"
            placeholder="Mật khẩu"
            formControlName="password"
            class="form-control"
            id="basic-default-password"
            aria-describedby="basic-default-password"
          />
          <div
            class="input-group-append"
            (click)="showPassWord = !showPassWord"
          >
            <span class="input-group-text cursor-pointer"
              ><i
                class="feather"
                [ngClass]="{
                  'icon-eye-off': showPassWord,
                  'icon-eye': !showPassWord
                }"
              ></i
            ></span>
          </div>
        </div>
        <div
          class="text-danger"
          *ngIf="
            getFormControl('password').invalid &&
            getFormControl('password').touched
          "
        >
          <div *ngIf="getFormControl('password').errors?.required">
            Không được bỏ trống
          </div>
          <div *ngIf="getFormControl('password').errors?.minlength">
            Mật khẩu ít nhất 6 ký tự
          </div>
          <div *ngIf="getFormControl('password').errors?.pattern">
            Mật khẩu phải có ít nhất 1 chữ hoa, 1 số và 1 ký tự đặc biệt.
          </div>
        </div>
      </div>
      <div class="form-group">
        <label for="confirmPassword">
          Xác nhận mật khẩu <span class="text-danger">*</span>
        </label>

        <div class="input-group form-password-toggle mb-50">
          <input
            [type]="showConfirmPass ? 'text' : 'password'"
            placeholder="Xác nhận mật khẩu mới"
            formControlName="confirm_password"
            class="form-control"
            id="confirm-password"
            aria-describedby="confirm-password"
          />
          <div
            class="input-group-append"
            (click)="showConfirmPass = !showConfirmPass"
          >
            <span class="input-group-text cursor-pointer">
              <i
                class="feather"
                [ngClass]="{
                  'icon-eye-off': showConfirmPass,
                  'icon-eye': !showConfirmPass
                }"
              ></i>
            </span>
          </div>
        </div>

        <div
          class="text-danger"
          *ngIf="
            getFormControl('confirm_password').invalid &&
            (getFormControl('confirm_password').dirty ||
              getFormControl('confirm_password').touched)
          "
        >
          <div *ngIf="getFormControl('confirm_password').errors?.required">
            Xác nhận mật khẩu là bắt buộc.
          </div>
          <div *ngIf="getFormControl('confirm_password').errors?.mustMatch">
            Mật khẩu xác nhận không khớp.
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer">
    <button rippleEffect class="btn btn-secondary mr-1" (click)="modal.close()">
      Huỷ
    </button>
    <button
      rippleEffect
      class="btn btn-primary"
      (click)="submitAddAccount()"
      [disabled]="!formAddAccount.valid"
    >
      Xác nhận
    </button>
  </div>
</ng-template>

<!-- Modal cho Ghi chú trên màn hình nhỏ -->
<ng-template #notesModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title font-weight-bolder">Ghi chú</h4>
    <button
      type="button"
      class="close"
      aria-label="Close"
      (click)="modal.dismiss('Cross click')"
    >
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body p-0">
    <app-take-note></app-take-note>
  </div>
</ng-template>
