import { Component, OnInit, ViewChild, ViewEncapsulation } from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { CoreConfigService } from "@core/services/config.service";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { AuthenticationService } from "app/auth/service";
import { ShowContent } from "app/models/ShowContent";
import { ShowSideBar } from "app/models/ShowSideBa";
import { Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { QuanLyVanBanService } from "../quan-ly-van-ban.service";
import { DetailWorkSpaceService } from "./detail-work-space.service";
import { ListDocumentService } from "./list-document/list-document.service";
import { MediaObserver } from "@angular/flex-layout";
import { Form<PERSON>uilder, FormGroup, Validators } from "@angular/forms";
import { ToastrService } from "ngx-toastr";
import { CompareClauseChatbotComponent } from "./compare-clause-chatbot/compare-clause-chatbot.component";
import { ShepherdService } from 'angular-shepherd';

// import { ShepherdService } from "angular-shepherd";
@Component({
  selector: "app-detail-work-space",
  templateUrl: "./detail-work-space.component.html",
  styleUrls: ["./detail-work-space.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class DetailWorkSpaceComponent implements OnInit {

  isNotesCollapsed = true;
  public workSpaceName: string = "";
  public workSpaceId: string = "";
  public limit: number[] = [10, 15, 20, 25, 30];
  public isMaximized: boolean = false;
  public showButtonMaximize: boolean = false;
  public _unSubscribe: Subject<any> = new Subject<any>();
  public showContent = ShowContent;
  public showSidebar = ShowSideBar;
  public showContentValue = ShowContent.Search;
  public showSideBarValue = ShowSideBar.Note;
  public userName: string;
  public role: string;
  public activeTabs = "ghichu";
  public currentUser: any;
  public backBtnClass = "btn btn-sm btn-outline-primary";
  public nextBtnClass = "btn btn-sm btn-primary btn-next";
  public isMobileDocumentPanelOpen: boolean = false;
  // Forms & modal refs
  @ViewChild("changePassModal") changePassModal: NgbActiveModal;
  @ViewChild("addAccountModal") addAccountModal: NgbActiveModal;
  @ViewChild("editUserModal") editUserModal: NgbActiveModal;
  @ViewChild("reportModal") reportModal: NgbActiveModal;
  @ViewChild("notesModal") notesModal: NgbActiveModal;
  @ViewChild(CompareClauseChatbotComponent) chatbotCompare: CompareClauseChatbotComponent;
  public formChangePass: FormGroup;
  public formAddAccount: FormGroup;
  public showOldPass = false;
  public showNewPass = false;
  public showPassWord = false;
  public showConfirmPass = false;
  isSmallScreenVar: boolean = false;
  isADUser: boolean = false;
  private _openNotesListener = () => {
    this.openNotesFromTour('ghichu');
  };
  constructor(
    private route: ActivatedRoute,
    private workSpace: DetailWorkSpaceService,
    private configApp: CoreConfigService,
    private listDocumentService: ListDocumentService,
    private router: Router,
    private _authenService: AuthenticationService,
    private modalService: NgbModal,
    private authenService: AuthenticationService,
    private workspaceService: QuanLyVanBanService,
    private _mediaObserver: MediaObserver,
    private fb: FormBuilder,
    private _toast: ToastrService,
    private shepherdService: ShepherdService,
    // private shepherdService: ShepherdService
  ) {
    this.authenService.currentUser.subscribe((res) => {
      this.currentUser = res;
      this.userName = res?.fullname;
    });
    this.role = this._authenService.currentUserValue.role;
  }
  private setNotesTab(tab: 'ghichu' | 'chatbot') {
    if (tab === 'ghichu') {
      this.activeTabs = 'ghichu';
      this.showButtonMaximize = false;
      this.listDocumentService.rightSideBarValue.next(ShowSideBar.Note);
    } else {
      this.activeTabs = 'chatbot';
      this.showButtonMaximize = true;
      this.listDocumentService.rightSideBarValue.next(ShowSideBar.Chatbot);
    }
  }

  /** Mở sidebar theo đúng luồng UI như người dùng bấm nút – dùng cho Tour */
  private openNotesFromTour(tab: 'ghichu' | 'chatbot' = 'ghichu') {
    // Màn nhỏ: tái sử dụng luồng modal có sẵn để tránh lệch UI
    if (this.isSmallerThanLg()) {
      if (tab === 'ghichu') this.openNotesModal();
      else this.showChatbot();
      return;
    }

    const wasCollapsed = this.isNotesCollapsed;

    if (wasCollapsed) {
      // Dùng đúng nút toggle hiện có để mở panel (đảm bảo class, width…)
      this.toggleNotes();

      // Chờ 1 frame cho DOM bỏ class 'collapsed' rồi mới set tab -> tránh giật layout
      requestAnimationFrame(() => this.setNotesTab(tab));
    } else {
      this.setNotesTab(tab);
    }
  }
  ngOnInit(): void {
    this.configApp.setConfig({
      layout: {
        navbar: { hidden: true },
        menu: { hidden: true },
        footer: { hidden: true },
      },
    });
    this.route.queryParams.subscribe((params) => {
      this.workSpaceName = params["workSpaceName"];
      if (params["fileId"]) {
        this.showContentValue = ShowContent.Document;
      }
    });
    this.listDocumentService.contentValue
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((res) => {
        this.showContentValue = res;
      });

    this.workSpace.isNotesCollapsed
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((res) => {
        this.isNotesCollapsed = res; // để nhận biến nhằm mở collabse khi nhấn so sánh điều khoản
      });

    this.listDocumentService.rightSideBarValue
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((res) => {
        this.showSideBarValue = res;
        if (res == ShowSideBar.CompareClause || res == ShowSideBar.Chatbot) {
          this.showButtonMaximize = true;
          this.activeTabs = "chatbot";
        } else {
          this.activeTabs = "ghichu";
        }
      });
    window.addEventListener('open-notes-tab', this._openNotesListener);

    // Lắng nghe signal từ chatbot để toggle notes khi minimize
    this.workSpace.shouldToggleNotes
      .pipe(takeUntil(this._unSubscribe))
      .subscribe((shouldToggle) => {
        if (shouldToggle) {
          this.toggleNotes();
          // Reset signal
          this.workSpace.shouldToggleNotes.next(false);
        }
      });
    // this.workspaceService.isShowTourGuide
    //   .pipe(takeUntil(this._unSubscribe))
    //   .subscribe((res) => {
    //     if (res) {
    //       setTimeout(() => {
    //         this.shepherdService.start();
    //       }, 300);
    //     }
    //   });
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {
        fileId: null, // xoá hết params khi người dùng load trang
        tabs: null,
        type: null,
        luocdo: null,
        fileName: null,
        save: null,
      },
      queryParamsHandling: "merge", // giữ lại các param khác nếu có
    });
    this.showContentValue = ShowContent.Search;

    this.workSpaceId = this.route.snapshot.params.id;

    // Build forms (same validators as navbar)
    this.formAddAccount = this.fb.group(
      {
        email: [null, [Validators.required, Validators.email]],
        fullName: [null, Validators.required],
        password: [
          null,
          [
            Validators.required,
            Validators.minLength(6),
            Validators.pattern(/^(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{6,}$/),
          ],
        ],
        confirm_password: ["", Validators.required],
      },
      {
        validator: this.mustMatch("password", "confirm_password"),
      }
    );

    this.formChangePass = this.fb.group(
      {
        current_password: [null, Validators.required],
        new_password: [
          null,
          [
            Validators.required,
            Validators.minLength(6),
            Validators.pattern(/^(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{6,}$/),
          ],
        ],
        confirm_password: ["", Validators.required],
      },
      {
        validator: this.mustMatch("new_password", "confirm_password"),
      }
    );

    // this.isSmallScreenVar = this.breakpointObserver.isMatched("(max-width: 1024px)");
    this.isSmallScreenVar = window.innerWidth < 1200;

    window.addEventListener('resize', () => {
      this.isSmallScreenVar = window.innerWidth < 1200;
    });

    this.isADUser = localStorage.getItem('isADUser') === 'true';
  }
  get fc() {
    return this.formChangePass.controls;
  }
  getFormControl(name: string) {
    return this.formAddAccount.get(name);
  }
  mustMatch(controlName: string, matchingControlName: string) {
    return (formGroup: FormGroup) => {
      const control = formGroup.controls[controlName];
      const matchingControl = formGroup.controls[matchingControlName];

      if (matchingControl.errors && !matchingControl.errors.mustMatch) {
        return;
      }

      if (control.value !== matchingControl.value) {
        matchingControl.setErrors({ mustMatch: true });
      } else {
        matchingControl.setErrors(null);
      }
    };
  }
  addAccount() {
    this.modalOpen(this.addAccountModal);
  }
  changePass() {
    this.modalOpen(this.changePassModal);
  }
  submitChangePass() {
    if (this.formChangePass.valid) {
      this._authenService
        .changePass(
          this.formChangePass.get("current_password").value,
          this.formChangePass.get("new_password").value
        )
        .subscribe(
          (res) => {
            if (res.code == 200) {
              this._toast.success("Đổi mật khẩu", "Thành công", {
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
                closeButton: true,
              });
              this.modalService.dismissAll();
              this.formChangePass.reset();
            }
          },
          (err) => {
            this._toast.error("Sai mật khẩu", "Lỗi", {
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
              closeButton: true,
            });
          }
        );
    }
  }
  submitAddAccount() {
    if (this.formAddAccount.valid) {
      this._authenService
        .addAccount(
          this.formAddAccount.get("email").value,
          this.formAddAccount.get("fullName").value,
          this.formAddAccount.get("password").value
        )
        .subscribe(
          (res) => {
            if (res) {
              this._toast.success("Tạo tài khoản", "Thành công", {
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
                closeButton: true,
              });
              this.modalService.dismissAll();
              this.formAddAccount.reset();
            }
          },
          (err) => {
            if (err.error === "user with this email already exists.") {
              this._toast.error("Email đã được sử dụng", "Thất bại", {
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
                closeButton: true,
              });
            } else {
              this._toast.error("Thêm tài khoản", "Thất bại", {
                positionClass: "toast-top-right",
                toastClass: "toast ngx-toastr",
                closeButton: true,
              });
            }
          }
        );
    }
  }
  report() {
    this.modalOpen(this.reportModal);
  }
  editUser() {
    this.modalOpen(this.editUserModal);
  }
  logout() {
    this._authenService.logout();
  }
  toggleNotes() {
    this.isNotesCollapsed = !this.isNotesCollapsed;
    this.workSpace.isNotesCollapsed.next(this.isNotesCollapsed);

    // Nếu đang mở tab và là màn hình nhỏ
    if (!this.isNotesCollapsed && this.isSmallScreen()) {
      // Mặc định mở tab ghi chú trước
      this.activeTabs = "ghichu";
      this.listDocumentService.rightSideBarValue.next(ShowSideBar.Note);
      this.showButtonMaximize = false;
    }
  }

  // Hàm kiểm tra màn hình nhỏ (dưới 1200px)
  public isSmallScreen(): boolean {
    return window.innerWidth < 1200;
  }

  // Hàm kiểm tra màn hình nhỏ hơn lg (dưới 992px)
  private isSmallerThanLg(): boolean {
    return window.innerWidth < 992;
  }
  private waitForSelector(selector: string, timeout = 8000): Promise<HTMLElement|null> {
    return new Promise(resolve => {
      const el = document.querySelector(selector) as HTMLElement;
      if (el) return resolve(el);

      const start = Date.now();
      const tick = setInterval(() => {
        const node = document.querySelector(selector) as HTMLElement;
        if (node) { clearInterval(tick); resolve(node); }
        if (Date.now() - start > timeout) { clearInterval(tick); resolve(null); }
      }, 100);
    });
  }

  private waitForEl(selector: string, timeout = 5000): Promise<HTMLElement | null> {
    return new Promise(resolve => {
      const start = Date.now();
      const id = setInterval(() => {
        const el = document.querySelector(selector) as HTMLElement;
        if (el) { clearInterval(id); resolve(el); }
        else if (Date.now() - start > timeout) { clearInterval(id); resolve(null); }
      }, 50);
    });
  }

  private scrollInto(el: HTMLElement) {
    try { el.scrollIntoView({ block: 'center', inline: 'nearest', behavior: 'auto' }); } catch {}
  }

  private async centerOn(selector: string) {
    const el = await this.waitForSelector(selector);
    if (el) {
      el.scrollIntoView({ block: 'center', inline: 'nearest', behavior: 'auto' });
      await new Promise(r => setTimeout(r, 150)); // đợi 1 nhịp cho layout ổn định
    }
  }
  private async waitUntilVisible(selector: string, timeout = 8000) {
    const el = await this.waitForSelector(selector, timeout);
    if (!el) return;
    return new Promise<void>((resolve) => {
      let tries = 0;
      const check = () => {
        const r = el.getBoundingClientRect();
        const styles = getComputedStyle(el);
        const visible = r.width > 10 && r.height > 10 && styles.display !== 'none' && styles.visibility !== 'hidden';
        if (visible) resolve();
        else if (tries++ < 60) requestAnimationFrame(check);
        else resolve();
      };
      check();
    });
  }

  private buildDetailSteps() {
    const total = 7;

    return [
      // ===== BƯỚC 4: Danh sách văn bản =====
      {
        id: 'docs-list',
        title: 'Danh sách văn bản',
        text: this.makeText('Khu vực này cho phép quản lý toàn bộ tài liệu, văn bản bao gồm các tệp tải lên và văn bản đã được lưu từ cơ sở dữ liệu.', 4, total),
        attachTo: { element: '.ws-docs-list', on: 'right' },
        when: {
          show: () => {
            document.querySelector('.sidebar')?.classList.add('tour-unclip');
            document.querySelector('.ws-docs-list')?.classList.add('tour-target-top');
          },
          hide: () => {
            document.querySelector('.sidebar')?.classList.remove('tour-unclip');
            document.querySelector('.ws-docs-list')?.classList.remove('tour-target-top');
          }
        },
        buttons: [{ text:'Tiếp tục', type:'next', classes:'tour-btn tour-btn--primary ml-auto' }]
      },
      // ===== BƯỚC 5: Khu vực trung tâm =====
      {
        id: 'center-area',
        title: 'Khu vực trung tâm',
        text: this.makeText('Khu vực này dùng để hiển thị kết quả tìm kiếm, đối chiếu và so sánh các văn bản quy phạm pháp luật, đồng thời cung cấp nội dung chi tiết của văn bản.', 5, total),
        attachTo: { element: '#tim-kiem', on: 'right' },
        beforeShowPromise: async () => {
          const el = await this.waitForEl('#tim-kiem');
          if (el) this.scrollInto(el);
        },
        when: {
          show: () => {
            document.body.classList.add('cls-tour-running');
            [
              '.detail-work-space-main-content',
              '.detail-work-space-nav-outlet',
              '.content-body',
              '.content-detail',           
              '.detail-work-space-container', 
              '#tim-kiem'
            ].forEach(sel => document.querySelector(sel)?.classList.add('tour-unclip'));

            document.querySelector('#tim-kiem')
              ?.classList.add('tour-target-top','cls-tour-target');
          },
          hide: () => {
            document.body.classList.remove('cls-tour-running');
            [
              '.detail-work-space-main-content',
              '.detail-work-space-nav-outlet',
              '.content-body',
              '.content-detail',         
              '.detail-work-space-container', 
              '#tim-kiem'
            ].forEach(sel => document.querySelector(sel)?.classList.remove('tour-unclip'));

            document.querySelector('#tim-kiem')
              ?.classList.remove('tour-target-top','cls-tour-target');
          }
        }
        ,
        buttons: [
          { text:'Quay lại', type:'back', classes:'tour-btn tour-btn--outline-primary' },
          { text:'Tiếp tục', type:'next', classes:'tour-btn tour-btn--primary ml-auto' }
        ]
      },
      // ===== BƯỚC 6: Nút mở Ghi chú/Chatbot =====
      {
        id: 'notes-open',
        title: 'Ghi chú và Chatbot',
        text: this.makeText(
          'Ghi chú cho phép tạo, lưu trữ và quản lý các ghi chú cá nhân liên quan khi làm việc.<br/>Chatbot cho phép trò chuyện, đặt câu hỏi, truy vấn về các vấn đề pháp luật với câu trả lời đầy đủ, rõ ràng, chi tiết.',
          6, total
        ),
        attachTo: { element: '.tour-show-chatbot', on: 'left' },
        buttons: [
          { text: 'Quay lại', type: 'back', classes: 'tour-btn tour-btn--outline-primary' },
          {
            text: 'Mở rộng',
            classes: 'tour-btn tour-btn--primary ml-auto',
            action: () => {
              window.dispatchEvent(new Event('open-notes-tab'));  // anh đã có listener
              this.showStepWhenReady('notes-explain', '.detail-work-space-nav-outlet', 8000);
            }
          }
        ]
      },

      // ===== BƯỚC 7: Thuyết minh vùng Ghi chú/Chatbot =====
      {
        id: 'notes-explain',
        title: 'Ghi chú và Chatbot',
        text: this.makeText(
          'Ghi chú cho phép tạo, lưu trữ và quản lý các ghi chú cá nhân liên quan khi làm việc.<br/>Chatbot cho phép trò chuyện, đặt câu hỏi, truy vấn về các vấn đề pháp luật với câu trả lời đầy đủ, rõ ràng, chi tiết.',
          7, total
        ),

        // GẮN VÀO TOÀN BỘ CỘT GHI CHÚ/CHATBOT
        attachTo: { element: '.card.notes', on: 'left' },

        // (tùy chọn, “bảo hiểm”) chủ động add/remove lớp highlight
        when: {
          show: () => document.querySelector('.card.notes')?.classList.add('cls-tour-target'),
          hide: () => document.querySelector('.card.notes')?.classList.remove('cls-tour-target'),
        },

        buttons: [
          { text: 'Kết thúc', type: 'cancel', classes: 'tour-btn tour-btn--primary ml-auto' }
        ]
      }
    ];
  }

  private makeText(content: string, index: number, total: number) {
    return `
      <div class="tour-body">
        <div class="tour-content">
          <p>${content}</p>
        </div>
      </div>
    `;
  }
  private preventScrollJumpOnShow = () => {
    document.documentElement.classList.add('shepherd-modal-is-visible');
    document.body.classList.add('shepherd-modal-is-visible');

    // Xóa mọi inline style Shepherd có thể set
    document.body.style.paddingRight = '0px';
    (document.documentElement as HTMLElement).style.paddingRight = '0px';

    // Khóa ngang, cho phép dọc (khớp CSS ở trên)
    document.body.style.overflowX = 'hidden';
    (document.documentElement as HTMLElement).style.overflowX = 'hidden';
    document.body.style.overflowY = 'auto';
    (document.documentElement as HTMLElement).style.overflowY = 'auto';
  };

  private clearScrollJumpFix = () => {
    document.body.style.removeProperty('padding-right');
    (document.documentElement as HTMLElement).style.removeProperty('padding-right');
    document.body.style.removeProperty('overflow');
    (document.documentElement as HTMLElement).style.removeProperty('overflow');
    document.body.style.removeProperty('overflow-x');
    (document.documentElement as HTMLElement).style.removeProperty('overflow-x');
    document.body.style.removeProperty('overflow-y');
    (document.documentElement as HTMLElement).style.removeProperty('overflow-y');
  };

  handleGhiChuClick() {
    if (this.isSmallerThanLg()) {
      // console.log("small screen")
      this.openNotesModal();
    } else {
      this.isNotesCollapsed = false;
      this.activeTabs = 'ghichu';
      this.showButtonMaximize = false;
    }
  }

  onNavNoteChange(event: any) {
    if (event.nextId == "ghichu") {
      this.isNotesCollapsed = false;
      this.listDocumentService.rightSideBarValue.next(ShowSideBar.Note);
      this.showButtonMaximize = false;
    } else {
      this.listDocumentService.rightSideBarValue.next(ShowSideBar.Chatbot);
      this.showButtonMaximize = true;

      // Nếu là màn hình nhỏ và chuyển sang tab chatbot thì gọi maximumChatbot luôn
      if (this.isSmallScreen()) {
        this.maximumChatbot();
      }
    }
  }
  showChatbot() {
    // console.log("call me ?")
    this.isNotesCollapsed = false;
    this.activeTabs = "chatbot";
    this.showButtonMaximize = true;
    this.listDocumentService.rightSideBarValue.next(ShowSideBar.Chatbot);
    // console.log(window.innerWidth)
    // console.log(this.isSmallScreen())
    if (this.isSmallScreen()) {
        this.maximumChatbot();
      }
  }
  maximumChatbot() {
    if (this.chatbotCompare && !this.chatbotCompare.doneChatBot) {
    this._toast.warning("Đang so sánh...", "Cảnh báo", {
      positionClass: "toast-top-right",
      toastClass: "toast ngx-toastr",
      closeButton: true,
      timeOut: 3000,
    });
    return;
  }
    this.workSpace.isMaximized.next(true);
  }
  modalOpen(modalSM) {
    this.modalService.open(modalSM, {
      centered: true,
      size: "sm",
    });
  }

  // Phương thức mở modal ghi chú cho màn hình nhỏ
  openNotesModal() {
    this.modalService.open(this.notesModal, {
      centered: true,
      size: "lg",
      windowClass: "notes-modal"
    });
  }
  refreshLinkAvatar(event) {
    this.authenService.refreshAvatar().subscribe((res) => {
      const avatarLink = res.data.avatar_url;
      (event.target as HTMLImageElement).src = avatarLink;
      let currentUser = JSON.parse(localStorage.getItem("current_User"));

      // Bước 2: Cập nhật giá trị email
      if (currentUser) {
        currentUser.avatar = avatarLink;

        // Bước 3: Ghi lại vào localStorage
        localStorage.setItem("current_User", JSON.stringify(currentUser));
      }
    });
  }

  ngAfterViewInit() {
    // Cấu hình Shepherd cho trang Workspace
    this.hardKillShepherdArtifacts();

    this.shepherdService.defaultStepOptions = {
      cancelIcon: { enabled: true },
      classes: 'cls-tour',
      scrollTo: false,
      canClickTarget: true,
      useModalOverlay: false,
      highlightClass: 'cls-tour-target',
      arrow: true, 
    };
    this.shepherdService.modal = false;

    // Nếu từ trang trước đặt cờ resume, build steps và start tour
    const resume = localStorage.getItem('cls_tour_resume');
    if (resume === 'docs-list') {
      const steps = this.buildDetailSteps();
      this.shepherdService.addSteps(steps);
      this.shepherdService.start();             
      this.showStepWhenReady('docs-list', '.ws-docs-list'); // nhảy đúng Step 4
      localStorage.removeItem('cls_tour_resume');
    }
  }

  // ngAfterViewInit() {
    // tour steps
  //   this.shepherdService.defaultStepOptions = {
  //     cancelIcon: {
  //       enabled: true,
  //     },
  //   };
  //   this.shepherdService.modal = true;

  //   this.shepherdService.addSteps([
  //     {
  //       title: "Danh sách văn bản",
  //       text: "Khu vực này cho phép quản lý toàn bộ tài liệu, văn bản bao gồm các tệp tải lên và văn bản đã được lưu từ cơ sở dữ liệu .",
  //       attachTo: {
  //         element: ".file-container",
  //         on: "right",
  //       },
  //       buttons: [
  //         {
  //           text: "Tiếp tục",
  //           type: "next",
  //           classes: "btn btn-sm btn-primary btn-next ml-auto",
  //         },
  //       ],
  //       useModalOverlay: true,
  //     },
  //     {
  //       title: "Khu vực trung tâm",
  //       text: "Khu vực này dùng để hiển thị kết quả tìm kiếm, đối chiếu và so sánh các văn bản quy phạm pháp luật, đồng thời cung cấp nội dung chi tiết của văn bản. ",
  //       attachTo: {
  //         element: ".tour-view-file",
  //         on: "left",
  //       },
  //       buttons: [
  //         {
  //           text: "Quay lại",
  //           type: "back",
  //           classes: this.backBtnClass,
  //         },
  //         {
  //           text: "Tiếp tục",
  //           type: "next",
  //           classes: this.nextBtnClass,
  //         },
  //       ],
  //     },
  //     {
  //       title: "Ghi chú và Chatbot",
  //       text: `<p class="mb-0">Ghi chú cho phép tạo, lưu trữ và quản lý các ghi chú cá nhân liên quan khi làm việc. </p>
  //             <p>Chatbot cho phép trò chuyện, đặt câu hỏi, truy vấn về các vấn đề pháp luật với câu trả lời đầy đủ rõ ràng chi tiết .</p>
  //       `,
  //       attachTo: {
  //         element: ".tour-show-chatbot",
  //         on: "left",
  //       },
  //       buttons: [
  //         {
  //           text: "Quay lại",
  //           type: "back",
  //           classes: this.backBtnClass,
  //         },
  //         {
  //           text: "Mở rộng",
  //           classes: this.nextBtnClass,
  //           action: () => {
  //             this.toggleNotes();
  //             setTimeout(() => {
  //               this.shepherdService.next();
  //             }, 500);
  //           },
  //         },
  //       ],
  //     },
  //     {
  //       title: "Ghi chú và Chatbot",
  //       text: `<p class="mb-0">Ghi chú cho phép tạo, lưu trữ và quản lý các ghi chú cá nhân liên quan khi làm việc. </p>
  //             <p>Chatbot cho phép trò chuyện, đặt câu hỏi, truy vấn về các vấn đề pháp luật với câu trả lời đầy đủ rõ ràng chi tiết .</p>
  //       `,
  //       attachTo: {
  //         element: ".abcccc",
  //         on: "left",
  //       },
  //       buttons: [
  //         {
  //           text: "Kết thúc",
  //           type: "next",
  //           classes: this.nextBtnClass,
  //         },
  //       ],
  //     },
  //   ]);
  // }

  private showStepWhenReady(stepId: string, selector: string, timeoutMs = 20000) {
    const tour: any = (this.shepherdService as any).tourObject;
    const start = Date.now();
    const tick = setInterval(() => {
      if (document.querySelector(selector)) {
        clearInterval(tick);
        try { tour.show(stepId); } catch {}
      }
      if (Date.now() - start > timeoutMs) clearInterval(tick);
    }, 250);
  }
  private hardKillShepherdArtifacts() {
    try { (this.shepherdService as any).tourObject?.cancel(); } catch {}
    document.querySelectorAll('.shepherd-modal-overlay-container').forEach(el => el.remove());
    document.body.classList.remove('shepherd-modal-is-visible');
    document.documentElement.classList.remove('shepherd-modal-is-visible');
    document.body.style.removeProperty('padding-right');
    document.documentElement.style.removeProperty('padding-right');
    document.body.style.removeProperty('overflow');
    document.documentElement.style.removeProperty('overflow');
    window.scrollTo({ top: 0, left: 0, behavior: 'auto' });
  }
  // Mobile document panel methods
  toggleMobileDocumentPanel(): void {
    this.isMobileDocumentPanelOpen = !this.isMobileDocumentPanelOpen;
  }

  closeMobileDocumentPanel(): void {
    this.isMobileDocumentPanelOpen = false;
  }

  closeCompareChatbot() {
    this.listDocumentService.rightSideBarValue.next(ShowSideBar.Chatbot);
  }

  ngOnDestroy(): void {
    try { this.shepherdService.cancel(); } catch {}
    this.hardKillShepherdArtifacts(); 
    localStorage.removeItem('cls_tour_resume');
    this.listDocumentService.contentValue.next(ShowContent.Document);
    this.listDocumentService.rightSideBarValue.next(ShowSideBar.Note);
    this.workSpace.isMaximized.next(false);
    this.workspaceService.isShowTourGuide.next(false);
    this._unSubscribe.next(null);
    this._unSubscribe.complete();
    this.listDocumentService.reset();
    window.removeEventListener('open-notes-tab', this._openNotesListener);
  }
}
