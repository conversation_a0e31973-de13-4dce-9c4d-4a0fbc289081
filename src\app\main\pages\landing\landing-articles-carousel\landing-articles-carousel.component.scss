/* landing-articles-carousel.component.scss */
.lac {
    position: relative;
    /* anchor overlay controls */
    overflow: visible;
    /* allow outside-positioned buttons */

    padding-bottom: 12px;

    &::after {
      content: "";
      display: block;
      height: 1px;
      width: 100%;
      margin-top: 40px;
      background: #e5e7eb;
    }

    &__header {
        /* header only serves as a DOM host for controls; remove layout impact */
        margin: 0;
        padding: 0;
        height: 0;
    }

    &__controls {
        position: absolute;
        left: 0;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;
        pointer-events: none;
        /* allow clicks to pass except on buttons */
    }

    &__ctrl {
        width: 40px;
        height: 40px;
        border-radius: 999px;
        border: 1px solid rgba(0, 0, 0, 0.08);
        background: #fff;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.06);
        display: grid;
        place-items: center;
        cursor: pointer;
        pointer-events: auto;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
    }

    &__chev {
        width: 8px;
        height: 8px;
        border-top: 2px solid #0a66ff;
        border-right: 2px solid #0a66ff;
        transform: rotate(45deg);

        &--left {
            transform: rotate(-135deg);
        }

        &--right {
            transform: rotate(45deg);
        }
    }

    /* place controls fully outside the carousel/grid */
    &__ctrl--prev {
        left: -56px;
    }

    &__ctrl--next {
        right: -56px;
    }

    @media (max-width: 1199.98px) {
        &__ctrl--prev {
            left: -32px;
        }

        &__ctrl--next {
            right: -32px;
        }
    }

    @media (max-width: 991.98px) {
        &__ctrl--prev {
            left: -12px;
        }

        &__ctrl--next {
            right: -12px;
        }
    }

    &__grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 24px;
    }

    &__item {
        height: 100%;
    }

    &__dots {
        margin-top: 12px;
        display: flex;
        justify-content: center;
        gap: 8px;
    }

    &__dot {
        width: 8px;
        height: 8px;
        border-radius: 999px;
        background: #cfd7e6;
        border: 0;
        cursor: pointer;

        &--active {
            background: #0a66ff;
        }
    }
}

:host ::ng-deep .lac__carousel .carousel-control-prev,
:host ::ng-deep .lac__carousel .carousel-control-next,
:host ::ng-deep .lac__carousel .carousel-indicators {
    display: none !important;
}

/* Responsive */
@media (max-width: 1199.98px) {
  .lac__grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 991.98px) {
    .lac__grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 575.98px) {
    .lac__grid {
        grid-template-columns: 1fr;
    }
}
.lac__ctrl{
  border: 1px solid rgba(2,18,43,.10);
  box-shadow: 0 6px 16px rgba(2,18,43,.08);
  transition: background-color .2s ease, border-color .2s ease, box-shadow .2s ease, transform .2s ease;
    top: 50%;
    transform: translateY(-50%); 
    z-index: 11;  
    pointer-events: auto;      
}
.lac__chev{
  border-top-color: #0EA5E9;  
  border-right-color: #0EA5E9;
}
.lac__ctrl:hover,
.lac__ctrl:focus-visible{
  background: #0EA5E9;
  border-color: #0EA5E9;
  box-shadow: 0 10px 22px rgba(14,165,233,.30);
  outline: none;
}
.lac__ctrl:hover .lac__chev,
.lac__ctrl:focus-visible .lac__chev{
  border-top-color: #fff;
  border-right-color: #fff;
}
.lac__ctrl:active{ transform: translateY(1px); }

.lac__dots{ margin-top:14px; display:flex; justify-content:center; gap:10px; }
.lac__dot{
  width:8px; height:8px; border-radius:999px; background:#d6dbe6; border:0; cursor:pointer; padding:0;
  transition: transform .2s ease, background-color .2s ease, width .2s ease;
}
.lac__dot:hover{ transform: scale(1.15); }
.lac__dot--active{ background:#0EA5E9; width:20px; }

.lac__grid{ 
  padding-bottom:14px; 
  display: grid;
  grid-template-columns: repeat(4, minmax(0,1fr)); 
  gap: 24px;
}

.lac {
  position: relative;
  overflow: visible;
  isolation: isolate;
  &__header { margin:0; padding:0; height:0; }

  &__controls {
    position: absolute;
    left: 0; right: 0;
    top: 50%;
    transform: translateY(-50%);
    z-index: 2;
    pointer-events: none;
  }

  &__ctrl {
    width: 40px; height: 40px; border-radius: 999px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    background: #fff;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.06);
    display: grid; place-items: center;
    cursor: pointer; pointer-events: auto;
    position: absolute; top: 50%;
    transform: translateY(-50%);
  }

  &__chev {
    width: 8px; height: 8px;
    border-top: 2px solid #0a66ff;
    border-right: 2px solid #0a66ff;
    transform: rotate(45deg);

    &--left  { transform: rotate(-135deg); }
    &--right { transform: rotate(45deg); }
  }

  &__ctrl--prev { left: -56px; }
  &__ctrl--next { right: -56px; }

  @media (max-width:1199.98px){ &__ctrl--prev{left:-32px} &__ctrl--next{right:-32px} }
  @media (max-width: 991.98px){ &__ctrl--prev{left:-12px} &__ctrl--next{right:-12px} }
@media (min-width: 1200px) {
  .lac__grid {
    display: flex;
    justify-content: center;
    align-items: stretch;
    gap: 24px;
  }
  .lac__item {
    --col: calc((100% - (4 - 1) * 24px) / 4);
    flex: 0 0 var(--col);
    max-width: var(--col);
  }
  .lac__ghost {
    visibility: hidden;
  }
}
@media (min-width: 1200px) {
  .lac__grid {
    display: flex;
    justify-content: center;
    align-items: stretch;
    gap: 24px;
  }
  .lac__item {
    --col: calc((100% - (4 - 1) * 24px) / 4);
    flex: 0 0 var(--col);
    max-width: var(--col);
  }
}

.lac__grid--center {
  justify-content: center;
}

@media (min-width: 1200px) {
  .lac__ghost { display: none !important; }
}

}

.landing-article{
  position: relative;
  overflow: visible;
  z-index: 0;

  border-radius: 14px;
  background: #fff;
  border: 1px solid rgba(2,18,43,.06);

  box-shadow: none;

  transition: transform .22s ease, border-color .22s ease, box-shadow .22s ease;
}

.landing-article::after{
  content: "";
  position: absolute;
  left: 22px; 
  right: 22px;
  bottom: -14px;
  height: 18px;
  border-radius: 999px;
  pointer-events: none;

  background: radial-gradient(50% 60% at 50% 40%,
              rgba(2,18,43,.22) 0%,
              rgba(2,18,43,.14) 35%,
              rgba(2,18,43,.08) 60%,
              rgba(2,18,43,0) 100%);

  opacity: 0;   
  transform: translateY(6px);
  transition: opacity .25s ease, transform .25s ease;
  z-index: -1;
}

.landing-article:hover{
  transform: translateY(-3px);
  border-color: rgba(2,18,43,.10);
  box-shadow: none;
}
.landing-article:hover::after{
  opacity: .9;
  transform: translateY(0);
}


.lac__carousel{
  position: relative;
  z-index: 1;
}

.lac__controls{
  position: absolute;
  inset: 0;    
  height: auto; 
  transform: none; 
  top: 0;  
  z-index: 10;  
  pointer-events: none;
}


:host ::ng-deep .lac__carousel .carousel-control-prev,
:host ::ng-deep .lac__carousel .carousel-control-next,
:host ::ng-deep .lac__carousel .carousel-indicators { display: none !important; }
.lac__grid--center{
  justify-items: center;
}
@media (max-width: 575.98px){
  .lac__grid--center{ justify-items: center; }
}
/* Responsive */
@media (max-width:1199.98px){ .lac__grid{ grid-template-columns: repeat(2, minmax(0,1fr)); } }
@media (max-width: 991.98px){ .lac__grid{ grid-template-columns:repeat(2,1fr); } }
@media (max-width: 575.98px){ .lac__grid{ grid-template-columns:1fr; } }

:host{
  --lac-title-fs: 16px;
  --lac-title-lh: 24px;
  --lac-title-lines: 3;
  --lac-title-block-h: calc(var(--lac-title-lh) * var(--lac-title-lines));
}

:host ::ng-deep app-landing-article
  .landing-article__title,
:host ::ng-deep app-landing-article
  .card-title,
:host ::ng-deep app-landing-article
  .title,
:host ::ng-deep app-landing-article
  h1, 
:host ::ng-deep app-landing-article
  h2,
:host ::ng-deep app-landing-article
  h3,
:host ::ng-deep app-landing-article
  h4,
:host ::ng-deep app-landing-article
  h5,
:host ::ng-deep app-landing-article
  h6,
:host ::ng-deep app-landing-article
  a.title,
:host ::ng-deep app-landing-article
  .post-title {
  font-size: var(--lac-title-fs);
  line-height: var(--lac-title-lh);

  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: var(--lac-title-lines);
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;

  min-height: var(--lac-title-block-h);
  max-height: var(--lac-title-block-h);
}

:host ::ng-deep app-landing-article .landing-article__title a{
  display: -webkit-box !important;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: var(--lac-title-lines);
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: var(--lac-title-block-h);
  max-height: var(--lac-title-block-h);
  line-height: var(--lac-title-lh);
}


@media (min-width: 1200px) {
  .lac__grid {
    display: flex;
    justify-content: center;     /* căn giữa dãy thẻ */
    align-items: stretch;
    gap: 24px;
  }

  .lac__item {
    /* 4 cột → có 3 khoảng gap = 3*24px */
    --col: calc((100% - (4 - 1) * 24px) / 4);
    flex: 0 0 var(--col);
    max-width: var(--col);
  }
}