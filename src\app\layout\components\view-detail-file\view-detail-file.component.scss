/* Container c<PERSON><PERSON> bảng */
.class-85vh {
  height: 85vh;
}

.document-table-container {
  width: 100%;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  font-family: Arial, sans-serif;
}

/* C<PERSON>u hình bảng */
.document-table {
  width: 100%;
  border-collapse: collapse;
}

.document-table td {
  padding: 8px 12px;
  border: 1px solid #ddd;
}

/* Cột tiêu đề */
.document-table__label {
  background-color: #f8f9fa;
  font-weight: bold;
  width: 25%;
}

/* Cột nội dung */
.document-table__value {
  width: 25%;
}
.document-table__value2 {
  width: 100%;
}

/* Trạng thái hiệu lực - <PERSON><PERSON>u sắc theo status */
.document-status {
  font-weight: bold;
  padding: 10px;
  border-radius: 4px;
}

/* Còn hiệu lực */
.document-status--active {
  background-color: rgba(220, 255, 220, 1);
  color: rgba(25, 135, 84, 1);
}

/* Hết hiệu lực một phần */
.document-status--warning {
  background-color: rgba(255, 245, 200, 1);
  color: rgba(255, 165, 0, 1);
}

/* Hết hiệu lực toàn bộ */
.document-status--danger {
  background-color: rgba(255, 230, 230, 1);
  color: rgba(220, 53, 69, 1);
}

/* Màu mặc định nếu không khớp với các trạng thái trên */
.document-status--info {
  background-color: rgba(230, 245, 255, 1);
  color: rgba(13, 110, 253, 1);
}
.document-status__label {
  margin-right: 5px;
}

/* Trạng thái hiệu lực - Màu sắc theo status */
.document-status__value {
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 4px;
}

.header-luocdo {
  background-color: rgba(245, 245, 245, 1);
}
::ng-deep.accordion .card .card-header button {
  background-color: rgba(245, 245, 245, 1) !important;
}
.doc-container {
  position: relative;
}

.loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.8);
  padding: 10px 20px;
  border-radius: 5px;
  font-size: 16px;
  font-weight: bold;
}
#detail-file .accordion .card .card-header button {
  background: rgba(245, 245, 245, 1) !important;
}
.invalid-date-tong-quan {
  background-color: #ffe6e6;
}
@keyframes highlightFade {
  0% {
    background-color: yellow;
  }
  100% {
    background-color: transparent;
  }
}

.highlight-scroll {
  animation: highlightFade 4s ease forwards;
}
.font-weight-bold {
  font-weight: bold !important;
}
:host [contenteditable="true"] {
  border: 1px dashed #ccc !important;
  padding: 4px !important;
  min-height: 24px !important;
}
.popover-box {
  position: absolute;
  background-color: white;
  border: 1px solid #ccc;
  // padding: 6px 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  border-radius: 6px;
  z-index: 999;
}
.function-item {
  padding: 10px 25px;
}
.function-item:hover {
  background-color: #f0f0f0;
}
.gradient-text {
  background: linear-gradient(
    to left,
    rgba(0, 97, 255) 0%,
    rgba(0, 97, 255, 0.8) 45%,
    rgb(0, 97, 255, 0) 50%,
    rgba(0, 97, 255, 0.8) 55%,
    rgba(0, 97, 255) 100%
  );
  background-size: 200% auto;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  animation: textShine 3s linear infinite;
}
@keyframes textShine {
  0% {
    background-position: -100% center;
  }
  100% {
    background-position: 100% center;
  }
}

.view-detail-file-header-border {
  border-bottom: 1px solid rgba(224, 224, 224, 1);
}

.view-detail-file-save-button {
  min-width: 70px;
}

.spinner-saving {
  max-height: 29.6px;
}

.view-detail-file-content-container {
  height: 80vh;
  display: flex;
}

.view-detail-file-content-wrapper {
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1 1 auto;
  background: linear-gradient(
    to bottom,
    #e6f0f8 0px,
    #ffffff 80px,
    #ffffff
  );
}

.view-detail-file-popover {
  position: absolute;
  z-index: 9999;
}

.view-detail-file-doc-viewer {
  height: 75vh;
}

.view-detail-file-summary-wrapper {
  position: relative;
  overflow-y: auto;
  overflow-x: hidden;
  background: linear-gradient(
    to bottom,
    #e6f0f8 0px,
    #ffffff 80px,
    #ffffff
  );
}

.view-detail-file-summary-content {
  white-space: pre-wrap;
  word-wrap: break-word;
}



.view-detail-file-summary-content p {
  margin: 0;      /* thu nhỏ khoảng cách giữa các mục */
  
}


.view-detail-file-luocdo-container {
  height: 80vh;
  display: flex;
  flex-direction: column;
}

.view-detail-file-luocdo-content {
  flex: 1 1 auto;
  overflow-y: auto;
  overflow-x: hidden;
}

.view-detail-file-lienquan-container {
  height: 75vh;
  display: flex;
  flex-direction: column;
}

.view-detail-file-lienquan-content {
  overflow-y: auto;
  overflow-x: hidden;
  flex: 1 1 auto;
}

.view-detail-file-table-container {
  overflow: auto;
  max-height: 70vh;
}

.view-detail-file-dothi-container {
  height: 80vh;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
}

.custom-tooltip {
  background: rgba(0, 0, 0, 0.85);
  color: #fff;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  max-width: 320px;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
  opacity: 0.95;
  line-height: 1.4;
  transition: opacity 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
}

.custom-tooltip > :not(.tooltip-hint) {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  // white-space: normal;
  white-space: pre-line;
}

.tooltip-hint {
  margin-top: 4px;
  font-size: 12px;
  color: #ccc;
  font-style: italic;
  flex-shrink: 0;
}

.reference-modal-body .reference-content .accordion .card {
  box-shadow: none;
}

.reference-modal-body .reference-content .accordion .card svg {
  min-height: 24px;
  min-width: 24px;
}

.reference-modal-body .reference-content .accordion .card .card-header button {
  padding-left: 0;
}

.reference-modal-body .reference-content .accordion .card-body {
  padding: 0;
}

.reference-modal-body .reference-content .accordion .ref-title {
  color: black;
}

.reference-modal-body .reference-content .accordion .ref-content {
  padding: .5rem;
  border: 1px solid #ddd;
  border-radius: 6px;
}

.ref-text-scroll {
  text-decoration: underline !important;
}
