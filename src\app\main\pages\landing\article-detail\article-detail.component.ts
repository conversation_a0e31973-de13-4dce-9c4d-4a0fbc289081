import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener, Inject } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { Subject, of } from 'rxjs';
import { catchError, switchMap, takeUntil } from 'rxjs/operators';
import { <PERSON>Sanitizer, SafeHtml } from '@angular/platform-browser';
import { environment } from 'environments/environment';
import { CoreConfigService } from '@core/services/config.service';
import { Location, DOCUMENT } from '@angular/common';  

@Component({
  selector: 'app-article-detail',
  templateUrl: './article-detail.component.html',
  styleUrls: ['./article-detail.component.scss']
})
export class ArticleDetailComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  articleId: number | null = null; 
  title = '';
  topic = ''; 
  description = '';
  date = '';
  coverImage: string | null = null;
  htmlContent: SafeHtml | '' = '';
  notFound = false;
  isScrolled = false;

  private readonly API_BASE = (environment.apiUrl || '').replace(/\/+$/, '');
  private readonly CMS_LIST = `${this.API_BASE}/cms/cms_content`;
  public urls = {
    url1: "https://drive.google.com/drive/folders/1kMQUNE7zD7K277-F6Kh7dnl6rh8NqpWC?usp=drive_link",
    url2: "https://forms.gle/xB7ao9UjBKNPFt8h7",
  };
  private equalsSlug(a?: string, b?: string): boolean {
    if (!a || !b) return false;
    return a.normalize('NFC') === b.normalize('NFC');
  }

  private _oldConfig: any;

  constructor(
    private route: ActivatedRoute,
    private http: HttpClient,
    private sanitizer: DomSanitizer,
    private router: Router,
    private _coreConfig: CoreConfigService,
    private location: Location,
    @Inject(DOCUMENT) private document: Document ,
  ) {}

  @HostListener('window:scroll')
  onWindowScroll(): void {
    this.isScrolled = window.scrollY > 4;
  }

  goLandingSection(
    section: 'chatbot'|'information'|'mission'|'articles'|'contact'
  ){
    this.router.navigate(
      ['/pages/landing'],
      { fragment: section }
    );
  }

  ngOnInit(): void {
    this.document.body.classList.add('article-detail-page');
    this._oldConfig = this._coreConfig.config;
    this._coreConfig.setConfig(
      {
        layout: {
          menu: { hidden: true },
          navbar: { hidden: true },
          footer:  { type: 'hidden' },
          customizer: false,
          contentWidth: 'full'
        }
      },
      { emitEvent: true }
    );

    this.route.paramMap
      .pipe(
        switchMap(params => {
          const slug = params.get('slug') || '';
          const asNumber = Number(slug);

          // Nếu slug là số ⇒ lấy theo id
          if (!isNaN(asNumber) && String(asNumber) === slug) {
            return this.http
              .get<any>(`${this.CMS_LIST}/${asNumber}`)
              .pipe(catchError(() => of(null)));
          }

          // Nếu slug là text ⇒ tìm theo slug
          return this.route.queryParamMap.pipe(
            switchMap(qp => {
              const allowPreview = qp.get('preview') === '1';

              return this.http.get<any[]>(this.CMS_LIST).pipe(
                catchError(() => of([])),
                switchMap(list => {
                  const slugParam = (params.get('slug') || '').normalize('NFC');
                  let candidates = Array.isArray(list) ? list : [];

                  if (!allowPreview) {
                    candidates = candidates.filter(x => x.status === 'PUBLISHED');
                  }

                  const found =
                    candidates.find(
                      x => (x?.slug || '').normalize('NFC') === slugParam
                    ) || null;

                  return of(found);
                })
              );
            })
          );
        }),
        takeUntil(this.destroy$)
      )
      .subscribe(data => {
        if (!data) {
          this.notFound = true;
          return;
        }

        const apiOrigin = new URL(this.API_BASE).origin;
        const d = data.created_at ? new Date(data.created_at) : null;

        this.date = d
          ? `${('0' + d.getDate()).slice(-2)}/${(
              '0' +
              (d.getMonth() + 1)
            ).slice(-2)}/${d.getFullYear()}`
          : '';

        const img = data.image
          ? /^https?:\/\//.test(data.image)
            ? data.image
            : `${apiOrigin}${data.image}`
          : null;

        this.articleId = data.id ?? null;
        this.title = data.title || '';
        this.topic       = data.topic || '';
        this.description = data.description || '';
        this.coverImage = img;

        this.htmlContent = this.sanitizer.bypassSecurityTrustHtml(
          data.body || ''
        );
      });
  }

  backToLanding() {
    this.router.navigate(
      ['/pages/landing'],
      {
        fragment: 'articles',
        queryParams: this.articleId ? { focus: this.articleId } : {}
      }
    );
  }

  ngOnDestroy(): void {
    this.document.body.classList.remove('article-detail-page');
    if (this._oldConfig) {
      this._coreConfig.setConfig(this._oldConfig, { emitEvent: true });
    }
    this.destroy$.next();
    this.destroy$.complete();
  }
}
