import {
  Compo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  HostListener,
  ViewEncapsulation,
} from "@angular/core";
import { Router, NavigationEnd } from "@angular/router";

import { Subject } from "rxjs";
import { take, takeUntil, filter } from "rxjs/operators";
import { PerfectScrollbarDirective } from "ngx-perfect-scrollbar";

import { CoreConfigService } from "@core/services/config.service";
import { CoreMenuService } from "@core/components/core-menu/core-menu.service";
import { CoreSidebarService } from "@core/components/core-sidebar/core-sidebar.service";
import { getAppName as getAppNameHelper } from "app/shared/image.helper";

@Component({
  selector: "vertical-menu",
  templateUrl: "./vertical-menu.component.html",
  styleUrls: ["./vertical-menu.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class VerticalMenuComponent implements OnInit, OnD<PERSON>roy {
  coreConfig: any;
  menu: any;
  isCollapsed: boolean;
  isScrolled: boolean = false;
  getAppName = getAppNameHelper

  // Private
  private _unsubscribeAll: Subject<any>;

  /**
   * Constructor
   *
   * @param {CoreConfigService} _coreConfigService
   * @param {CoreMenuService} _coreMenuService
   * @param {CoreSidebarService} _coreSidebarService
   * @param {Router} _router
   */
  constructor(
    private _coreConfigService: CoreConfigService,
    private _coreMenuService: CoreMenuService,
    private _coreSidebarService: CoreSidebarService,
    private _router: Router
  ) {
    // Set the private defaults
    this._unsubscribeAll = new Subject();
  }

  @ViewChild(PerfectScrollbarDirective, { static: false })
  directiveRef?: PerfectScrollbarDirective;
  private _savedHiddenBeforeCms: boolean | null = null;
  private _baseMenu: any[] = [];
  private _cmsMenu: any[]  = [];

  // Lifecycle Hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * On Init
   */
  ngOnInit(): void {
    // Subscribe config change
    this._coreConfigService.config
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((config) => {
        this.coreConfig = config;
        
        // ✅ Cập nhật isCollapsed khi config thay đổi
        if (config.layout && config.layout.menu) {
          this.isCollapsed = config.layout.menu.collapsed;
        }
      });

    // ✅ Lấy trạng thái collapsed ban đầu từ sidebar
    const sidebar = this._coreSidebarService.getSidebarRegistry('menu');
    if (sidebar) {
      this.isCollapsed = sidebar.collapsed;
    }

    // Close the menu on router NavigationEnd (Required for small screen to close the menu on select)
    this._router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this._unsubscribeAll)
      )
      .subscribe(() => {
        const menuSidebar = this._coreSidebarService.getSidebarRegistry('menu');
        if (menuSidebar) {
          menuSidebar.close();
        }
      });

    // Scroll to active on navigation end
    this._router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        take(1)
      )
      .subscribe(() => {
        setTimeout(() => {
          this.directiveRef.scrollToElement('.navigation .active', -180, 500);
        });
      });

    // Get current menu
    this._coreMenuService.onMenuChanged
      .pipe(filter(v => v !== null), takeUntil(this._unsubscribeAll))
      .subscribe(() => {
        const current = this._coreMenuService.getCurrentMenu() || [];
        this.menu = current;

        if (!this._baseMenu.length && !this._cmsMenu.length) {
          const { base, cms } = this._splitMenuByCms(current);
          this._baseMenu = base;
          this._cmsMenu  = cms;
          // áp menu phù hợp route hiện tại
          this._applyCmsSidebarRule(this._router.url);
        }
      });
    this._applyCmsSidebarRule(this._router.url);
    this._router.events
      .pipe(filter(e => e instanceof NavigationEnd), takeUntil(this._unsubscribeAll))
      .subscribe((e: NavigationEnd) => this._applyCmsSidebarRule(e.urlAfterRedirects || e.url));
  }

  /**
   * On Destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }

  // Public Methods
  // -----------------------------------------------------------------------------------------------------
  private _isCms(url: string): boolean {
    return /(^|\/)cms(\/|$|\?|#)/.test(url);
  }
  private _isPublic(url: string): boolean {
    const u = url.replace(/^\/cls(?=\/)/i, '');
    const isLanding  = /(^|\/)(pages\/)?landing(\/|$|\?|#)/i.test(u);
    const isHome     = /(^|\/)home(\/|$|\?|#)/i.test(u);
    const isAuth     = /authentication|auth\-login|auth\-register|login|register/i.test(u);
    const isMiscErr  = /(^|\/)(pages\/)?miscellaneous\/error(\/|$|\?|#)/i.test(u);
    const isArticles = /(^|\/)(pages\/)?articles(\/|$|\?|#)/i.test(u);
    const isArticle  = /(^|\/)(pages\/)?article(\/|$|\?|#)/i.test(u);
    return isLanding || isHome || isAuth || isMiscErr || isArticles || isArticle;
  }

  private _lastMenuSig = '';
  private _deferSetConfig(next: any) {
    Promise.resolve().then(() => {
      this._coreConfigService.setConfig(next, { emitEvent: true });
    });
  }
  private _splitMenuByCms(items: any[]): { base: any[]; cms: any[] } {
    const isCmsItem = (it: any): boolean => {
      if (Array.isArray(it?.children) && it.children.length) {
        return it.children.every((c: any) => isCmsItem(c));
      }
      const url = (it?.url || it?.path || "").toString();
      return url.startsWith("/cms/");
    };
    const clone = (x: any) => JSON.parse(JSON.stringify(x ?? []));
    const base: any[] = [], cms: any[] = [];
    (items || []).forEach((it) => {
      if (Array.isArray(it?.children) && it.children.length) {
        const kidsBase: any[] = [], kidsCms: any[] = [];
        it.children.forEach((ch: any) => (isCmsItem(ch) ? kidsCms.push(ch) : kidsBase.push(ch)));
        if (kidsBase.length) base.push({ ...it, children: clone(kidsBase) });
        if (kidsCms.length)  cms.push({ ...it, children: clone(kidsCms)  });
      } else {
        (isCmsItem(it) ? cms : base).push(clone(it));
      }
    });
    return { base, cms };
  }
  private _setMenu(items: any[]) {
    const sig = JSON.stringify(items ?? []);
    if (sig === this._lastMenuSig) return;   // menu không đổi -> bỏ
    this._lastMenuSig = sig;

    const svc: any = this._coreMenuService as any;

    if (typeof svc.setMenu === 'function') {
      svc.setMenu('main', items);
      return;
    }
    // Fallback SDK cũ
    try {
      if (typeof svc.isRegistered === 'function' ? svc.isRegistered('main') : true) {
        svc.unregister?.('main');
      }
    } catch {}
    svc.register?.('main', items);
    svc.setCurrentMenu?.('main');
  }
  private _applyCmsSidebarRule(url: string) {
    const isCms = this._isCms(url);
    const currentHidden = !!this.coreConfig?.layout?.menu?.hidden;

    if (isCms) {
      if (this._savedHiddenBeforeCms === null) this._savedHiddenBeforeCms = currentHidden;
      if (currentHidden) this._deferSetConfig({ layout: { menu: { hidden: false } } });
      this._setMenu([...this._baseMenu, ...this._cmsMenu]);   // chỉ cộng thêm nhóm CMS khi ở /cms/**
      return;
    }

    // Rời CMS
    const isPublic = this._isPublic(url);
    if (isPublic) {
      if (!currentHidden) this._deferSetConfig({ layout: { menu: { hidden: true } } });
    } else if (this._savedHiddenBeforeCms !== null && currentHidden !== this._savedHiddenBeforeCms) {
      this._deferSetConfig({ layout: { menu: { hidden: this._savedHiddenBeforeCms } } });
    }
    this._savedHiddenBeforeCms = null;

    // Bỏ nhóm CMS khi không ở /cms/**
    this._setMenu([...this._baseMenu]);
  }
  /**
   * On Sidebar scroll set isScrolled as true
   */
  onSidebarScroll(): void {
    if (this.directiveRef.position(true).y > 3) {
      this.isScrolled = true;
    } else {
      this.isScrolled = false;
    }
  }

  /**
   * Toggle sidebar (cho màn hình nhỏ - nút X)
   * Đóng overlay sidebar
   */
  /**
 * Toggle sidebar (màn hình nhỏ - nút X)
 */
  toggleSidebar(): void {
    const sidebar = this._coreSidebarService.getSidebarRegistry('menu');
    // console.log("gohere")
    if (!sidebar) {
      console.warn('Menu sidebar not found');
      return;
    }

    // ✅ Sử dụng forceClose thay vì close
    if (sidebar['forceClose']) {
      sidebar['forceClose']();
      // console.log("forceClose")
    } else {
      sidebar.close(); // Fallback
    }
  }

  /**
   * Toggle sidebar collapsed status (cho màn hình lớn)
   * Collapse/Expand menu chính
   */
  toggleSidebarCollapsible(): void {
    const sidebar = this._coreSidebarService.getSidebarRegistry('menu');
    
    if (!sidebar) {
      console.warn('Menu sidebar not found in registry');
      return;
    }

    // Toggle trạng thái collapsed
    const newCollapsedState = !this.isCollapsed;
    
    // Cập nhật config
    this._coreConfigService.setConfig(
      { layout: { menu: { collapsed: newCollapsedState } } },
      { emitEvent: true }
    );
    
    // Cập nhật local state
    this.isCollapsed = newCollapsedState;
    
    // Toggle sidebar
    sidebar.toggleCollapsible();
  }
}
