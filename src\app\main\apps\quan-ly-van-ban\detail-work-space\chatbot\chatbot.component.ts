import {
  Compo<PERSON>,
  <PERSON>ement<PERSON>ef,
  HostListener,
  Input,
  OnInit,
  QueryList,
  Renderer2,
  ViewChild,
  ViewChildren,
  ViewEncapsulation,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ViewDetailFileService } from "app/layout/components/view-detail-file/view-detail-file.service";
import { FormType } from "app/models/FormType";
import { ShowContent } from "app/models/ShowContent";
import { TypeDocument } from "app/models/TypeDocument";
import { environment } from "environments/environment";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { filter, switchMap, takeUntil, tap } from "rxjs/operators";
import Swal from "sweetalert2";
import { randomUuidv4 } from "../../../../../../../util/randomUUID";
import { DetailWorkSpaceService } from "../detail-work-space.service";
import { ListDocumentService } from "../list-document/list-document.service";
import { ChatbotService } from "./chatbot.service";
import { TakeNoteService } from "../take-note/take-note.service";

import {
  Document as DocxDocument,
  Packer,
  Paragraph,
  HeadingLevel,
  TextRun,
  Table,
  TableRow,
  TableCell,
  WidthType,
  BorderStyle,
  AlignmentType,
  VerticalAlign,
} from "docx";
@Component({
  selector: "app-chatbot",
  templateUrl: "./chatbot.component.html",
  styleUrls: ["./chatbot.component.scss"],
  encapsulation: ViewEncapsulation.None
})
export class ChatbotComponent implements OnInit {
  @ViewChild('chatContainer') private chatContainer!: ElementRef<HTMLDivElement>;
  @ViewChild("viewFileModal") public viewFileModal: any;
  @ViewChildren("message") messageElements: QueryList<ElementRef>;
  @ViewChild("lastMessage") lastMessage!: ElementRef;
  @ViewChildren("replyMessage") replyMessages!: QueryList<ElementRef>;
  @ViewChild("answerContainer") answerContainer!: ElementRef;
  @Input("modal") public modal: NgbActiveModal;
  @Input("type") public type: FormType;
  private lastMouse = { x: 0, y: 0 };
  @HostListener('document:mouseup', ['$event'])
  captureMouse(e: MouseEvent) {
    this.lastMouse = { x: e.clientX, y: e.clientY };
  }
  private _nodeIn(el: HTMLElement, node: Node): boolean {
    const target = node.nodeType === Node.TEXT_NODE ? (node.parentElement as Node) : node;
    return el.contains(target);
  }
  listConversation = [];
  groupedConversations: { label: string; items: any[] }[] = [];
  groupedConversationsFilter: { label: string; items: any[] }[] = [];
  public types: FormType;
  messages = [];
  userInput = "";
  public isMaximized: boolean = false;
  public thinkingText: string = "";
  public answerText: string = "";
  public idMessage: number = 0;
  public bodyMessage: {
    role: string;
    content: string;
  }[] = [];
  abortController: AbortController | null = null;
  public selectedConversation: any = null;
  public checkDoneAnswer: boolean = true;
  public unSubAll: Subject<any> = new Subject();
  public workspaceId: string = "";
  public quota: number = 0;
  contextMenuPosition = { x: 0, y: 0 };
  public contextMenuVisible = false;
  public contextMenuItem: any = null;
  public isEditChatbotName: boolean = false;
  public searchConversationString: string = "";
  public statusThinking: string = "";
  public showScrollButton = false;
  private autoScroll: boolean = true; // Track if auto-scroll is enabled
  public listTool = [
    {
      value: 1,
      label: "Truy vấn kho VBQPPL",
      icon: "assets/images/icons/database.svg",
    },
    {
      value: 2,
      label: "Google tìm kiếm",
      icon: "assets/images/icons/database.svg",
    },
    // {
    //   value: 2,
    //   label: "Tìm kiếm sâu",
    //   icon: "assets/images/icons/deep-search.svg",
    // },
  ];
  public checkDoneThinking = false;
  public toolSelected = [
    {
      value: 1,
      label: "Truy vấn kho VBQPPL",
      icon: "assets/images/icons/database.svg",
      image: "assets/images/icons/database-danger.svg",
    },
  ];
  public textAskChatbot: string = null;
  public listDocument: any = [];
  public selectedDocuments: boolean[] = [];
  public selectedFile: any[] = []; // để lưu file được chọn
  public isHasTextFromVanBan: boolean = false;
  chatHeight: number = 0;
  public selection_text: string = null;
  public selected_save_files = [];
  public selected_upload_files = [];
  public workSpaceName: string = "";
  public fileId: string;
  public es_id: string;
  public typeDocument: string;
  public isSpeechToText: boolean = false;
  public idMessageSpeechToText: string = null;
  isMobileSidebarOpen: boolean = false;
  private selectedDocumentKeys = new Set<string>();
  public isCreativeMode: boolean = false; // Chế độ sáng tạo
  constructor(
    private workSpaceService: DetailWorkSpaceService,
    private chatbotService: ChatbotService,
    private route: ActivatedRoute,
    private toast: ToastrService,
    private viewDetailFileService: ViewDetailFileService,
    private router: Router,
    private listDocumentService: ListDocumentService,
    private modalService: NgbModal,
    private renderer: Renderer2,
    private noteService: TakeNoteService,
  ) {}
  @HostListener("document:keydown.escape", ["$event"])
  handleEscapeKey(event: KeyboardEvent) {
    if (this.type != FormType.Chatbot) this.isMaximized = false; //điều kiện này là tránh cho lúc xem văn bản từ chatbot ngoài không gian dự án
  }

  private getSelectedHtmlIn(container: HTMLElement): string | null {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) return null;

    const range = selection.getRangeAt(0);
    const common = range.commonAncestorContainer;
    if (!container.contains(common)) return null;

    const frag = range.cloneContents();
    const div = document.createElement("div");
    div.appendChild(frag);
    const html = div.innerHTML.trim();

    // loại rỗng (chỉ tag)
    const textOnly = html.replace(/<[^>]*>/g, "").trim();
    if (!textOnly) return null;

    // chặn ảnh vì addNote() đang không cho ảnh
    if (/<img\s/i.test(html)) return null;

    return html;
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.workspaceId = params["id"];
    });
    this.route.queryParams.subscribe((params) => {
      this.workSpaceName = params["workSpaceName"];
      this.fileId = params["fileId"];
      this.es_id = params["es_id"];
      this.typeDocument = params["type"];
    });
    this.workSpaceService.isMaximized
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        this.isMaximized = res;
      });
    if (this.type == FormType.Chatbot) {
      this.isMaximized = true;
      const workspace_id = localStorage.getItem("workspace_id");
      this.workspaceId = workspace_id || "";
    }
    this.chatbotService.textFormVanBan // text khi bôi đen trong toàn văn, người dùng bấm hỏi chatbot
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        if (res) {
          this.isHasTextFromVanBan = false;
          this.selection_text = res;
          // console.log(this.selection_text);

          // this.selectedConversation = null;
          // this.messages = [];
        } else {
          this.loadConversationsWithMessages();
        }
      });
    this.chatbotService.textBoiDen // text khi bôi đen trong toàn văn , k bấm hỏi chatbot
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        if (res) {
          this.isHasTextFromVanBan = true;
          this.selection_text = res;
        } else {
          this.isHasTextFromVanBan = false;
        }
      });
    this.chatbotService.listDocument
      .pipe(takeUntil(this.unSubAll))
      .subscribe((res) => {
        if (Array.isArray(res)) {
          this.listDocument = res.filter((item: any) => item.status == 1);
          const availableKeys = new Set(
            this.listDocument
              .map((doc: any) => this.getDocumentKey(doc))
              .filter((key): key is string => !!key)
          );
          // Remove selections that are no longer available
          this.selectedDocumentKeys.forEach((key) => {
            if (!availableKeys.has(key)) {
              this.selectedDocumentKeys.delete(key);
            }
          });
        } else {
          this.listDocument = []; // fallback nếu res null
          this.selectedDocumentKeys.clear();
        }
        this.syncSelectedDocuments();
      });


    this.getQuota();
    this.autoScroll = true; // Enable auto-scroll on init
    this.syncSelectedDocuments();
    this.setupResizeListener();
    const ua = navigator.userAgent || '';
    const platform = (navigator as any).userAgentData?.platform || navigator.platform || '';
    const isMobileUA = /Mobi|Android/i.test(ua);
    const isAndroidUA = /Android/i.test(ua);
    const isIOSUA = /iPhone|iPad|iPod/i.test(ua);

    const isAndroidPlatform =
      platform.toLowerCase().includes('android') ||
      (platform.toLowerCase().includes('linux') && 'ontouchstart' in window);

    const isIOSPlatform =
      platform.toLowerCase().includes('ios') ||
      (platform.toLowerCase().includes('mac') && navigator.maxTouchPoints > 1);

    if (isAndroidUA || isAndroidPlatform) {
      this.renderer.addClass(document.body, 'is-android');
      // console.log('Detected Android device ✅');
    } else if (isIOSUA || isIOSPlatform) {
      this.renderer.addClass(document.body, 'is-ios');
      // console.log('Detected iOS device ✅');
    } else {
      // console.log('Desktop or unknown device 🖥️', { ua, platform });
    }
  }
  getOfficeIcon(document) {
    if (document?.types == TypeDocument.CSDL) {
      return "assets/images/icons/file-search.svg";
    } else if (document?.types == TypeDocument.FromSearch) {
      return "assets/images/icons/search.svg";
    } else {
      const fileName = document?.name;
      let fileExtension = null;

      if (typeof fileName === "string" && fileName.lastIndexOf(".") !== -1) {
        fileExtension = fileName.substring(fileName.lastIndexOf(".") + 1);
      }

      if (fileExtension == "docx") return "assets/images/icons/docx.svg";
      if (fileExtension == "pdf") return "assets/images/icons/pdf.svg";
      if (fileExtension == "doc") return "assets/images/icons/doc.svg";
      return "assets/images/icons/docx.svg";
    }
  }
  getQuota() {
    this.chatbotService.getQuota().subscribe((res) => {
      this.quota = res.limit_request - res.request_count;
    });
  }
  loadConversationsWithMessages() {
    this.chatbotService
      .getConversation(this.workspaceId)
      .pipe(
        takeUntil(this.unSubAll),
        tap((res: any) => {
          this.listConversation = res;
          if (this.listConversation.length == 0) this.messages = [];
          this.groupConversations();
          this.selectedConversation = res[0];
        }),
        filter((res: any) => res.length > 0),
        switchMap((res: any) => {
          const firstConversationId = res[0].id;
          return this.chatbotService.getMessageChatbot(firstConversationId);
        }),
        tap((messageRes: any) => {
          this.messages = messageRes.messages.map((msg) => {
            // Tìm vị trí <answer>
            const idx = msg.answer.indexOf("<answer>");
            if (idx !== -1) {
              // Kiểm tra ký tự ngay sau <answer>
              const after = msg.answer.substring(idx + 8, idx + 9); // 8 là độ dài '<answer>'
              if (after !== "\n") {
                // Nếu chưa có \n, chèn vào
                msg.answer = msg.answer.replace("<answer>", "<answer>\n\n");
              }
            }
            return msg;
          });

          setTimeout(() => {
            this.scrollToBottom();
          }, 10);
        })
      )
      .subscribe();
  }
  groupConversations() {
    const now = new Date();
    const today: any[] = [];
    const last7Days: any[] = [];
    const last30Days: any[] = [];

    for (const item of this.listConversation) {
      const updateAt = new Date(
        item.updated_at ? item.updated_at : item.created_at
      );
      const diffInDays = Math.floor(
        (now.setHours(0, 0, 0, 0) - updateAt.setHours(0, 0, 0, 0)) /
          (1000 * 60 * 60 * 24)
      );
      if (diffInDays === 0) {
        today.push(item);
      } else if (diffInDays > 0 && diffInDays < 7) {
        last7Days.push(item);
      } else if (diffInDays >= 7 && diffInDays < 30) {
        last30Days.push(item);
      }
    }

    // Sắp xếp từng nhóm từ mới nhất đến cũ nhất
    const sortDesc = (a: any, b: any) =>
      new Date(b.updated_at ? b.updated_at : b.created_at).getTime() -
      new Date(a.updated_at ? a.updated_at : a.created_at).getTime();
    today.sort(sortDesc);
    last7Days.sort(sortDesc);
    last30Days.sort(sortDesc);

    this.groupedConversations = [
      { label: "Hôm nay", items: today },
      { label: "7 ngày trước", items: last7Days },
      { label: "30 ngày trước", items: last30Days },
    ];
    this.groupedConversationsFilter = this.groupedConversations;
  }

  async streamFromChatbot() {
    this.abortController = new AbortController();
    this.bodyMessage = this.messages.map((msg) => ({
      role: msg.role,
      content: msg.answer,
    }));

    try {
      let content = null;
      if (this.textAskChatbot) {
        content = {
          content: this.textAskChatbot,
          original_doc_type:
            this.typeDocument == "upload"
              ? "uploaded"
              : this.typeDocument == "searching"
              ? "database"
              : this.typeDocument == "search"
              ? "saved"
              : null,
          id_document: this.es_id ? this.es_id : this.fileId || null,
        };
      }
      const response = await fetch(`${environment.apichatbot}/chat`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${localStorage.getItem("token")}`,
        },
        body: JSON.stringify({
          messages: this.bodyMessage,
          do_rewrite: false,
          stream: true,
          workspace_id: this.workspaceId,
          conversation_id: this.selectedConversation
            ? this.selectedConversation.id
            : null,
          selection_text: content,
          selected_upload_files: this.selected_upload_files,
          selected_save_files: this.selected_save_files,
          use_law_database: this.toolSelected.some((item) => item.value === 1),
          use_google_search: this.toolSelected.some((item) => item.value === 2),
          creative_mode: this.isCreativeMode, // Thêm tham số chế độ sáng tạo
        }),
      });

      if (!response.body) {
        console.error("❌ No response body");
        return;
      }
      if (response.ok) {
        if (this.workspaceId) {
          // // console.log("update");
          this.chatbotService.updateTimeWorkspace(this.workspaceId).subscribe();
        }
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder("utf-8");
      let buffer = "";
      let checkStartThinking = false;
      let checkStartAnswer = true; //fix tạm , bình thường là false, do AI kh trả về <answer></answer> nữa
      let thinkingText = "";
      let answerText = "";
      let id = "";
      this.checkDoneAnswer = false;
      this.scrollToBottom2();
      while (true) {
        if (this.abortController.signal.aborted) {
          // console.log("🚫 Đã huỷ stream, dừng vòng lặp.");
          this.checkDoneAnswer = true;
          if (this.quota > 0) this.quota -= 1;

          break;
        }
        const { value, done } = await reader.read();
        if (done) {
          if (this.quota > 0) this.quota -= 1;
          // console.log("✅ Stream complete");
          this.checkDoneAnswer = true;
          // this.selectedFile = [];
          // this.selected_save_files = [];
          // this.selected_upload_files = [];
          this.getMessageOnConversation(this.selectedConversation);
          break;
        }

        buffer += decoder.decode(value, { stream: true });

        // // console.log("json", buffer);
        const [jsonObjects, remaining] = this.extractJsonObjects(buffer);
        buffer = remaining;

        for (const obj of jsonObjects) {
          // // console.log(obj);

          this.checkDoneAnswer = false;
          if (obj.status) {
            this.checkDoneThinking = false;
            this.statusThinking = obj.data.message;
            if (obj.status == "need_clarification") {
              this.checkDoneThinking = true; // tắt tin nhắn thinking đi khi nhận được câu hỏi cần bổ sung thêm
              // // console.log("obj need_declaration", obj.data.message);
              this.messages.push({
                id: id,
                thinking: null,
                answer: obj.data.message,
                role: "assistant",
              });
              // console.log("this.messages", this.messages);

              this.checkDoneAnswer = true;
            }
          } else {
            this.checkDoneThinking = true;
          }

          // khi nhận được conversation_id thì sẽ load lại list hội thoại, TH tạo cuộc hội thoại mới
          if (obj.conversation_id && this.selectedConversation == null) {
            this.selectedConversation = {
              id: obj.conversation_id,
            };
            this.chatbotService
              .getConversation(this.workspaceId)
              .subscribe((res: any) => {
                this.listConversation = res;
                this.groupConversations();
                // this.selectedConversation = res[0];
              });

            // this.loadConversationsWithMessages();
          }
          if (obj.id) {
            id = obj.id;
          }
          let text = "";
          if (obj.text) {
            text = obj.text || "";
          }
          // // Handle thinking
          if (this.checkStartThinking(text)) checkStartThinking = true;
          else if (this.checkStartThinking(text) === false)
            checkStartThinking = false;
          if (checkStartThinking) thinkingText += text;

          // Handle answer  cmt tạm vào, sau này check do AI kh trả về <answer></answer> nữa
          // if (this.checkStartAnswer(text)) {
          //   checkStartAnswer = true;
          // } else if (this.checkStartAnswer(text) === false)
          //   checkStartAnswer = false;

          if (checkStartAnswer) {
            if (text.includes("<answer>")) {
              answerText += text + "\n\n"; // check trường hợp markdown đầu tiên cần thêm 1 cái \n nếu không nó kh nhận
            } else {
              if (answerText === "") {
                answerText += "\n" + text;
              } else {
                answerText += text;
              }
              // answerText += text;
            }
          }

          this.thinkingText = thinkingText;
          this.answerText = answerText;

          // Update to message list
          const index = this.messages.findIndex((m) => m.id === id);
          if (index !== -1) {
            this.messages[index].thinking = this.thinkingText;
            this.messages[index].answer = this.answerText;
            this.messages[index].role = "assistant";
          } else {
            this.messages.push({
              id: id,
              thinking: this.thinkingText,
              answer: this.answerText,
              role: "assistant",
            });
          }
        }
        // Đợi view render tin nhắn phản hồi xong
        setTimeout(() => {
          const replyElements = this.replyMessages.toArray();
          const lastReplyEl = replyElements[replyElements.length - 1];

          if (lastReplyEl) {
            const replyHeight = lastReplyEl.nativeElement.offsetHeight;
            const container = this.chatContainer.nativeElement; // giả sử bạn có ref tới container chat
            const maxScrollTop =
              container.scrollHeight - container.clientHeight; // max scrollTop

            if (replyHeight > this.chatHeight) {
              // Chỉ trừ nếu chiều cao phản hồi lớn hơn phần trắng
              this.chatHeight = Math.max(
                0,
                this.chatHeight - (replyHeight - this.chatHeight)
              );

              this.scrollToBottom();
            }
          }
        }, 50); // Đợi Angular render xong

        // this.scrollToBottom();
      }
    } catch (error) {
      this.checkDoneAnswer = true;
      this.checkDoneThinking = true;
      // this.toast.error("Thất bại", "Có lỗi xảy ra trong quá trình thực hiện!", {
      //   closeButton: true,
      //   positionClass: "toast-top-right",
      //   toastClass: "toast ngx-toastr",
      // });
    } finally {
      this.abortController = null;
    }
  }
  extractJsonObjects(str: string): any[] {
    const objects: any[] = [];
    let depth = 0;
    let start = -1;
    let inString = false;
    let escape = false;

    for (let i = 0; i < str.length; i++) {
      const char = str[i];

      if (char === '"' && !escape) {
        inString = !inString;
      }

      if (!inString) {
        if (char === "{") {
          if (depth === 0) start = i;
          depth++;
        } else if (char === "}") {
          depth--;
          if (depth === 0 && start !== -1) {
            const jsonStr = str.slice(start, i + 1);
            try {
              objects.push(JSON.parse(jsonStr));
            } catch (e) {
              console.warn("⚠️ JSON parse failed", jsonStr);
            }
            start = -1;
          }
        }
      }

      escape = char === "\\" && !escape;
    }

    const remaining = depth > 0 && start !== -1 ? str.slice(start) : "";
    return [objects, remaining];
  }

  checkStartThinking(text) {
    if (text.includes("<think>")) return true;
    else if (text.includes("<answer>")) return false;
  }
  checkStartAnswer(text) {
    if (text.includes("<answer>")) return true;
    else if (text.includes("</answer>")) return false;
  }

  minimumChatbot() {
    this.isMaximized = false;
    this.workSpaceService.isMaximized.next(false);

    // Nếu là màn hình nhỏ thì gọi toggleNotes để thu gọn
    if (this.isSmallScreen()) {
      this.workSpaceService.shouldToggleNotes.next(true);
    }
  }

  // Hàm kiểm tra màn hình nhỏ (dưới 1200px)
  private isSmallScreen(): boolean {
    return window.innerWidth < 1200;
  }

  // Hàm xử lý thay đổi chế độ sáng tạo
  changeTypeSearch(event: Event) {
    const target = event.target as HTMLInputElement;
    this.isCreativeMode = target.checked;
  }
  autoResize(event: Event) {
    const textarea = event.target as HTMLTextAreaElement;
    textarea.style.height = "auto"; // Reset height
    textarea.style.height = textarea.scrollHeight + "px"; // Set to scroll height
  }
  sendMessage(event: KeyboardEvent) {
    if (event.key === "Enter" && !event.shiftKey && this.quota != 0) {
      event.preventDefault(); // Ngăn xuống dòng khi nhấn Enter
      if (this.userInput.trim() && this.checkDoneAnswer) {
        this.textAskChatbot = this.selection_text;
        this.isHasTextFromVanBan = false;
        this.selection_text = null;
        this.checkDoneAnswer = false;

        this.messages.push({
          id: new randomUuidv4().randomUuidv4(),
          thinking: "",
          answer: this.userInput.trim(),
          role: "user",
          selection_text: this.textAskChatbot ? this.textAskChatbot : null,
          selected_files: {
            upload_files: this.selectedFile ? this.selectedFile : null,
          },
        });
        // this.selectedFile = [];
        // this.selected_save_files = [];
        // this.selected_upload_files = [];
        if (this.chatContainer)
          this.chatHeight = this.chatContainer.nativeElement.clientHeight - 100;

        setTimeout(() => this.scrollToBottom(), 10);
        // setTimeout(() => this.scrollToBottom(), 10);
        // setTimeout(() => this.scrollToBottom(), 20);

        this.streamFromChatbot();
        this.userInput = "";
      }
      this.resetTextareaHeight(); // 👉 Gọi reset chiều cao
    }
  }
  clickSendMessage() {
    if (this.userInput.trim() && this.checkDoneAnswer && this.quota != 0) {
      this.checkDoneAnswer = false;
      this.textAskChatbot = this.selection_text;
      this.isHasTextFromVanBan = false;
      this.messages.push({
        id: new randomUuidv4().randomUuidv4(),
        thinking: "",
        answer: this.userInput.trim(),
        role: "user",
        selection_text: this.textAskChatbot ? this.textAskChatbot : null,
        selected_files: {
          upload_files: this.selectedFile ? this.selectedFile : null,
        },
      });
      // this.selectedFile = [];
      // this.selected_save_files = [];
      // this.selected_upload_files = [];
      setTimeout(() => this.scrollToBottom(), 0);
      this.streamFromChatbot();
      this.userInput = "";
      this.selection_text = null;
    }
    this.resetTextareaHeight(); // 👉 Gọi reset chiều cao
  }
  resetTextareaHeight() {
    const textarea = document.getElementById(
      "queryChatbot"
    ) as HTMLTextAreaElement;
    if (textarea) {
      textarea.style.height = "auto"; // Reset lại chiều cao mặc định
    }
  }
  scrollToBottom(): void {
    try {
      if (this.autoScroll) {
        const container = this.chatContainer.nativeElement;
        // Cuộn thẳng xuống cuối cùng
        container.scrollTop = container.scrollHeight;
      }
    } catch (err) {
      console.error("Scroll failed:", err);
    }
  }
  scrollToBottom2(): void {
    try {
      const container = this.chatContainer.nativeElement;
      // Cuộn thẳng xuống cuối cùng
      container.scrollTop = container.scrollHeight;
    } catch (err) {
      console.error("Scroll failed:", err);
    }
  }
  cancelRequest() {
    this.checkDoneAnswer = true;
    if (this.abortController) {
      this.abortController.abort();
    }
  }
  addNewChat() {
    this.messages = []; // Clear messages for new chat
    // this.chatbotService.saveMessageChatbot.next([]);
    this.selectedConversation = null;
    if (window.innerWidth <= 768 && this.isMobileSidebarOpen) {
      this.toggleMobileSidebar();
    }
  }
  isMobileView(): boolean {
    return window.innerWidth <= 768;
  }
  onConversationChange(conversation: any) {
    this.selectedConversation = conversation;
    this.getMessageOnConversation(conversation);
    if (window.innerWidth <= 768) {
      this.toggleMobileSidebar();
    }
  }
  getMessageOnConversation(conversation: any) {
    this.chatbotService
      .getMessageChatbot(conversation.id)
      .pipe(
        takeUntil(this.unSubAll),
        tap((messageRes: any) => {
          this.messages = messageRes.messages.map((msg) => {
            // Tìm vị trí <answer>
            const idx = msg.answer.indexOf("<answer>");
            if (idx !== -1) {
              // Kiểm tra ký tự ngay sau <answer>
              const after = msg.answer.substring(idx + 8, idx + 9); // 8 là độ dài '<answer>'
              if (after !== "\n") {
                // Nếu chưa có \n, chèn vào
                msg.answer = msg.answer.replace("<answer>", "<answer>\n\n");
              }
            }
            return msg;
          });
          this.scrollToBottom();
        })
      )
      .subscribe();
  }
  onRightClickChatbotMessage(event: MouseEvent, item): void {
    event.preventDefault();
    this.contextMenuVisible = true;
    this.contextMenuPosition = { x: event.clientX, y: event.clientY };
    this.contextMenuItem = item;
  }
  closeContextMenu(): void {
    this.contextMenuVisible = false;
  }
  @HostListener("document:click")
  onDocumentClick(): void {
    this.closeContextMenu();
  }
  selectedForRename: any;
  renameChatbot(contextMenuItem) {
    this.isEditChatbotName = true;
    this.selectedForRename = contextMenuItem;
    setTimeout(() => {
      const elements = document.querySelectorAll("[contenteditable]");
      elements.forEach((el) => {
        if (el.textContent?.trim() === contextMenuItem.name) {
          // Move caret to end
          const range = document.createRange();
          const sel = window.getSelection();
          range.selectNodeContents(el);
          range.collapse(false);
          sel?.removeAllRanges();
          sel?.addRange(range);
        }
      });
    }, 0);
  }
  onEditNameEnter(event: KeyboardEvent, item: any) {
    event.preventDefault(); // Ngăn xuống dòng
    const newName = (event.target as HTMLElement).innerText.trim();
    this.saveNewName(item, newName);
  }

  onEditNameBlur(event: FocusEvent, item: any) {
    const newName = (event.target as HTMLElement).innerText.trim();
    this.saveNewName(item, newName);
  }

  saveNewName(item: any, newName: string) {
    if (newName !== item.name) {
      if (newName.length > 255 || newName.length == 0) {
        this.toast.error(
          newName.length > 255
            ? "Không được vượt quá 255 ký tự!"
            : "Tên không được để trống!",
          "Lỗi",
          {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          }
        );
        // Reset lại tên nếu quá dài hoặc để trống
        this.groupConversations();
        this.isEditChatbotName = false;
        return;
      } else {
        item.name = newName;
      }
      this.chatbotService.renameChatbot(item.id, newName).subscribe(
        (res) => {
          this.toast.success("Đã cập nhật tên chatbot", "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          item.updated_at = new Date().toISOString(); // Cập nhật thời gian sửa đổi
          this.groupConversations();
        },
        (err) => {
          // Luôn trả lại tên ban đầu nếu đổi tên thất bại
          this.groupConversations();

          this.toast.error("Có lỗi xảy ra khi cập nhật tên chatbot!", "Lỗi", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
        }
      );
    }
    this.isEditChatbotName = false;
  }
  deleteChatbot(contextMenuItem) {
    Swal.fire({
      title: "Bạn có chắc chắn muốn xóa?",
      icon: "warning",
      reverseButtons: true,
      showCancelButton: true,
      confirmButtonColor: "#008fd3",
      cancelButtonColor: "#EEE",
      customClass: {
        confirmButton: "swal-confirm",
        cancelButton: "swal-cancel",
      },
      confirmButtonText: "Xóa",
      cancelButtonText: "Hủy",

      preConfirm: async () => {
        return this.chatbotService.deleteChatbot(contextMenuItem.id).subscribe(
          (res) => {
            this.toast.success("Đã xoá lịch sử chat", "Thành công", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
            this.loadConversationsWithMessages();
          },
          (err) => {
            this.toast.error("Có lỗi xảy ra khi xoá lịch sử chat!", "Lỗi", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
          }
        );
      },

      allowOutsideClick: () => {
        return !Swal.isLoading();
      },
    });
  }
  filterConversations() {
    const search = this.searchConversationString?.trim().toLowerCase() || "";

    if (!search || search.length === 0) {
      this.groupedConversationsFilter = [...this.groupedConversations];
      return;
    }

    this.groupedConversationsFilter = this.groupedConversations
      .map((group) => ({
        ...group,
        items: group.items.filter((item) =>
          item.name.toLowerCase().includes(search)
        ),
      }))
      .filter((group) => group.items.length > 0);
  }

  onAnswerClick(event: MouseEvent): void {
    this.viewDetailFileService.clauseId.next(null); // reset clauseID khi nhấn vào thẻ a
    // sự kiện click vào thẻ a
    const target = event.target as HTMLElement;
    // console.log(target);
    // Kiểm tra nếu phần tử được click là thẻ <a>
    if (target.tagName.toLowerCase() === "a") {
      const linkText = target.textContent?.trim();
      const typeLink = target.getAttribute("href");
      if (typeLink.includes("legal")) {
        if (
          linkText.toLowerCase().includes("điều") ||
          linkText.toLowerCase().includes("khoản") ||
          linkText.toLowerCase().includes("điểm")
        ) {
          // ưu tiên tìm kiếm điều
          this.chatbotService
            .getDocIdFromSoHieu(linkText, true)
            .subscribe((res) => {
              if (!this.handleFileResult(res)) {
                this.chatbotService
                  .getDocIdFromSoHieu(linkText, false)
                  .subscribe((res2) => {
                    // this.viewDetailFileService.clauseId.next("dieu_1"); // để khi tìm điều khoản không ra sẽ nhảy sang văn bản, và chuyển scroll về điều 1 tránh hiểu lầm
                    if (!this.handleFileResult(res2)) {
                      this.toast.error(
                        "Không tìm thấy văn bản nào với số hiệu này",
                        "Lỗi",
                        {
                          closeButton: true,
                          positionClass: "toast-top-right",
                          toastClass: "toast ngx-toastr",
                        }
                      );
                    }
                  });
              }
            });
          // console.log("Bạn đã click vào <a> có text:", linkText);
        } else {
          // ưu tiên tìm kiếm văn bản
          this.chatbotService
            .getDocIdFromSoHieu(linkText, false)
            .subscribe((res) => {
              if (!this.handleFileResult(res)) {
                this.chatbotService
                  .getDocIdFromSoHieu(linkText, true)
                  .subscribe((res2) => {
                    // this.viewDetailFileService.clauseId.next("dieu_1"); // để khi tìm điều khoản không ra sẽ nhảy sang văn bản, và chuyển scroll về điều 1 tránh hiểu lầm
                    if (!this.handleFileResult(res2)) {
                      this.toast.error(
                        "Không tìm thấy văn bản nào với số hiệu này",
                        "Lỗi",
                        {
                          closeButton: true,
                          positionClass: "toast-top-right",
                          toastClass: "toast ngx-toastr",
                        }
                      );
                    }
                  });
              }
            });
          // console.log("Bạn đã click vào <a> có text:", linkText);
        }
      } else {
        const idFile = typeLink.split(":").pop();
        this.router.navigate([], {
          queryParams: {
            fileId: idFile,
            tabs: "toanvan",
            type: "searching",
            time: new Date().getTime(),
            fileName: this.transform(linkText),
            save: true,
          },
          queryParamsHandling: "merge",
        });
        this.listDocumentService.FileSearchTemp.next(null);

        this.listDocumentService.setBehavior(ShowContent.Search);
      }
      // // console.log("link", linkText);

      // Nếu muốn chặn chuyển trang mặc định:
      event.preventDefault();
      // Xử lý logic tại đây...
    }
  }
  private handleFileResult(res: any) {
    const file = res.data[0];
    if (res.data.length > 0) {
      this.viewDetailFileService.fileInfor.next(file);
      this.router.navigate([], {
        queryParams: {
          fileId: file.id,
          tabs: "toanvan",
          type: "searching",
          time: new Date().getTime(),
          fileName: this.transform(file.trich_yeu),
          save: true,
        },
        queryParamsHandling: "merge",
      });
      this.listDocumentService.FileSearchTemp.next(file);
      this.listDocumentService.setBehavior(ShowContent.Search);
      if (file.terms.length > 0) {
        // nếu có điều khoản thì sẽ lấy điều khoản đầu tiên để hiển thị, còn k có thì thôi
        const termId = file.terms[0].term_id;
        this.viewDetailFileService.clauseId.next(termId);
      }

      // this.isMaximized = false;
      if (this.type == FormType.Chatbot) {
        this.modalOpen(this.viewFileModal, FormType.Convert, "xl");
        this.isMaximized = true;
      }
      if (this.isMaximized && this.type != FormType.Chatbot) {
        this.modalOpen(this.viewFileModal, FormType.Maximized, "xl");
        // this.isMaximized = true;
      }
      return true;
    }
    return false;
  }
  modalOpen(modalSM, type: FormType, size) {
    this.modalService.open(modalSM, {
      centered: true,
      size: size,
    });
    this.types = type;
  }
  transform(value: string): string {
    return value.replace(/<[^>]+>/g, ""); // Xóa tất cả thẻ HTML
  }
  onChatScroll() {
    const el = this.chatContainer.nativeElement;
    // Hiển thị nút nếu chưa cuộn hết phần tử chat
    this.showScrollButton =
      el.scrollTop + el.clientHeight < el.scrollHeight - 50;

    // Disable auto-scroll if user is not at the bottom, enable if at bottom
    this.autoScroll = el.scrollTop + el.clientHeight >= el.scrollHeight - 100;
  }
  selectTool(tools) {
    // Tránh thêm trùng lặp
    const exists = this.toolSelected.some((t) => t.value === tools?.value);
    if (!exists) {
      this.toolSelected = [...this.toolSelected, tools];
    }
    // // console.log("this.toolSelected", this.toolSelected);
  }
  removeTool(index) {
    this.toolSelected.splice(index, 1);
  }
  editQuestion(index) {
    const userMessages = this.messages.filter((m) => m.role === "user");
    const userMessage = userMessages[(index - 1) / 2].answer; // vì index là 1 3 5 7 ... nên phải xử lý lại các index thàn 0,1,2 ...
    this.userInput = userMessage;
  }

  // Danh sách công cụ còn lại (ẩn các công cụ đã chọn ở popover)
  getAvailableTools() {
    return this.listTool.filter(
      (tool) => !this.toolSelected.some((sel) => sel.value === tool.value)
    );
  }
  isAllSelected(): boolean {
    return (
      this.listDocument.length > 0 &&
      this.listDocument.every((doc) => this.isDocumentSelected(doc))
    );
  }
  toggleSelection(index: number) {
    const doc = this.listDocument[index];
    if (!doc) return;
    const key = this.getDocumentKey(doc);
    if (!key) return;

    if (this.selectedDocumentKeys.has(key)) {
      this.selectedDocumentKeys.delete(key);
    } else {
      this.selectedDocumentKeys.add(key);
    }
    this.syncSelectedDocuments();
  }

  toggleAll(event: Event) {
    const checked = (event.target as HTMLInputElement).checked;

    if (checked) {
      this.listDocument.forEach((doc) => {
        const key = this.getDocumentKey(doc);
        if (key) this.selectedDocumentKeys.add(key);
      });
    } else {
      this.selectedDocumentKeys.clear();
    }
    this.syncSelectedDocuments();
  }
  clearDocument(event) {
    event.stopPropagation();
    this.selectedDocumentKeys.clear();
    this.syncSelectedDocuments();
  }

  private getDocumentKey(doc: any): string | null {
    if (!doc) return null;
    const key = doc?.es_id ?? doc?.id ?? doc?.doc_id ?? null;
    return key !== null && key !== undefined ? String(key) : null;
  }

  private isDocumentSelected(doc: any): boolean {
    const key = this.getDocumentKey(doc);
    return !!key && this.selectedDocumentKeys.has(key);
  }

  private syncSelectedDocuments(): void {
    if (!Array.isArray(this.listDocument) || this.listDocument.length === 0) {
      this.selectedDocuments = [];
      this.selectedFile = [];
      this.selected_save_files = [];
      this.selected_upload_files = [];
      return;
    }

    this.selectedDocuments = this.listDocument.map((doc) =>
      this.isDocumentSelected(doc)
    );

    this.selectedFile = this.listDocument.filter((_, index) => this.selectedDocuments[index]);

    this.selected_save_files = this.selectedFile
      .filter((doc: any) => doc.types === TypeDocument.CSDL)
      .map((doc: any) => (doc.es_id ? doc.es_id : doc.id));

    this.selected_upload_files = this.selectedFile
      .filter((doc: any) => doc.types === TypeDocument.UPLOAD)
      .map((doc: any) => (doc.es_id ? doc.es_id : doc.id));
  }
  feedbackChatbot(message, action) {
    // // console.log("message", message);
    this.chatbotService.feedbackChatbot(message.id, action).subscribe(
      (res) => {
        // Cập nhật lại tin nhắn trong danh sách
        const index = this.messages.findIndex((m) => m.id === message.id);
        if (index !== -1) {
          this.messages[index].feedback = action;
        }
      },
      (error) => {
        this.toast.error("Có lỗi xảy ra khi gửi phản hồi!", "Lỗi", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
      }
    );
  }
  private audio: HTMLAudioElement | null = null;
  isPaused = false;
  textToSpeech(message) {
    if ("speechSynthesis" in window) {
      // Dừng tất cả speech đang chạy
      speechSynthesis.cancel();

      this.idMessageSpeechToText = message.id;
      this.isSpeechToText = true;
      this.isPaused = false;

      // Tạo utterance mới
      const utterance = new SpeechSynthesisUtterance(message.answer);

      // Cấu hình voice
      utterance.lang = "vi-VN"; // Tiếng Việt
      utterance.rate = 1; // Tốc độ đọc
      utterance.pitch = 1; // Cao độ giọng
      utterance.volume = 1; // Âm lượng

      // Xử lý sự kiện kết thúc
      utterance.onend = () => {
        this.isSpeechToText = false;
        this.isPaused = false;
        this.idMessageSpeechToText = null;
      };

      utterance.onerror = (err) => {
        console.error("Lỗi khi phát speech:", err);
        this.isSpeechToText = false;
        this.isPaused = false;
        this.idMessageSpeechToText = null;
      };

      // Bắt đầu đọc
      speechSynthesis.speak(utterance);
    } else {
      this.toast.error("Trình duyệt không hỗ trợ text-to-speech", "Lỗi", {
        closeButton: true,
        positionClass: "toast-top-right",
        toastClass: "toast ngx-toastr",
      });
    }
  }

  pauseTextToSpeech() {
    if (speechSynthesis.speaking && !speechSynthesis.paused) {
      speechSynthesis.pause();
      this.isPaused = true;
      // console.log("Đã tạm dừng speech!");
    }
  }

  resumeTextToSpeech() {
    if (speechSynthesis.paused) {
      speechSynthesis.resume();
      this.isPaused = false;
      // console.log("Đã tiếp tục speech!");
    }
  }

  endTextToSpeech() {
    speechSynthesis.cancel();
    this.isSpeechToText = false;
    this.isPaused = false;
    this.idMessageSpeechToText = null;
  }

  showPopover = false;
  popoverPosition = { top: 0, left: 0 };
  selectedText = "";
  selectedHtml = ""; 
  
private _getSelectionEndRect(range: Range): DOMRect {
  const rects = range.getClientRects();
  if (rects && rects.length > 0) {
    return rects[rects.length - 1]; 
  }
  const tmpSpan = document.createElement('span');
  tmpSpan.textContent = '\u200b';
  range.collapse(false);
  range.insertNode(tmpSpan);
  const rect = tmpSpan.getBoundingClientRect();
  tmpSpan.parentNode?.removeChild(tmpSpan);
  return rect;
}

  onTextSelect() {
    // nếu đang stream thì khỏi bật popup để tránh nhảy
    if (!this.checkDoneAnswer) { this.showPopover = false; return; }

    const container = this.chatContainer?.nativeElement as HTMLElement;
    const sel = window.getSelection();
    const text = sel?.toString();

    if (!container || !text || !sel?.rangeCount) { this.showPopover = false; return; }

    const range = sel.getRangeAt(0);
    const common = range.commonAncestorContainer;
    if (!this._nodeIn(container, common)) { this.showPopover = false; return; }

    const html = this.getSelectedHtmlIn(container);
    if (!html) { this.showPopover = false; return; }
    this.selectedHtml = html;

    // định vị tại cuối vùng chọn (trong KHUNG CUỘN)
    const endRect = this._getSelectionEndRect(range);
    const box = container.getBoundingClientRect();
    const sx = container.scrollLeft || 0;
    const sy = container.scrollTop  || 0;
    const GAP = 10;

    let left = (endRect.left   - box.left) + sx;
    let top  = (endRect.bottom - box.top)  + sy + GAP;

    this.showPopover = true;
    this.popoverPosition = { top, left };

    setTimeout(() => {
      const pop = document.querySelector('.selection-popover') as HTMLElement;
      if (!pop) return;

      const PAD=8, W=pop.offsetWidth||220, H=pop.offsetHeight||44;
      const maxL = container.scrollWidth  - W - PAD;
      const maxT = container.scrollHeight - H - PAD;

      if (left > maxL) left = maxL;
      if (top  > maxT) top  = Math.max(PAD, (endRect.top - box.top) + sy - H - GAP);

      left = Math.max(PAD, Math.min(left, maxL));
      top  = Math.max(PAD, Math.min(top,  maxT));
      this.popoverPosition = { top, left };
    }, 0);
  }

  createNoteFromSelection() {
    if (!this.selectedHtml) return;

    this.noteService.textFromVanBan.next(this.selectedHtml);

    window.dispatchEvent(new CustomEvent('open-notes-tab'));
    this.showPopover = false;
  }

  cancelSelectionPopover() {
    this.showPopover = false;
  }

  viewFileAttached(fileAttached) {
    if (fileAttached) {
      // this.viewDetailFileService.fileInfor.next(fileAttached);
      this.router.navigate([], {
        queryParams: {
          fileId: fileAttached.id,
          tabs: "toanvan",
          type: "searching",
          time: new Date().getTime(),
          fileName: this.transform(fileAttached.name),
          save: true,
        },
        queryParamsHandling: "merge",
      });
      // this.listDocumentService.FileSearchTemp.next(fileAttached);
      this.listDocumentService.setBehavior(ShowContent.Search);

      // this.isMaximized = false;
      if (this.type == FormType.Chatbot) {
        this.modalOpen(this.viewFileModal, FormType.Convert, "xl");
        this.isMaximized = true;
      }
      if (this.isMaximized && this.type != FormType.Chatbot) {
        this.modalOpen(this.viewFileModal, FormType.Maximized, "xl");
      }
      return true;
    }
    return false;
  }

  // Xuất hội thoại theo định dạng md/html/docx; mặc định là docx
  exportFile(format: "md" | "html" | "docx" = "docx") {
    try {
      // Tạo chuỗi thời gian YYYYMMDD-HHMM để đặt tên file
      const now = new Date();
      const y = now.getFullYear();
      const m = String(now.getMonth() + 1).padStart(2, "0");
      const d = String(now.getDate()).padStart(2, "0");
      const hh = String(now.getHours()).padStart(2, "0");
      const mm = String(now.getMinutes()).padStart(2, "0");

      // Lấy tên workspace/hội thoại làm base name
      const wsName =
        (this.selectedConversation?.name ?? this.workSpaceName ?? "chatbot")
          .toString()
          .trim() || "chatbot";
      const base = `${wsName}-${y}${m}${d}-${hh}${mm}`;

      // Nhánh xuất markdown hoặc HTML trực tiếp bằng Blob
      if (["md", "html"].includes(format)) {
        let content = "";
        let mime = "text/plain;charset=utf-8";
        let filename = `${base}.md`;

        // Chuyển messages -> nội dung theo định dạng tương ứng
        switch (format) {
          case "md":
            content = this._buildMarkdownTranscript(
              this.messages ?? [],
              wsName
            );
            mime = "text/markdown;charset=utf-8";
            filename = `${base}.md`;
            break;
          case "html":
            content = this._buildHtmlTranscript(this.messages ?? [], wsName);
            mime = "text/html;charset=utf-8";
            filename = `${base}.html`;
            break;
        }

        // Tạo Blob và trigger download thủ công bằng thẻ <a> tạm
        const blob = new Blob([content], { type: mime });
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = filename;
        a.style.display = "none";
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // Thông báo thành công
        this.toast?.success("Đã xuất hội thoại", "Thành công", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
        return;
      }

      // Nhánh xuất DOCX: tạo Blob async rồi tải về
      if (format === "docx") {
        if (!Array.isArray(this.messages) || this.messages.length === 0) {
          this.toast?.info("Hãy bắt đầu hội thoại trước khi xuất dữ liệu", "Thông báo", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          return;
        }

        this._buildDocxBlob(this.messages ?? [], wsName)
          .then((blob) => {
            const url = URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            a.download = `${base}.docx`;
            a.style.display = "none";
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);

            this.toast?.success("Đã xuất hội thoại", "Thành công", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
          })
          .catch((e) => {
            console.error(e);
            this.toast?.error("Không thể xuất hội thoại", "Lỗi", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
          });
        return;
      }
    } catch (e) {
      // Bắt lỗi tổng thể quá trình export
      console.error("Export failed:", e);
      this.toast?.error("Không thể xuất hội thoại", "Lỗi", {
        closeButton: true,
        positionClass: "toast-top-right",
        toastClass: "toast ngx-toastr",
      });
    }
  }

  // Xây dựng file HTML hoàn chỉnh (head + body) từ messages
  private _buildHtmlTranscript(messages: any[], title: string): string {
    // Hàm escape ký tự HTML cơ bản cho <title>...
    const esc = (s: string) =>
      (s || "").replace(
        /[&<>"]/g,
        (c) =>
          (({ "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;" } as any)[
            c
          ])
      );
    // Khung HTML + style in trang
    const head = `<!doctype html><html><head><meta charset="utf-8"><title>${esc(
      title
    )}</title>
  <style>
    body{font-family:system-ui,-apple-system,Segoe UI,Roboto,Arial,sans-serif;max-width:1000px;margin:24px auto;padding:0 16px;line-height:1.6;color:#111}
    h1{margin:0 0 8px 0}
    .meta{font-size:12px;color:#666;margin-bottom:12px}
    table.turns{width:100%;border-collapse:collapse;table-layout:fixed}
    .who{width:140px;vertical-align:top;font-weight:700;color:#ea580c; /* cam */ padding:8px 10px}
    .content{vertical-align:top;padding:8px 12px;border-left:1px solid #e5e7eb}
    tr + tr td{border-top:1px solid #e5e7eb}
    .content p{margin:0 0 8px 0}
    .content ul,.content ol{margin:8px 0 8px 20px}
    a{color:#2563eb;text-decoration:underline}
  </style></head><body>`;
    // Thân HTML (bảng hội thoại)
    const inner = this._buildHtmlInnerTranscript(messages, title);
    return head + inner + "</body></html>";
  }

  // Xây phần thân HTML: render từng lượt hội thoại thành bảng
  private _buildHtmlInnerTranscript(messages: any[], title: string): string {
    // Escape chuỗi cho các vị trí cần thiết
    const esc = (s: string) =>
      (s || "").replace(
        /[&<>"]/g,
        (c) =>
          (({ "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;" } as any)[
            c
          ])
      );

    // Style dành cho nội dung in (A4, margin)
    const style = `
  <style>
    @page { size: A4; margin: 12mm; }
    body{font-family:system-ui,-apple-system,Segoe UI,Roboto,Arial,sans-serif;line-height:1.6;color:#111}
    h1{margin:0 0 8px 0}
    .meta{font-size:12px;color:#666;margin-bottom:12px}
    table.turns{width:100%;border-collapse:collapse;table-layout:fixed}
    td.who{width:120px;vertical-align:top;font-weight:700;padding:8px 10px}
    td.who.user{color:#111}           /* Bạn: */
    td.who.assistant{color:#ea580c}   /* AI pháp luật: (cam) */
    td.content{vertical-align:top;padding:8px 12px;border-left:1px solid #e5e7eb}
    tr + tr td{border-top:1px solid #e5e7eb}
    .content p{margin:0 0 8px 0}
    .content ul,.content ol{margin:8px 0 8px 20px}
    a{color:#2563eb;text-decoration:underline}
  </style>`;

    const rows: string[] = [];
    for (const msg of messages || []) {
      // Nhãn người nói
      const who = msg.role === "user" ? "Bạn:" : "AI pháp luật:";
      const whoClass = msg.role === "user" ? "user" : "assistant";

      // Làm sạch nội dung cho từng vai trò (assistant có thể có thẻ <answer>)
      const raw =
        msg.role === "assistant"
          ? String(msg.answer || "").replace(/<\/?answer>/g, "")
          : this._htmlToText(String(msg.answer || ""));

      // Chuyển markdown cơ bản sang HTML (list/paragraph/link)
      const html = this._mdToHtmlBasic(raw);

      // Thêm một hàng vào bảng hội thoại
      rows.push(
        `<tr>
        <td class="who ${whoClass}">${who}</td>
        <td class="content">${html}</td>
      </tr>`
      );
    }

    // Header + meta + bảng nội dung
    return `
    ${style}
    <h1>${esc(title)}</h1>
    <div class="meta">Exported: ${esc(new Date().toLocaleString())}</div>
    <table class="turns"><tbody>${rows.join("")}</tbody></table>
  `;
  }

  // Xây dựng Blob DOCX từ messages (sử dụng docx library)
  private async _buildDocxBlob(messages: any[], title: string): Promise<Blob> {
    // console.log("msg for docx", messages);
    const children: any[] = [
      // Tiêu đề tài liệu và dòng meta thời gian
      new Paragraph({
        text: title || "Hội thoại",
        heading: HeadingLevel.TITLE,
        alignment: AlignmentType.LEFT,
      }),
      // new Paragraph({
      //   children: [
      //     new TextRun({
      //       text: `Exported: ${new Date().toLocaleString()}`,
      //       italics: true,
      //       color: "666666",
      //     }),
      //   ],
      //   spacing: { after: 200 },
      // }),
    ];

    // Bộ style viền rỗng để bảng/ô không có đường viền
    const NONE = { style: BorderStyle.NONE, size: 0, color: "FFFFFF" } as const;
    const noBorders = {
      top: NONE,
      bottom: NONE,
      left: NONE,
      right: NONE,
      insideHorizontal: NONE,
      insideVertical: NONE,
    };

    // Phân tích bold / italic trong text
    const makeTextRuns = (text: string): TextRun[] => {
      const parts: TextRun[] = [];

      // Tách theo group: [ ... ] hoặc phần còn lại
      const regex = /(\[.+?\])/g;

      const segments = text.split(regex); // giữ nguyên text ngoài ngoặc

      for (const seg of segments) {
        if (!seg) continue;

        // Nếu là dạng [ ... ] => màu xanh 1E40AF
        if (/^\[.+?\]$/.test(seg)) {
          parts.push(
            new TextRun({
              text: seg.slice(1, -1), // bỏ dấu []
              color: "1E40AF",
            })
          );
          continue;
        }

        // Các style khác: bold, italic
        const innerRegex =
          /(\*\*\*.+?\*\*\*|\*\*.+?\*\*|\*.+?\*|[^*]+)/g;
        let match;
        while ((match = innerRegex.exec(seg))) {
          const token = match[0];

          if (/^\*\*\*(.+)\*\*\*$/.test(token))
            parts.push(new TextRun({ text: token.slice(3, -3), bold: true, italics: true }));
          else if (/^\*\*(.+)\*\*$/.test(token))
            parts.push(new TextRun({ text: token.slice(2, -2), bold: true }));
          else if (/^\*(.+)\*$/.test(token))
            parts.push(new TextRun({ text: token.slice(1, -1), italics: true }));
          else
            parts.push(new TextRun({ text: token }));
        }
      }
      return parts.length ? parts : [new TextRun(" ")];
    };

    // Phân tích dòng markdown có indent (list lồng nhau)
    const parseMarkdownLine = (line: string) => {
      const raw = line ?? "";
      const indentSpaces = raw.match(/^\s*/)?.[0]?.length || 0;
      const level = Math.floor(indentSpaces / 2); // mỗi 2 space = 1 cấp
      const t = raw.trim();

      if (!t) return { type: "empty", text: " ", level };

      const heading = /^(#{1,6})\s+(.+)$/.exec(t);
      if (heading)
        return { type: "heading", level: heading[1].length, text: heading[2] };

      // const ordered = /^(\d+)[.)]\s+(.+)$/.exec(t);
      // if (ordered)
      //   return { type: "ordered", text: ordered[2], level };

      const bullet = /^([-*•])\s+(.+)$/.exec(t);
      if (bullet)
        return { type: "bullet", text: bullet[2], level };

      if (/^-{3,}$/.test(t)) 
        return { type: "hr", text: "", level };

      return { type: "normal", text: t, level };
    };

    // Tạo Paragraph tương ứng
    const makePara = (line: string) => {
      const parsed = parseMarkdownLine(line);

      switch (parsed.type) {
        case "heading":
          return new Paragraph({
            // children: makeTextRuns(parsed.text),
            children: [
              new TextRun({
                text: parsed.text,
                bold: true,
                color: "000000", // ép màu đen
              }),
            ],
            heading: HeadingLevel[`HEADING_${Math.min(parsed.level, 6)}`],
            spacing: { after: 120 },
          });

        // case "ordered":
        //   return new Paragraph({
        //     children: makeTextRuns(parsed.text),
        //     numbering: { reference: "ordered-list", level: Math.min(parsed.level, 5) },
        //     spacing: { after: 40 },
        //   });

        case "bullet":
          return new Paragraph({
            children: makeTextRuns(parsed.text),
            bullet: { level: Math.min(parsed.level, 5) },
            spacing: { after: 40 },
          });

        case "hr":
          return new Paragraph({
            children: [],
            border: {
              bottom: {
                color: "999999",
                size: 5,     // độ dày
                style: BorderStyle.SINGLE,
              },
            },
            // spacing: { before: 200, after: 200 },
          });

        default: {
          // Nếu bắt đầu bằng số (ordered-like) và có indent => thụt lề
          const isNumbered = /^\d+[.)]\s+/.test(parsed.text);
          if (isNumbered && parsed.level > 0) {
            return new Paragraph({
              children: makeTextRuns(parsed.text),
              indent: { left: 540 * (parsed.level + 1) }, // 0.5 inch mỗi cấp
              spacing: { after: 40 },
            });
          }

          // Mặc định
          return new Paragraph({
            children: makeTextRuns(parsed.text),
            spacing: { after: 60 },
          });
        }
      }
    };

    const rows: any[] = [];

    // Xử lý markdown bảng
    const isTableRow = (line: string) => /^\s*\|.*\|\s*$/.test(line);
    const isSeparatorRow = (line: string) => /^\s*\|?(?:\s*:?-+:?\s*\|)+\s*$/.test(line);

    // Parse 1 nhóm bảng
    const parseMarkdownTable = (lines: string[], startIdx: number) => {
      const rows: string[][] = [];
      let i = startIdx;

      // dòng tiêu đề
      if (!isTableRow(lines[i])) return null;
      const header = lines[i].split("|").slice(1, -1).map(c => c.trim());
      rows.push(header);
      i++;

      // dòng phân tách |---|
      if (!isSeparatorRow(lines[i])) return null;
      i++;

      // các dòng dữ liệu
      while (i < lines.length && isTableRow(lines[i])) {
        const cols = lines[i].split("|").slice(1, -1).map(c => c.trim());
        rows.push(cols);
        i++;
      }

      return {
        table: rows,
        nextIndex: i
      };
    };

    // Convert rows[][] => bảng DOCX
    const makeDocxTable = (rows: string[][]) => {
      return new Table({
        width: { size: 100, type: WidthType.PERCENTAGE },
        borders: {
          top: { style: BorderStyle.SINGLE, size: 1, color: "000000" },
          bottom: { style: BorderStyle.SINGLE, size: 1, color: "000000" },
          left: { style: BorderStyle.SINGLE, size: 1, color: "000000" },
          right: { style: BorderStyle.SINGLE, size: 1, color: "000000" },
          insideHorizontal: { style: BorderStyle.SINGLE, size: 1, color: "000000" },
          insideVertical: { style: BorderStyle.SINGLE, size: 1, color: "000000" },
        },
        rows: rows.map((cols, rowIndex) =>
          new TableRow({
            children: cols.map((col, colIndex) =>
              new TableCell({
                children: [
                  new Paragraph({
                    children: rowIndex === 0
                      ? [
                          new TextRun({
                            text: col,
                            bold: true,
                            color: "000000",
                          }),
                        ]
                      : makeTextRuns(col),
                    spacing: { after: 80 },
                  })
                ]
              })
            )
          })
        ),
      });
    };


    // Duyệt message -> dựng bảng 2 cột (người nói | nội dung)
    for (const msg of messages || []) {
      const who = msg.role === "user" ? "Bạn:" : "Chatbot:";

      // Làm sạch nội dung theo vai trò
      let raw =
        msg.role === "assistant"
          ? String(msg.answer || "").replace(/<\/?answer>/g, "")
          : this._htmlToText(String(msg.answer || ""));

      // Xóa mọi cụm (legal: ... )
      raw = raw.replace(/\(legal:[^)]*\)/g, "");

      const plain = this._mdLinksToPlain(raw);
      const lines = plain.split(/\r?\n/);
      // const contentParas = lines.length
      //   ? lines.map(makePara)
      //   : [new Paragraph({ text: " " })];
      const contentParas: any[] = [];

      let i = 0;
      while (i < lines.length) {
        const line = lines[i];

        // Nếu dòng này là bảng => parse toàn bộ block bảng
        if (isTableRow(line)) {
          const parsed = parseMarkdownTable(lines, i);
          if (parsed) {
            contentParas.push(makeDocxTable(parsed.table));
            i = parsed.nextIndex;
            continue;
          }
        }

        // Nếu không phải bảng => paragraph bình thường
        contentParas.push(makePara(line));
        i++;
      }

      if (contentParas.length === 0) {
        contentParas.push(new Paragraph({ text: " " }));
      }

      // Ô bên trái: nhãn người nói, nền cam nhạt, không viền
      const whoCell = new TableCell({
        width: { size: 18, type: WidthType.PERCENTAGE },
        verticalAlign: VerticalAlign.TOP,
        margins: { top: 120, bottom: 80, left: 160, right: 120 }, // twips
        shading: { fill: "FFF7ED" }, // cam nhạt
        borders: noBorders,
        children: [
          new Paragraph({
            children: [new TextRun({ text: who, bold: true, color: "EA580C" })], // chữ cam đậm
          }),
        ],
      });

      // Ô bên phải: nội dung hội thoại, không viền

      // Thêm các đoạn phụ nếu có selection_text hoặc selected_files (với user)
      const extraParas: Paragraph[] = [];
      if (msg.role === "user") {
        // Nếu có selection_text
        if (msg.selection_text) {
          // Tách dòng để giữ định dạng trong selection_text
          const lines = msg.selection_text.split(/\n/);

          for (const line of lines) {
            if (line.trim()) {
              if (line === lines[0]) {
                extraParas.push(new Paragraph({
                  children: [new TextRun({ text: `⤷ ${line}`, bold: true })],
                  spacing: { before: 120, after: 60 },
                }));
                continue;
              }
              extraParas.push(new Paragraph({
                children: [new TextRun({ text: `${line}`, bold: true })],
                spacing: { before: 120, after: 60 },
              }));
            }
          }
        }
  
        // Nếu có selected_files (save_files + upload_files)
        const saveFiles = msg.selected_files?.save_files || [];
        const uploadFiles = msg.selected_files?.upload_files || [];
        const allFiles = [...saveFiles, ...uploadFiles];
  
        if (allFiles.length > 0) {
          extraParas.push(
            new Paragraph({
              children: [new TextRun({ text: "Tài liệu đính kèm:", })],
              spacing: { before: 120, after: 60 },
            })
          );
  
          for (const f of allFiles) {
            extraParas.push(
              new Paragraph({
                children: [
                  new TextRun({ text: `- ${f.name}`, color: "1E40AF" }), // xanh đậm nhẹ
                ],
                spacing: { after: 40 },
              })
            );
          }
        }
      }

      const contentCell = new TableCell({
        width: { size: 82, type: WidthType.PERCENTAGE },
        verticalAlign: VerticalAlign.TOP,
        margins: { top: 120, bottom: 80, left: 160, right: 160 },
        borders: noBorders,
        children: [...contentParas, ...extraParas],
      });

      // Thêm một hàng (không khóa cantSplit để cho phép tràn sang trang)
      rows.push(new TableRow({ children: [whoCell, contentCell] }));
    }

    // Định nghĩa nhiều cấp numbering và gói thành tài liệu DOCX rồi chuyển thành Blob
    const doc = new DocxDocument({
      numbering: {
        config: [
          {
            reference: "ordered-list",
            levels: Array.from({ length: 6 }, (_, i) => ({
              level: i,
              format: "decimal",
              text: `%${i + 1}.`,
              alignment: AlignmentType.LEFT,
              style: { paragraph: { indent: { left: 720 * (i + 1) } } }, // thụt 0.5 inch mỗi cấp
            })),
          },
        ],
      },
      sections: [
        {
          properties: {},
          children: [
            ...children,
            new Table({
              width: { size: 100, type: WidthType.PERCENTAGE },
              borders: noBorders,
              rows,
            }),
          ],
        },
      ],
    });

    return Packer.toBlob(doc);
  }

  // Kết xuất Markdown: bảng 2 cột (Người nói | Nội dung)
  private _buildMarkdownTranscript(messages: any[], title: string): string {
    const escPipe = (s: string) => (s || "").replace(/\|/g, "\\|");
    const header = `# ${this._escMdInline(
      title
    )}\n_Exported: ${new Date().toLocaleString()}_\n\n`;
    const lines: string[] = [header, "| Người nói | Nội dung |", "|---|---|"];
    for (const msg of messages || []) {
      const who = msg.role === "user" ? "Bạn:" : "Chatbot:";
      const raw =
        msg.role === "assistant"
          ? String(msg.answer || "").replace(/<\/?answer>/g, "")
          : this._htmlToText(String(msg.answer || ""));
      // Thay xuống dòng bằng <br> để hiển thị gọn trong bảng Markdown
      const cell = escPipe((raw || "").replace(/\r?\n/g, "<br>"));
      lines.push(`| ${who} | ${cell} |`);
    }
    return lines.join("\n");
  }

  // Chuyển markdown cơ bản -> HTML đơn giản (link/list/paragraph)
  private _mdToHtmlBasic(md: string): string {
    if (!md) return "";
    // Link markdown -> thẻ <a>
    let s = md.replace(
      /\[(.+?)\]\((https?:\/\/[^\s)]+)\)/g,
      '<a href="$2" target="_blank" rel="noopener">$1</a>'
    );

    // Duyệt từng dòng để render list/paragraph
    const lines = s.split(/\r?\n/);
    const out: string[] = [];
    let inUl = false,
      inOl = false;

    // Đóng các khối danh sách đang mở
    const flush = () => {
      if (inUl) {
        out.push("</ul>");
        inUl = false;
      }
      if (inOl) {
        out.push("</ol>");
        inOl = false;
      }
    };

    for (let line of lines) {
      const ul = /^\s*[-*•]\s+(.+)$/.exec(line);
      const ol = /^\s*\d+[.)]\s+(.+)$/.exec(line);

      if (ul) {
        if (!inUl) {
          flush();
          out.push("<ul>");
          inUl = true;
        }
        out.push(`<li>${ul[1]}</li>`);
        continue;
      }

      if (ol) {
        if (!inOl) {
          flush();
          out.push("<ol>");
          inOl = true;
        }
        out.push(`<li>${ol[1]}</li>`);
        continue;
      }

      // Không phải list -> đóng list nếu đang mở, rồi render paragraph/br
      flush();
      if (line.trim() === "") out.push("<br/>");
      else out.push(`<p>${line}</p>`);
    }

    // Đảm bảo đóng khối nếu còn
    flush();
    return out.join("");
  }

  // Chuyển HTML sang text thuần (dùng DOM để strip tag)
  private _htmlToText(html: string): string {
    const div = document.createElement("div");
    div.innerHTML = html || "";
    return (div.textContent || div.innerText || "").trim();
  }

  // Đổi markdown link [text](url) -> "text (url)" cho DOCX
  private _mdLinksToPlain(md: string): string {
    if (!md) return "";
    return md.replace(/\[(.+?)\]\((https?:\/\/[^\s)]+)\)/g, "$1 ($2)");
  }

  // Escape inline cho markdown title
  private _escMdInline(s: string): string {
    return (s || "").replace(/([\\`*_{}\[\]()#+\-.!|>])/g, "\\$1");
  }
  toggleMobileSidebar(): void {
    this.isMobileSidebarOpen = !this.isMobileSidebarOpen;
    
    // Prevent body scroll when sidebar is open
    if (this.isMobileSidebarOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }
  }

  private setupResizeListener(): void {
    window.addEventListener('resize', this.handleResize);
  }

  private handleResize = (): void => {
    // Đóng sidebar khi chuyển sang màn hình lớn hơn 768px
    if (window.innerWidth > 768 && this.isMobileSidebarOpen) {
      this.isMobileSidebarOpen = false;
      document.body.style.overflow = '';
    }
  }
  ngOnDestroy() {
    this.unSubAll.next(null);
    this.unSubAll.complete();
    this.chatbotService.textFormVanBan.next(null);
    window.removeEventListener('resize', this.handleResize);
  }
}
