<div class="row">
  <div class="col-auto pe-0 border-right">
    <ul
      ngbNav
      #navVertical="ngbNav"
      [(activeId)]="activeTabId"
      (navChange)="onTabChange($event.nextId)"
      class="nav nav-tabs nav-left flex-column"
    >
      <li ngbNavItem="ALL" class="pb-1">
        <a ngbNavLink>Tất cả ({{ allQaCount }})</a>
        <ng-template ngbNavContent></ng-template>
      </li>
      <li ngbNavItem="DRAFT" class="pb-1">
        <a ngbNavLink>Mới khởi tạo ({{ draftQa.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li>
      <li ngbNavItem="WRITING" class="pb-1">
        <a ngbNavLink>Đ<PERSON> viết ({{ writingQa.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li>
      <li ngbNavItem="REVIEWING" class="pb-1">
        <a ngbNavLink>Chờ phê duyệt ({{ reviewingQa.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li>
      <li ngbNavItem="COMPLETED" class="pb-1">
        <a ngbNavLink>Hoàn thành ({{ completedQa.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li>
      <li ngbNavItem="PUBLISHED" class="pb-1">
        <a ngbNavLink>Đã công bố ({{ publishedQa.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li>
      <!-- <li ngbNavItem="UNPUBLISHED" class="pb-1">
        <a ngbNavLink>Chưa công bố ({{ unpublishedQa.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li> -->
			<li ngbNavItem="REJECTED" class="pb-1">
        <a ngbNavLink>Từ chối ({{ rejectedQa.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li>
      <li ngbNavItem="ARCHIVED" class="pb-1">
        <a ngbNavLink>Lưu trữ ({{ archivedQa.length }})</a>
				<ng-template ngbNavContent></ng-template>
      </li>
    </ul>
  </div>

  <div class="col ps-0" style="min-width:0;">
		<!-- [externalPaging]="true" -->
		<!-- [offset]="page - 1" -->
    <ngx-datatable
      #QaTable
      [rows]="filteredQa"
      [rowHeight]="'auto'"
      class="bootstrap core-bootstrap cursor"
      [columnMode]="ColumnMode.force"
      [footerHeight]="50"
      [scrollbarH]="true"
      [limit]="12"
      [count]="totalItem"
      (activate)="onActivate($event)"
      (page)="onPage($event)"
      (sort)="onSort($event)"
      [sorts]="[{ prop: 'id', dir: 'asc' }]"
    >
      <ngx-datatable-column name="Mã" prop="id" [width]="70" [sortable]="true" [comparator]="compareNumber"></ngx-datatable-column>
      <ngx-datatable-column name="Câu hỏi" prop="__qa_title__" [sortable]="true">
        <ng-template ngx-datatable-cell-template let-row="row">
          <div class="text-wrap text-break">
            {{ row.__qa_title__ || getQaTitle(row) }}
          </div>
        </ng-template>
      </ngx-datatable-column>
			<ngx-datatable-column name="Người viết" prop="writer.fullname">
				<ng-template ngx-datatable-cell-template let-row="row">
          <div class="text-wrap text-break">
            {{ row.writer?.fullname }}
          </div>
        </ng-template>
			</ngx-datatable-column>
			<ngx-datatable-column name="Người p.duyệt" prop="reviewer.fullname">
				<ng-template ngx-datatable-cell-template let-row="row">
          <div class="text-wrap text-break">
            {{ row.reviewer?.fullname }}
          </div>
        </ng-template>
			</ngx-datatable-column>
      <ngx-datatable-column name="Cập nhật lúc" prop="">
        <ng-template ngx-datatable-cell-template let-row="row">
          <span>{{ formatRelativeTime(row.updated_at || row.created_at) }}</span>
        </ng-template>
      </ngx-datatable-column>
			<ngx-datatable-column name="Chủ đề" prop="topic">
				<ng-template ngx-datatable-cell-template let-row="row">
          <div class="text-wrap text-break">
            {{ row.topic }}
          </div>
        </ng-template>
      </ngx-datatable-column>
			<ngx-datatable-column *ngIf="activeTabId=='ALL'" name="Trạng thái" prop="status">
        <ng-template ngx-datatable-cell-template let-row="row">
          <div
            class="badge"
            [ngClass]="{
              'badge-light-dark': row.status === 'DRAFT',
              'badge-light-primary': row.status === 'WRITING' || row.status === 'REVIEWING',
              'badge-light-success': row.status === 'COMPLETED' || row.status === 'PUBLISHED',
              'badge-light-secondary': row.status === 'UNPUBLISHED',
              'badge-light-danger': row.status === 'REJECTED',
              'badge-light-warning': row.status === 'ARCHIVED'
            }"
          >
            {{
              row.status === 'DRAFT' ? 'Mới khởi tạo' :
              row.status === 'WRITING' ? 'Đang viết' :
              row.status === 'REVIEWING' ? 'Chờ phê duyệt' :
              row.status === 'COMPLETED' ? 'Hoàn thành' :
              row.status === 'PUBLISHED' ? 'Đã công bố' :
              row.status === 'UNPUBLISHED' ? 'Chưa công bố' :
              row.status === 'REJECTED' ? 'Từ chối' :
              row.status === 'ARCHIVED' ? 'Lưu trữ' :
              row.status
            }}
          </div>
        </ng-template>
      </ngx-datatable-column>
			<ngx-datatable-column name="Hiệu lực" prop="is_active">
        <ng-template ngx-datatable-cell-template let-row="row">
          <div
            class="badge"
            [ngClass]="{
              'badge-danger': !row.is_active,
              'badge-success': row.is_active
            }"
          >
            {{ row.is_active ? 'Hoạt động' : 'Đã ẩn' }}
          </div>
        </ng-template>
      </ngx-datatable-column>
      <ngx-datatable-column name="" [sortable]="false" [width]="80">
        <ng-template ngx-datatable-cell-template let-row="row">
          <span container="body">
            <a
							*ngIf="row.is_active"
              href="javascript:void(0);"
              class="hide-arrow color-333"
              id="dropdownBrowserState"
              data-toggle="dropdown"
              aria-haspopup="true"
              aria-expanded="false"
              placement="top"
              ngbTooltip="Vô hiệu hóa"
              container="body"
							(click)="updateActiveQa(row.id)"
            >
              <i data-feather="slash" class="mx-50"></i>
            </a>

						<a
							*ngIf="!row.is_active"
							href="javascript:void(0);"
							class="hide-arrow color-333"
							id="dropdownBrowserState"
							data-toggle="dropdown"
							aria-haspopup="true"
							aria-expanded="false"
							placement="top"
							ngbTooltip="Kích hoạt lại"
							container="body"
							(click)="updateActiveQa(row.id)"
						>
							<i data-feather="check-circle" class="mx-50"></i>
						</a>

            <a
              href="javascript:void(0);"
              class="hide-arrow color-333"
              id="dropdownBrowserState"
              data-toggle="dropdown"
              aria-haspopup="true"
              aria-expanded="false"
							ngbTooltip="Xóa"
							placement="top"
							container="body"
							(click)="deleteQa(row.id)"
            >
              <i data-feather="trash-2" class="mx-50"></i>
            </a>
          </span>
        </ng-template>
      </ngx-datatable-column>
    </ngx-datatable>
  </div>
</div>