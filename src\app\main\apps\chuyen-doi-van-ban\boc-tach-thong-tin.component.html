<div class="boc-tach-thong-tin-container p-0">
  <div class="boc-tach-thong-tin-card card m-0">
    <div class="boc-tach-thong-tin-card-body card-body">
      <div class="select-file w-100 d-flex mb-2 mt-1">
        <span class="">
          <label for="basicInput">Tìm kiếm</label>
          <div class="col-xl-12 p-0">
            <div class="form-group">
              <input
                type="text"
                class="form-control"
                id="basicInput"
                placeholder="Tìm kiếm tài liệu"
                [formControl]="searchDocument"
              />
            </div>
          </div>
        </span>
        <span class="d-flex align-items-center ml-auto">
          <label for="btn"></label>
          <button
            class="btn btn-outline-primary text-primary"
            id="btn"
            rippleEffect
            (click)="fileInput.click(); fileInput.value = ''"
            [disabled]="isUploading"
          >
            <span *ngIf="isUploading" class="d-flex align-items-center">
              <span
                class="spinner-border spinner-border-sm mr-50"
                role="status"
                aria-hidden="true"
              ></span>
              Đang tải lên...
            </span>
            <span *ngIf="!isUploading">Tải lên tài liệu (PDF)</span>
            <!-- <span [data-feather]="'upload'" [class]="'ml-25'"></span> -->
            <input
              class="boc-tach-thong-tin-file-input"
              type="file"
              #fileInput
              (change)="addFile($event); fileInput.value = ''"
              accept=".pdf"
              multiple
            />
          </button>
        </span>
      </div>
      <table class="table" *ngIf="listFileOCR?.length > 0">
        <thead>
          <tr>
            <th width="5%">STT</th>
            <th width="40%">{{ "Filename" | translate }}</th>
            <th width="20%" class="text-center">Trạng thái chuyển đổi</th>
            <th width="20%">Ngày tạo</th>
            <th class="text-end">{{ "Action" | translate }}</th>
          </tr>
        </thead>
        <tbody class="cursor-pointer">
          <tr *ngFor="let item of listFileOCR; let i = index">
            <td>
              <span>{{ i + (page - 1) * 12 + 1 }}</span>
            </td>
            <td (click)="viewFile(item)">
              <strong>{{ item?.name }}</strong>
            </td>
            <td class="text-center" (click)="viewFile(item)">
              <div
                class="badge badge-pill"
                [ngClass]="{
                  'badge-light-danger':
                    item?.status == documentStatus.STATUS_FAILED,
                  'badge-light-success':
                    item?.status == documentStatus.STATUS_SUCCESS,
                  'badge-light-warning':
                    item?.status == documentStatus.STATUS_PENDING,
                  'badge-light-info':
                    item?.status == documentStatus.STATUS_PROCESSING
                }"
              >
                {{ item?.status_display }}
              </div>
            </td>
            <td (click)="viewFile(item)">
              <p>{{ item.created_at | date : "HH:mm dd/MM/yyyy" }}</p>
            </td>
            <td nowrap class="actions-cell">
              <button
                *ngIf="item.status == 1 && item.types == 0"
                type="button"
                class="btn text-success btn-raised btn-outline-success btn-sm mr-50"
                (click)="downloadFileConvert(item, 'docx')"
                rippleEffect
                placement="left"
                container="body"
                ngbTooltip="Tải file chuyển đổi"
              >
                <span data-feather="download"></span>
              </button>
              <button
                *ngIf="item.status == documentStatus.STATUS_FAILED"
                type="button"
                class="btn text-success btn-raised btn-outline-success btn-sm mr-50"
                (click)="reLoadFile(item)"
                [disabled]="item.isLoading"
                rippleEffect
                placement="left"
                container="body"
                ngbTooltip="Bóc tách lại"
              >
                <span data-feather="refresh-ccw"></span>
              </button>

              <button
                *ngIf="item.types == 0"
                type="button"
                class="btn text-success btn-raised btn-outline-success btn-sm mr-50"
                (click)="download(item, 'pdf')"
                [disabled]="item.isReady || item.isUploading"
                rippleEffect
                placement="left"
                container="body"
                ngbTooltip="Tải file gốc"
              >
                <span data-feather="download-cloud"></span>
              </button>
              <button
                *ngIf="
                  item.status != documentStatus.STATUS_PROCESSING &&
                  item.status != documentStatus.STATUS_PENDING
                "
                type="button"
                class="btn text-info btn-raised btn-outline-info btn-sm mr-50"
                (click)="openEditModal(item)"
                [disabled]="item.isLoading"
                rippleEffect
                placement="left"
                container="body"
                ngbTooltip="Đổi tên tài liệu"
              >
                <span data-feather="edit"></span>
              </button>
              <button
                *ngIf="
                  item.status != documentStatus.STATUS_PROCESSING &&
                  item.status != documentStatus.STATUS_PENDING
                "
                type="button"
                class="btn text-danger btn-raised btn-outline-danger btn-sm mr-50"
                (click)="deleteDocument(item)"
                [disabled]="item.isLoading || itemDeleting.includes(item.id)"
                rippleEffect
                placement="left"
                container="body"
                ngbTooltip="Xóa tài liệu"
              >
                <span *ngIf="!itemDeleting.includes(item.id)"
                  ><span data-feather="trash"></span
                ></span>
                <span
                  class="spinner-border spinner-border-sm"
                  role="status"
                  aria-hidden="true"
                  *ngIf="itemDeleting.includes(item.id)"
                ></span>
              </button>
            </td>
          </tr>
        </tbody>
      </table>
      <ng-container *ngIf="listFileOCR?.length == 0">
        <div
          class="d-flex h-75 align-items-center justify-content-center flex-column"
        >
          <img src="assets/images/icons/no-file.svg" alt="no-file" />
          <h4 class="mt-2">Không có văn bản</h4>
        </div>
      </ng-container>
      <span class="d-flex w-100 justify-content-center" *ngIf="total > 10">
        <ngb-pagination
          [maxSize]="3"
          pageSize="12"
          [collectionSize]="total"
          [(page)]="page"
          aria-label="Default pagination"
          [rotate]="true"
          [ellipses]="false"
          [boundaryLinks]="true"
          (pageChange)="onPageChange($event)"
        >
        </ngb-pagination>
      </span>
    </div>
  </div>
  <span class="boc-tach-thong-tin-close-button" (click)="closeModal()">
    <img src="assets/images/icons/x.svg" alt="x" />
  </span>
</div>
<ng-template #editDocumentNameModal let-modal>
  <div class="modal-body">
    <div class="col-12 p-0">
      <div class="form-group">
        <label
          for="basicTextarea"
          class="w-100 align-items-center d-flex justify-content-between"
          >Chỉnh sửa tên tài liệu
          <div class="">
            <button
              class="btn btn-sm ml-auto p-0"
              (click)="modal.dismiss('Cross click')"
            >
              <img src="assets/images/icons/x.svg" alt="x" />
            </button></div
        ></label>
      </div>
    </div>
    <div class="col-12 p-0">
      <form #editNameForm="ngForm" (ngSubmit)="updateName(modal)">
        <div class="form-group">
          <div class="d-flex align-items-center justify-content-between">
            <label for="documentName">Tên tài liệu</label>

            <div class="text-muted font-sm text-right">
              {{ fileName.value.length }}/255
            </div>
          </div>
          <input
            type="text"
            id="documentName"
            class="form-control"
            [formControl]="fileName"
            name="documentName"
            required
            maxlength="255"
          />
        </div>

        <div class="modal-footer">
          <button type="submit" class="btn btn-primary">Lưu</button>
          <button
            type="button"
            class="btn btn-secondary"
            (click)="modal.dismiss()"
          >
            Hủy
          </button>
        </div>
      </form>
    </div>
  </div>
</ng-template>
<ng-template #viewFileModal let-modal>
  <app-view-detail-file [modal]="modal" [type]="type"></app-view-detail-file>
</ng-template>
