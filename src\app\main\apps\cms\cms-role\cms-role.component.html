<div class="d-flex justify-content-between mb-1">
	<div class="d-flex">
		<ng-select 
			class="me-2 mr-1 width-170px"
			[items]="[
				{ label: 'Ngư<PERSON>i viết', value: 'WRITER' },
				{ label: 'Ngư<PERSON><PERSON> phê duyệt', value: 'REVIEWER' },
				{ label: 'Admin', value: 'ADMIN' }
			]"
			bindLabel="label"
			bindValue="value"
			placeholder="Chọn vai trò"
			[clearable]="true"
			[(ngModel)]="selectedRole"
			(ngModelChange)="onRoleChange()"
		>
		</ng-select>
		<input 
			type="text" 
			class="form-control width-240px" 
			placeholder="Tìm email hoặc tên..."
			[(ngModel)]="search"
			(ngModelChange)="searchChanged.next($event)"
		/>
	</div>

	<div class="">
		<button type="button" class="btn btn-primary" (click)="openModal(modalAddUser)">
			<span data-feather="plus" class="me-50"></span>
			Thêm người dùng
		</button>
	</div>
</div>

<ngx-datatable
	#ContentTable
	[rows]="cmsUserList"
	[rowHeight]="'auto'"
	class="bootstrap core-bootstrap cursor"
	[columnMode]="ColumnMode.force"
	[headerHeight]="40"
	[footerHeight]="50"
	[scrollbarH]="true"
	[limit]="12"
	[count]="cmsUserTotal"
	[externalPaging]="true"
	[offset]="page - 1"
	(activate)="onActivate($event)"
	(page)="onPage($event)"
>
	<ngx-datatable-column name="Tên người dùng" prop="user.fullname" [sortable]="false"></ngx-datatable-column>
	<ngx-datatable-column name="Email" prop="user.email" [sortable]="false"></ngx-datatable-column>
	<ngx-datatable-column name="Vai trò" prop="role" [sortable]="false">
		<ng-template ngx-datatable-cell-template let-rowIndex="rowIndex" let-value="value" let-row="row">
			<div 
				(dblclick)="editingRole[rowIndex + '-role'] = true"
				*ngIf="!editingRole[rowIndex + '-role']"
				ngbTooltip="Nhấp đúp để sửa"
				container="body"
			>
				<span 
					class="badge mr-50"
					[ngClass]="{
						'badge-light-danger': row.role === 'ADMIN',
						'badge-light-primary': row.role === 'REVIEWER',
						'badge-light-dark': row.role === 'WRITER'
					}"
				>
					{{ 
						row.role === 'ADMIN' ? 'Admin' 
						: row.role === 'REVIEWER' ? 'Người phê duyệt' 
						: row.role === 'WRITER' ? 'Người viết' 
						: row.role 
					}}
				</span>
			</div>

			<select
				*ngIf="editingRole[rowIndex + '-role']"
				(blur)="editingRole[rowIndex + '-role'] = false"
				(change)="inlineEditingUpdateRole($event, 'role', rowIndex)"
				[value]="value"
				class="form-control form-control-sm"
			>
				<option value="ADMIN">Admin</option>
				<option value="REVIEWER">Người phê duyệt</option>
				<option value="WRITER">Người viết</option>
			</select>
		</ng-template>
	</ngx-datatable-column>
</ngx-datatable>


<ng-template #modalAddUser let-modal>
	<div class="modal-header">
		<h5 class="modal-title" id="exampleModalCenterTitle">Thêm người dùng mới</h5>
		<button type="button" class="close" (click)="modal.dismiss('Cross click')" aria-label="Close">
			<span aria-hidden="true">&times;</span>
		</button>
	</div>
	<div
		class="modal-body min-height-80vh"
		tabindex="0"
		ngbAutofocus>
		<form action="#" class="invoice-repeater">
			<div *ngFor="let u of addUserList; let i = index;" @heightIn>
				<div class="row align-items-end">
					<div class="col-12 col-md-6 mb-1">
						<div class="form-group mb-0">
							<label for="user_id{{ i }}">Người dùng</label>
							<ng-select
								[items]="userList"
								bindLabel="fullname"
								bindValue="id"
								[(ngModel)]="u.user_id"
								[name]="'user_id' + i"
								[id]="'user_id' + i"
								placeholder="Chọn người dùng"
								[clearable]="false"
								class="w-100"
								(change)="updateUserListDisabled()"
							>
								<ng-template ng-option-tmp let-item="item">
									{{item.fullname}} <br>
									<span *ngIf="item.email">&lt;{{item.email}}&gt;</span>
								</ng-template>
							</ng-select>
						</div>
					</div>
	
					<div class="col-12 col-md-4 mb-1">
						<div class="form-group mb-0">
							<label for="role{{ i }}">Vai trò</label>
							<ng-select
								[items]="[
									{label: 'Admin', value: 'ADMIN'},
									{label: 'Người phê duyệt', value: 'REVIEWER'},
									{label: 'Người viết', value: 'WRITER'}
								]"
								bindLabel="label"
								bindValue="value"
								[(ngModel)]="u.role"
								name="role{{ i }}"
								id="role{{ i }}"
								placeholder="Chọn vai trò"
								[clearable]="false"
								class="w-100"
							>
							</ng-select>
						</div>
					</div>

					<div class="col-12 col-md-2 mb-1 d-flex align-items-end">
						<div class="form-group mb-0 w-100">
							<label class="invisible">Xóa</label>
							<button type="button" class="btn btn-icon btn-outline-danger w-100" (click)="deleteUser(i)" rippleEffect>
								<span [data-feather]="'x'"></span>
							</button>
						</div>
					</div>
				</div>
				<hr />
			</div>
		</form>

		<div class="mt-1">
			<button class="btn btn-sm btn-outline-primary" type="button" (click)="addUserToList()" rippleEffect>
				<i data-feather="plus" class="mr-25"></i>
				<span>Thêm mới</span>
			</button>
		</div>
	</div>
	<div class="modal-footer">
		<button 
			type="button" 
			class="btn btn-primary" 
			(click)="create()" 
			rippleEffect
			[disabled]="isAddUserDisabled">
			Xác nhận
		</button>
	</div>
</ng-template>