/**
 * Constants for graph component
 */

export const NODE_COLORS = {
  DEFAULT: '#42a5f5',
  ARTICLE: '#ef5350',
  DOCUMENT: '#64b5f6',
};

export const NODE_SIZES = {
  ROOT: 100,
  REGULAR: 80,
  MIN_LABEL_SIZE: 30,
} as const;

export const CHART_CONFIG = {
  FORCE: {
    REPULSION: 800,
    GRAVITY: 0.08,
    EDGE_LENGTH: 220,
  },
  LINE_STYLE: {
    WIDTH: 2,
    WIDTH_EMPHASIS: 10,
  },
  EDGE_SYMBOL: {
    SIZE: [4, 10],
    TYPES: ['circle', 'arrow'] as const,
  },
} as const;

export const RELATIONSHIP_DIRECTIONS = {
  OUTGOING: 'OUTGOING',
  INCOMING: 'INCOMING',
} as const;

export const NODE_TYPES = {
  VAN_BAN: 'VAN_BAN',
  DIEU_KHOAN: 'DIEU_KHOAN',
} as const;

export const RELATIONSHIP_TYPES = {
  BAI_BO: 'bai_bo',
  BAO_GOM: 'bao_gom',
  BO_SUNG: 'bo_sung',
  CAN_CU: 'can_cu',
  DAN_CHIEU: 'dan_chieu',
  DINH_CHI: 'dinh_chi',
  HUONG_DAN: 'huong_dan',
  QUY_DINH_CHI_TIET: 'quy_dinh_chi_tiet',
  SUA_DOI: 'sua_doi',
  SUA_DOI_BO_SUNG: 'sua_doi_bo_sung',
  THAY_THE: 'thay_the',
} as const;

export const VALIDATION_LIMITS = {
  DEPTH: { MIN: 1, MAX: 3 },
  GLOBAL_LIMIT: { MIN: 1, MAX: 50 },
  LIMIT_PER_SEED: { MIN: 1, MAX: 10 },
} as const;

export const TOAST_CONFIG = {
  SUCCESS: {
    closeButton: true,
    positionClass: 'toast-top-right',
    toastClass: 'toast ngx-toastr',
  },
  ERROR: {
    closeButton: true,
    positionClass: 'toast-top-right',
    toastClass: 'toast ngx-toastr',
  },
} as const;

export const ERROR_MESSAGES = {
  NO_DOCUMENT_ID: 'Không tìm thấy ID tài liệu',
  NO_NODE_INFO: 'Không tìm thấy thông tin node',
  NO_NODE_ID: 'Không tìm thấy ID node',
  CANNOT_HIDE_ROOT: 'Không thể ẩn node gốc',
  NO_NODES_TO_HIDE: 'Không có node mới nào được ẩn',
  NO_NODES_TO_RESTORE: 'Không có node nào để khôi phục',
  GRAPH_UPDATE_FAILED: 'Không tìm thấy dữ liệu đồ thị',
  EXPANSION_FAILED: 'Không thể mở rộng đồ thị',
} as const;

export const SUCCESS_MESSAGES = {
  GRAPH_UPDATED: 'Cập nhật dữ liệu đồ thị văn bản điều khoản',
  EXPANSION_SUCCESS: 'Mở rộng đồ thị thành công',
  NODES_HIDDEN: 'Đã ẩn các node liên quan',
  NODES_RESTORED: 'Khôi phục các node thành công',
} as const;

export const INFO_MESSAGES = {
  NO_NEW_NODES_HIDDEN: 'Không có node mới nào được ẩn',
} as const;

