import {
  Component,
  ElementRef,
  Input,
  OnInit,
  SimpleChanges,
  ViewChild,
  ViewEncapsulation,
} from "@angular/core";
import { ActivatedRoute, Router } from "@angular/router";
import { PaginationService } from "@core/components/paginate/paginatioin.service";
import { NgbActiveModal, NgbModal } from "@ng-bootstrap/ng-bootstrap";
import { ColumnMode, DatatableComponent } from "@swimlane/ngx-datatable";
import { CacheDataService } from "app/auth/service/cache-data.service";
import { ViewDetailFileService } from "app/layout/components/view-detail-file/view-detail-file.service";
import { CoQuanBanHanhTinhMoiBoCu } from "app/models/CoQuanBanHanhMoiTinhMoi";
import { CoQuanBanHanhTinhCuBoCu } from "app/models/CoQuanBanHanhTinhCu";
import { CoQuanBanHanhTinhCuBoMoi } from "app/models/CoQuanBanHanhTinhCuBoMoi";
import { CoQuanBanHanhTinhMoiBoMoi } from "app/models/CoQuanBanHanhTinhMoiBoMoi";
import { CoQuanQuanLyTinhCuBoCu } from "app/models/CoQuanQuanLyTinhCuBoCu";
import { CoQuanQuanLyTinhCuBoMoi } from "app/models/CoQuanQuanLyTinhCuBoMoi";
import { CoQuanQuanLyTinhMoiBoCu } from "app/models/CoQuanQuanLyTinhMoiBoCu";
import { CoQuanQuanLyTinhMoiBoMoi } from "app/models/CoQuanQuanLyTinhMoiBoMoi";
import { DocumentStatus } from "app/models/DocumentStatus";
import { FormType } from "app/models/FormType";
import { LoaiVanBan } from "app/models/LoaiVanBan";
import { ShowContent } from "app/models/ShowContent";
import { ShowSideBar } from "app/models/ShowSideBa";
import { TrangThaiHieuLuc } from "app/models/TrangThaiHieuLuc";
import feather from "feather-icons";
import { FlatpickrOptions } from "ng2-flatpickr";
import { ToastrService } from "ngx-toastr";
import { Subject } from "rxjs";
import { finalize } from "rxjs/operators";
import { DetailWorkSpaceService } from "../quan-ly-van-ban/detail-work-space/detail-work-space.service";
import { ListDocumentService } from "../quan-ly-van-ban/detail-work-space/list-document/list-document.service";
import { QuanLyVanBanService } from "../quan-ly-van-ban/quan-ly-van-ban.service";
import { TimKiemThongMinhService } from "./tim-kiem-thong-minh.service";
import { SearchLegalTerm } from "app/models/SearchLegalTerm";
@Component({
  selector: "app-tim-kiem-ai",
  templateUrl: "./tim-kiem-thong-minh.component.html",
  styleUrls: ["./tim-kiem-thong-minh.component.scss"],
  encapsulation: ViewEncapsulation.None,
})
export class TimKiemThongMinhComponent implements OnInit {
  @ViewChild("tableRowDetails") tableRowDetails: DatatableComponent;
  @ViewChild(DatatableComponent) table: DatatableComponent;
  @Input("modal") public modal: NgbActiveModal;
  @ViewChild("viewFileModal") public viewFileModal: any;
  @Input("type") public type: FormType;
  @ViewChild("valueText") valueTextRef!: ElementRef;
  readonly SLT = SearchLegalTerm;

  public FormType = FormType;
  public threshold: number = 0.5;
  public sizePage = [250, 500, 1000, 2000];
  public isSearchAdvance: boolean = false;
  public page: number = 1;
  public totalItem: number = 0;
  public totalDieuKhoan: number = 0;
  public filteredCount: number = 0;
  public ColumnMode = ColumnMode.flex;
  public limit: number = this.sizePage[0];
  public limitTable = 20;
  public dataTimKiemThongThuong: any = [];
  public dataTimKiemThongThuongFilter: any = [];
  public statistic: any = [];
  public searchString: string;
  public filterString: string;
  public dataExport: any;
  public typeDoiChieu: any;
  public listId: [] = [];
  public valueTrangThai: any = [];
  public types: FormType;
  public valueCoQuanBanHanh: [] = [];
  public valueCoQuanQuanLy: [] = [];
  public listLoaiVanBan: any = LoaiVanBan;
  public valueLoaiVanBan: string[] = [];
  public valueLinhVucVanBan: string[] = [];
  public isShowFastSearch: boolean = false;
  public listCoQuanBanHanh: any = CoQuanBanHanhTinhCuBoCu;
  public listCoQuanQuanLy: any = CoQuanQuanLyTinhCuBoCu;
  public listTrangThaiHieuLuc: any = TrangThaiHieuLuc;
  public pageDefault: number = 1;
  isShowTable: boolean = false;
  public currentPage = 1;
  public itemsPerPage = 5;
  public search_legal_term: SearchLegalTerm = SearchLegalTerm.TieuDe;
  public filterBody = { sort: "loai_van_ban", order: "desc" };
  selectedSort: string = "Sắp xếp";
  public typeSort: string = "arrow-down-circle";
  public noData: boolean = false;
  public valueNgayBanHanh: string = null;
  public customDateOptions: FlatpickrOptions = {
    altFormat: "j-m-Y",
    enableTime: false,
    altInput: true,
    mode: "range",
  };
  public valueNgayCoHieuLuc: string = null;
  public customDateOptionsCoHieuLuc: FlatpickrOptions = {
    altFormat: "j-m-Y",
    enableTime: false,
    altInput: true,
    mode: "range",
  };
  public ten_van_ban: string = "";
  public so_hieu: string = "";
  public indexTerm: number = -1;
  public isShowTerm: boolean = false;
  actionFile = [
    "Toàn văn",
    // "Tổng quan",
    "Lược đồ",
    // "Tải xuống PDF",
    // "Tải xuống DOCX",
    "Lưu",
  ];
  public totalValueThongKe: number = 0;
  public isReload = false;
  hoveredIndex: { [key: number]: number | null } = {};
  searchQuery: string = "";
  searchResults: any[] = [];
  public suggestOptions: string[] = [];
  public must: string = "";
  public should: string = "";
  public not: string = "";
  public currentLabelFilter: string = "Tất cả";
  public unSubAll: Subject<any> = new Subject();
  public selectedFiles: any[] = [];
  public DocumentStatus = DocumentStatus;
  public hideSearchAdvanced: boolean = false;
  public valueThongKe: string = "Thống kê";
  public listThongKeVanBan = [];
  // Add a property to store the scroll container selector
  private scrollContainerSelector = ".virtual-scroll-viewport";
  public sat_nhap_tinh: boolean = false;
  public boMoi: boolean = false;

  public listTheLoaiVanBan = [
    // { label: "Tất cả", value: "tất cả" },
    { label: "Văn bản hành chính", value: "văn bản hành chính" },
    { label: "Văn bản quy phạm", value: "văn bản quy phạm" },
  ];
  public valueTheLoaiVanBan: any[] = [];
  termStates: boolean[] = [];
  public is_paging: boolean = false;
  public listDanhSachLinhVuc = [];
  isFetchingLinhVuc: boolean = false;
  isSearching: boolean = false;
  isExporting: boolean = false;
  constructor(
    private _timKiemThongMinh: TimKiemThongMinhService,
    private paginationService: PaginationService,
    private toast: ToastrService,
    private router: Router,
    public cacheDataService: CacheDataService,
    private quanlyvanban: QuanLyVanBanService,
    private route: ActivatedRoute,
    private viewDetailFileService: ViewDetailFileService,
    private workspace: DetailWorkSpaceService,
    private listDocumentService: ListDocumentService,
    private modalService: NgbModal
  ) {}

  ngOnInit(): void {
    this.searchString = "";
    this.filterString = "";
    this.dataTimKiemThongThuong = [];
    this.statistic = [];
    this.dataExport = [];
    this.listDocumentService.rightSideBarValue.next(ShowSideBar.Chatbot);
    const cacheData = this.cacheDataService.getcacheData();
    if (cacheData) {
      this.dataTimKiemThongThuong = cacheData.data.data;
      this.dataTimKiemThongThuongFilter = cacheData.data.data;
      this.searchString = cacheData.query;
      this.search_legal_term = cacheData.type;
      this.dataExport = cacheData.data;
      this.totalItem = cacheData.data.total || 0;
      this.totalDieuKhoan = cacheData.data.data.length || 0;
      this.filteredCount = cacheData.data.total || 0;
      const allStatistic = {
        label: "Tất cả",
        count: this.dataTimKiemThongThuong?.length || 0,
      };
      this.statistic = cacheData.data.statistic.filter(
        (item) => item.count > 0
      );
      if (allStatistic.count > 0) {
        this.statistic.unshift(allStatistic);
      }
    }
    const cacheFilter = this.cacheDataService.getFilter();
    if (cacheFilter) {
      this.isSearchAdvance = cacheFilter.isSearchAdvance;
      this.hideSearchAdvanced = true;
      this.limit = cacheFilter.limit;
      // this.totalItem = cacheFilter.totalItem;
      // this.totalDieuKhoan = cacheFilter.totalDieuKhoan;
      this.filterString = cacheFilter.filterString;
      this.listId = cacheFilter.listId;
      this.valueCoQuanBanHanh = cacheFilter.valueCoQuanBanHanh;
      this.valueCoQuanQuanLy = cacheFilter.valueCoQuanQuanLy;
      this.valueLoaiVanBan = cacheFilter.valueLoaiVanBan;
      this.valueTrangThai = cacheFilter.valueTrangThai;
      this.page = cacheFilter.page;
      // this.search_legal_term = cacheFilter.search_legal_term;
      this.valueNgayBanHanh = cacheFilter.valueNgayBanHanh;
      this.valueNgayCoHieuLuc = cacheFilter.valueNgayCoHieuLuc;
      this.customDateOptionsCoHieuLuc.defaultDate = cacheFilter.valueNgayCoHieuLuc;
      this.must = cacheFilter.must;
      this.should = cacheFilter.should;
      this.not = cacheFilter.not;
      this.ten_van_ban = cacheFilter.ten_van_ban;
      this.so_hieu = cacheFilter.so_hieu;
      this.customDateOptions.defaultDate = cacheFilter.valueNgayBanHanh;
      this.threshold = cacheFilter.threshold;
      this.sat_nhap_tinh = cacheFilter.sat_nhap_tinh;
      this.valueTheLoaiVanBan = cacheFilter.the_loai_van_ban;
      this.isShowTable = cacheFilter.isShowTable;
      // // console.log("cacheFilter", cacheFilter);
    }

    const cacheCheckbox = this.cacheDataService.getCheckbox();
    if (cacheCheckbox) {
      this.selectedFiles = cacheCheckbox;
    }
    const cacheTermState = this.cacheDataService.getStateToggleClause();
    if (cacheTermState) {
      this.termStates = cacheTermState;
    }
    this.listDocumentService.FileSearchTemp.next(null); // xoá hết các file term bên danh sách tài liệu
    if (this.type == FormType.Search) {
      this.actionFile = ["Toàn văn", "Lược đồ"]; // chỗ tìm kiếm ngoài không gian dự án không có action lưu
    }

    if (this.sat_nhap_tinh) {
      this.listCoQuanBanHanh = CoQuanBanHanhTinhMoiBoCu;
      this.listCoQuanQuanLy = CoQuanQuanLyTinhMoiBoCu;
    } else {
      this.listCoQuanBanHanh = CoQuanBanHanhTinhCuBoCu;
      this.listCoQuanQuanLy = CoQuanQuanLyTinhCuBoCu;
    }
    this.getThongKeVanBan();
    // Restore scroll position if available
    setTimeout(() => {
      this.restoreScrollPosition();
    }, 0);
  }

  // Save scroll position to cache
  private saveScrollPosition() {
    const container = document.querySelector(this.scrollContainerSelector);
    if (container) {
      this.cacheDataService.setScrollPosition(
        (container as HTMLElement).scrollTop
      );
    }
  }

  // Restore scroll position from cache
  private restoreScrollPosition() {
    const scrollTop = this.cacheDataService.getScrollPosition();
    if (typeof scrollTop === "number") {
      const container = document.querySelector(this.scrollContainerSelector);
      if (container) {
        (container as HTMLElement).scrollTop = scrollTop;
      }
    }
  }

  isChecked(id: any): boolean {
    return this.selectedFiles.some((f) => f.id === id);
  }
  rowDetailsToggleExpand(row) {
    this.tableRowDetails.rowDetail.toggleExpandRow(row);
  }
  getThongKeVanBan() {
    this._timKiemThongMinh.getThongKeVanBan().subscribe((res: any) => {
      this.listThongKeVanBan = res;
    });
    this.isFetchingLinhVuc = true;
    this._timKiemThongMinh.getLinhVucTraCuu()
    .pipe(finalize(() => { this.isFetchingLinhVuc = false; }))
    .subscribe({
        next: (res) => {
          if (res) {
            this.listDanhSachLinhVuc = res;
          }
        },
        error: (error) => {
          console.error("Error fetching Linh Vuc:", error);
        }
      });
  }
  suggestSearch(event) {
    // // console.log(this.searchString, event);
    // UPdate searchString
    this.searchString = event.query;
    this._timKiemThongMinh.suggestSearchPhrase(event.query).subscribe(
      (res) => {
        // // console.log(res);
        this.suggestOptions = res;
      },
      (error) => {
        // console.log(error);
      }
    );
  }
  searchDieuKhoan() {

    this.isReload = true;
    this.filterBody = { sort: "loai_van_ban", order: "desc" };
    if (this.valueThongKe != "Thống kê") {
      this.valueThongKe = "Thống kê";
      this.valueLoaiVanBan = [];
    }
    this.selectedSort = "Sắp xếp";
    this.typeSort = "arrow-down-circle";
    this.currentLabelFilter = "Tất cả";
    this._timKiemThongMinh
      .searchDieuKhoan({
        limit: this.limit,
        filter: this.filterString,
        search: this.searchString,
        listId: this.listId,
        valueCoQuanBanHanh: this.valueCoQuanBanHanh,
        valueCoQuanQuanly: this.valueCoQuanQuanLy,
        valueLoaiVanBan: this.valueLoaiVanBan,
        valueTrangThai: this.valueTrangThai,
        page: this.page,
        search_norm: this.search_legal_term === this.SLT.DieuKhoan,
        search_only_legal_title:
          this.search_legal_term === this.SLT.TieuDe ? true : null,
        ngay_ban_hanh: this.valueNgayBanHanh,
        ngay_co_hieu_luc: this.valueNgayCoHieuLuc,
        must: this.must,
        should: this.should,
        not: this.not,
        ten_van_ban: this.ten_van_ban,
        so_hieu: this.so_hieu,
        threshold: this.threshold,
        sat_nhap_tinh: this.sat_nhap_tinh,
        loai_hinh_tai_lieu: Array.isArray(this.valueTheLoaiVanBan) ? this.valueTheLoaiVanBan.join(', ') : this.valueTheLoaiVanBan,
        boMoi: this.boMoi,
        linh_vuc: this.valueLinhVucVanBan,
        sort: "loai_van_ban",
        order: "desc",
      })
      .subscribe(
        (res) => {
          this.isReload = false;
          this.currentPage = 1;
          if (res.total == 0) {
            this.noData = true;
          } else {
            this.noData = false;
          }
          const cache = {
            data: res,
            query: this.searchString,
            type: this.search_legal_term,
          };
          this.cacheDataService.setcacheData(cache);
          this.dataExport = res;
          this.dataTimKiemThongThuong = res.data;
          this.dataTimKiemThongThuongFilter = res.data;
          this.termStates = new Array(
            this.dataTimKiemThongThuongFilter.length
          ).fill(false);
          const allStatistic = {
            label: "Tất cả",
            count: this.dataTimKiemThongThuong?.length || 0,
          };
          this.statistic = res.statistic.filter((item) => item.count > 0);
          if (allStatistic.count > 0) {
            this.statistic.unshift(allStatistic);
          }

          this.toast.info(res.msg, "Tìm kiếm", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          this.totalItem = res.total;
          this.totalDieuKhoan = res.data ? res.data.length : 0;
          this.filteredCount = res.total;
        },
        (error) => {
          this.isReload = false;
        }
      );
  }
  changeTypeSearch(e) {
    this.hideSearchAdvanced = false;
    if (e.target.checked) {
      this.isSearchAdvance = true;
    } else {
      this.isSearchAdvance = false;
      this.filterString = "";
      this.listId = [];
      this.valueCoQuanBanHanh = [];
      this.valueCoQuanQuanLy = [];
      this.valueLoaiVanBan = [];
      this.valueTrangThai = [];
      this.valueNgayBanHanh = null;
      this.valueNgayCoHieuLuc = null;
      this.must = "";
      this.should = "";
      this.not = "";
      this.ten_van_ban = "";
      this.so_hieu = "";
    }
  }

  ngAfterViewInit() {
    feather.replace();
  }

  ngOnChanges(changes: SimpleChanges) {
    if ("isSearchAdvance" in changes) {
      // schedule after Angular updates the DOM
      Promise.resolve().then(() => feather.replace());
    }
  }
  toggleTypeSearch() {
    // this.search_legal_term = !this.search_legal_term;
    this.dataTimKiemThongThuongFilter = [];
    this.termStates = new Array(this.dataTimKiemThongThuongFilter.length).fill(
      false
    );
    this.selectedFiles = [];
  }

  onSearchLegalTermChange(_value: SearchLegalTerm) {
    this.toggleTypeSearch();
    this.runSearchForActiveTab();
  }

  private runSearchForActiveTab(): void {
    // Allow search to run even without searchString when filters are active
    const hasActiveFilters =
      (this.valueLinhVucVanBan && this.valueLinhVucVanBan.length > 0) ||
      (this.valueLoaiVanBan && this.valueLoaiVanBan.length > 0) ||
      (this.dataTimKiemThongThuong && this.dataTimKiemThongThuong.length > 0);

    if (!this.searchString?.trim() && !hasActiveFilters) {
      return;
    }

    this.searchDieuKhoan();
  }

  changeTypeView(e) {
    if (e.target.checked) {
      this.isShowTable = true;
    } else {
      this.isShowTable = false;
    }
  }
  onActivate(e) {
    if (e.type == "click" && e.column.name) {
      const filter = {
        limit: this.limit,
        totalItem: this.totalItem,
        totalDieuKhoan: this.totalDieuKhoan,
        filterString: this.filterString,
        listId: this.listId,
        valueCoQuanBanHanh: this.valueCoQuanBanHanh,
        valueCoQuanQuanLy: this.valueCoQuanQuanLy,
        valueLoaiVanBan: this.valueLoaiVanBan,
        valueTrangThai: this.valueTrangThai,
        page: this.page,
        search_legal_term: this.search_legal_term,
        search_only_legal_title:
          this.search_legal_term === this.SLT.TieuDe ? true : null,
        valueNgayBanHanh: this.valueNgayBanHanh,
        valueNgayCoHieuLuc: this.valueNgayCoHieuLuc,
        must: this.must,
        should: this.should,
        not: this.not,
        ten_van_ban: this.ten_van_ban,
        so_hieu: this.so_hieu,
        threshold: this.threshold,
        isSearchAdvance: this.isSearchAdvance,
        sat_nhap_tinh: this.sat_nhap_tinh,
        the_loai_van_ban: this.valueTheLoaiVanBan,
        isShowTable: this.isShowTable,
      };

      this.cacheDataService.setFilter(filter);
      this.viewDetailFileService.fileInfor.next(e.row);
      this.router.navigate([], {
        queryParams: {
          fileId: e.row.id,
          tabs: "toanvan",
          type: "searching",
          time: new Date().getTime(),
          fileName: this.transform(e.row.trich_yeu),
          save: true,
        },
        queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
      });
      this.listDocumentService.FileSearchTemp.next(e.row);
      this.listDocumentService.setBehavior(ShowContent.Search);
      if (this.type == FormType.Search) {
        // trường hợp xem ngoài không gian dự án
        this.modalOpen(this.viewFileModal, FormType.Convert, "xl");
      }
    }
  }
  transform(value: string): string {
    return value.replace(/<[^>]+>/g, ""); // Xóa tất cả thẻ HTML
  }
  viewDetailFile(action, file) {
    this.saveScrollPosition(); // Save scroll position before navigating
    const filter = {
      limit: this.limit,
      totalItem: this.totalItem,
      totalDieuKhoan: this.totalDieuKhoan,
      filterString: this.filterString,
      listId: this.listId,
      valueCoQuanBanHanh: this.valueCoQuanBanHanh,
      valueCoQuanQuanLy: this.valueCoQuanQuanLy,
      valueLoaiVanBan: this.valueLoaiVanBan,
      valueTrangThai: this.valueTrangThai,
      page: this.page,
      search_legal_term: this.search_legal_term,
      search_only_legal_title:
        this.search_legal_term === this.SLT.TieuDe ? true : null,
      valueNgayBanHanh: this.valueNgayBanHanh,
      must: this.must,
      should: this.should,
      not: this.not,
      ten_van_ban: this.ten_van_ban,
      so_hieu: this.so_hieu,
      threshold: this.threshold,
      isSearchAdvance: this.isSearchAdvance,
      sat_nhap_tinh: this.sat_nhap_tinh,
      the_loai_van_ban: this.valueTheLoaiVanBan,
      isShowTable: this.isShowTable,
    };

    this.cacheDataService.setFilter(filter);
    switch (action) {
      case "Toàn văn":
        this.viewDetailFileService.fileInfor.next(file);
        this.router.navigate([], {
          queryParams: {
            fileId: typeof file.id == "string" ? file.doc_id : file.id,
            tabs: "toanvan",
            type: "searching",
            time: new Date().getTime(),
            fileName: this.transform(file.trich_yeu),
            save: true,
          },
          queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
        });
        this.listDocumentService.FileSearchTemp.next(file);
        this.listDocumentService.setBehavior(ShowContent.Search);
        this.viewDetailFileService.clauseId2.next(null);
        if (this.type == FormType.Search) {
          // trường hợp xem ngoài không gian dự án
          this.modalOpen(this.viewFileModal, FormType.Convert, "xl");
        }
        break;
      case "Tổng quan":
        this.viewDetailFileService.fileInfor.next(file);
        this.router.navigate([], {
          queryParams: {
            fileId: file.id,
            tabs: "tongquan",
            type: "searching",
            time: new Date().getTime(),
            fileName: this.transform(file.trich_yeu),
            save: true,
          },
          queryParamsHandling: "merge",
        });
        this.listDocumentService.setBehavior(ShowContent.Search);
        this.listDocumentService.FileSearchTemp.next(file);
        this.viewDetailFileService.clauseId2.next(null);
        break;
      case "Lược đồ":
        this.viewDetailFileService.fileInfor.next(file);
        this.router.navigate([], {
          queryParams: {
            fileId: typeof file.id == "string" ? file.doc_id : file.id,
            tabs: "luocdo",
            type: "searching",
            time: new Date().getTime(),
            fileName: this.transform(file.trich_yeu),
            save: true,
          },
          queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
        });
        this.listDocumentService.setBehavior(ShowContent.Search);
        this.listDocumentService.FileSearchTemp.next(file);
        this.viewDetailFileService.clauseId2.next(null);
        if (this.type == FormType.Search) {
          // trường hợp xem ngoài không gian dự án
          this.modalOpen(this.viewFileModal, FormType.Convert, "xl");
        }
        break;
      case "Sửa đổi":
        this.viewDetailFileService.fileInfor.next(file);

        this.router.navigate([], {
          queryParams: {
            fileId: file.id,
            tabs: "suadoi",
            type: "searching",
            time: new Date().getTime(),
            fileName: this.transform(file.trich_yeu),
            save: true,
          },
          queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
        });
        this.listDocumentService.FileSearchTemp.next(file);
        this.listDocumentService.setBehavior(ShowContent.Search);
        break;
      case "Lưu":
        this.saveHistoryFiles([file]);
        break;
      case "Tải xuống PDF":
        this._timKiemThongMinh.saveFile(file.id, "PDF");
        break;
      case "Tải xuống DOCX":
        this._timKiemThongMinh.saveFile(file.id, "DOCX");
        break;
      case "Tải về":
        break;
    }
  }
  viewDetailFileDieuKhoan(file, term?) {
    const filter = {
      limit: this.limit,
      totalItem: this.totalItem,
      totalDieuKhoan: this.totalDieuKhoan,
      filterString: this.filterString,
      listId: this.listId,
      valueCoQuanBanHanh: this.valueCoQuanBanHanh,
      valueCoQuanQuanLy: this.valueCoQuanQuanLy,
      valueLoaiVanBan: this.valueLoaiVanBan,
      valueTrangThai: this.valueTrangThai,
      page: this.page,
      search_legal_term: this.search_legal_term,
      search_only_legal_title:
        this.search_legal_term === this.SLT.TieuDe ? true : null,
      valueNgayBanHanh: this.valueNgayBanHanh,
      must: this.must,
      should: this.should,
      not: this.not,
      ten_van_ban: this.ten_van_ban,
      so_hieu: this.so_hieu,
      threshold: this.threshold,
      isSearchAdvance: this.isSearchAdvance,
      sat_nhap_tinh: this.sat_nhap_tinh,
      the_loai_van_ban: this.valueTheLoaiVanBan,
      isShowTable: this.isShowTable,
    };

    this.cacheDataService.setFilter(filter);
    this.saveScrollPosition(); // Save scroll position before navigating
    if (term) {
      // click vào điều khoản tìm được, để xử lý cho hiển thị văn bản scroll đến điều
      this.viewDetailFileService.clauseId2.next(term.term_id);
    } else {
      this.viewDetailFileService.clauseId2.next(null);
    }
    this.viewDetailFileService.fileInfor.next(file);
    this.router.navigate([], {
      queryParams: {
        fileId: file.id,
        tabs: "toanvan",
        type: "searching",
        time: new Date().getTime(),
        fileName: this.transform(file.trich_yeu),
        save: true,
      },
      queryParamsHandling: "merge", // Giữ lại các query params khác nếu có
    });
    this.listDocumentService.setBehavior(ShowContent.Search);
    this.listDocumentService.FileSearchTemp.next(file);
    this.cacheDataService.setStateToggleClause(this.termStates); // Lưu trạng tháiu đang xem điều khoản nào khi bấm vào xem văn bản
    if (this.type == FormType.Search) {
      // trường hợp xem ngoài không gian dự án
      this.modalOpen(this.viewFileModal, FormType.Convert, "xl");
    }
  }

  export() {
    this.isExporting = true;
    this._timKiemThongMinh.exportFile(this.dataExport)
    .pipe(finalize(() => { this.isExporting = false; }))
    .subscribe((res) => {
      const link = document.createElement("a");
      link.href = window.URL.createObjectURL(res);
      link.download = this.searchString ? `${this.searchString}.xlsx` : "Kết quả tìm kiếm.xlsx";
      link.click();
    });
  }
  modalOpen(modalSM, type: FormType, size) {
    this.modalService.open(modalSM, {
      centered: true,
      size: size,
    });
    this.types = type;
  }
  selectTypeDoiChieu(e) {
    this.listId = e.map((item) => {
      return item.value;
    });
  }
  selectCoQuanBanHanh(e) {
    this.valueCoQuanBanHanh = e.map((item) => {
      return item.value;
    });
  }
  selectCoQuanQuanLy(e) {
    this.valueCoQuanQuanLy = e.map((item) => {
      return item.value;
    });
  }
  selectLoaiVanBan(e) {
    this.valueLoaiVanBan = e.map((item) => {
      return item.value;
    });
    // const selected = this.valueLoaiVanBan.map((item: any) => item.key);
    // this.valueLoaiVanBan = selected;
    for (const item of this.listThongKeVanBan) {
      if (this.valueLoaiVanBan.includes("")) {
        item.checked = item.key === "Tất cả";
      } else {
        if (this.valueLoaiVanBan.includes(item.key)) {
          item.checked = true;
        } else {
          item.checked = false;
        }
      }
    }
    // // console.log("this", this.valueLoaiVanBan);
    // // console.log("that", this.listThongKeVanBan);
    this.valueThongKe = "Thống kê";
  }
  selectTrangThaiHieuLuc(e) {
    this.valueTrangThai = e.map((item) => {
      return item.value;
    });
  }
  changeNgayBanHanh(e) {
    this.valueNgayBanHanh = e.target.value;
    this.customDateOptions.defaultDate = e.target.value;
  }
  changeNgayCoHieuLuc(e) {
    this.valueNgayCoHieuLuc = e.target.value;
    this.customDateOptionsCoHieuLuc.defaultDate = e.target.value;
  }

  onPageChange(e) {
    this.page = e;
    this._timKiemThongMinh
      .searchDieuKhoan({
        limit: this.limit,
        filter: this.filterString,
        search: this.searchString,
        listId: this.listId,
        valueCoQuanBanHanh: this.valueCoQuanBanHanh,
        valueCoQuanQuanly: this.valueCoQuanQuanLy,
        valueLoaiVanBan: this.valueLoaiVanBan,
        valueTrangThai: this.valueTrangThai,
        page: this.page,
        search_norm: this.search_legal_term === this.SLT.DieuKhoan,
        search_only_legal_title:
          this.search_legal_term === this.SLT.TieuDe ? true : null,
        ngay_ban_hanh: this.valueNgayBanHanh,
        ngay_co_hieu_luc: this.valueNgayCoHieuLuc,
        must: this.must,
        should: this.should,
        not: this.not,
        ten_van_ban: this.ten_van_ban,
        so_hieu: this.so_hieu,
        threshold: this.threshold,
        sat_nhap_tinh: this.sat_nhap_tinh,
        loai_hinh_tai_lieu: Array.isArray(this.valueTheLoaiVanBan) ? this.valueTheLoaiVanBan.join(', ') : this.valueTheLoaiVanBan,
        boMoi: this.boMoi,
        linh_vuc: this.valueLinhVucVanBan,
        sort: this.filterBody.sort,
        order: this.filterBody.order,
      })
      .subscribe((res) => {
        this.currentPage = 1;
        this.table ? (this.table.offset = 0) : null;

        this.dataExport = res;
        this.dataTimKiemThongThuong = res.data;
        
        // Apply current filter if it's not "Tất cả"
        if (this.currentLabelFilter && this.currentLabelFilter !== "Tất cả") {
          this.dataTimKiemThongThuongFilter = res.data.filter(
            (item) => item.label === this.currentLabelFilter
          );
        } else {
          this.dataTimKiemThongThuongFilter = res.data;
        }
        
        this.termStates = new Array(
          this.dataTimKiemThongThuongFilter.length
        ).fill(false);
        this.totalItem = res.total;
        this.totalDieuKhoan = res.data ? res.data.length : 0;
        
        // Update filteredCount based on current filter
        if (this.currentLabelFilter && this.currentLabelFilter !== "Tất cả") {
          const selectedStat = this.statistic.find((item) => item.label === this.currentLabelFilter);
          this.filteredCount = selectedStat ? selectedStat.count : res.total;
        } else {
          this.filteredCount = res.total;
        }
        
        this.is_paging = res.is_paging;
        if (res.statistic) {
          const allStatistic = {
            label: "Tất cả",
            count: this.dataTimKiemThongThuong?.length || 0,
          };
          this.statistic = res.statistic.filter((item) => item.count > 0);
          if (allStatistic.count > 0) {
            this.statistic.unshift(allStatistic);
          }
        } else {
          this.statistic = [];
        }
      });
  }
  changePage(e) {
    const page = e.offset + 1;
    this.page = page;
    this._timKiemThongMinh
      .searchDieuKhoan({
        limit: this.limit,
        filter: this.filterString,
        search: this.searchString,
        listId: this.listId,
        valueCoQuanBanHanh: this.valueCoQuanBanHanh,
        valueCoQuanQuanly: this.valueCoQuanQuanLy,
        valueLoaiVanBan: this.valueLoaiVanBan,
        valueTrangThai: this.valueTrangThai,
        page: this.page,
        search_norm: this.search_legal_term === this.SLT.DieuKhoan,
        search_only_legal_title:
          this.search_legal_term === this.SLT.TieuDe ? true : null,
        ngay_ban_hanh: this.valueNgayBanHanh,
        ngay_co_hieu_luc: this.valueNgayCoHieuLuc,
        must: this.must,
        should: this.should,
        not: this.not,
        ten_van_ban: this.ten_van_ban,
        so_hieu: this.so_hieu,
        threshold: this.threshold,
        sat_nhap_tinh: this.sat_nhap_tinh,
        loai_hinh_tai_lieu: Array.isArray(this.valueTheLoaiVanBan) ? this.valueTheLoaiVanBan.join(', ') : this.valueTheLoaiVanBan,
        boMoi: this.boMoi,
        linh_vuc: this.valueLinhVucVanBan,
        sort: this.filterBody.sort,
        order: this.filterBody.order,
      })
      .subscribe((res) => {
        this.currentPage = 1;
        // this.table ? (this.table.offset = 0) : null;

        this.dataExport = res;
        this.dataTimKiemThongThuong = res.data;
        
        // Apply current filter if it's not "Tất cả"
        if (this.currentLabelFilter && this.currentLabelFilter !== "Tất cả") {
          this.dataTimKiemThongThuongFilter = res.data.filter(
            (item) => item.label === this.currentLabelFilter
          );
        } else {
          this.dataTimKiemThongThuongFilter = res.data;
        }
        
        this.termStates = new Array(
          this.dataTimKiemThongThuongFilter.length
        ).fill(false);
        this.totalItem = res.total;
        this.totalDieuKhoan = res.data ? res.data.length : 0;
        
        // Update filteredCount based on current filter
        if (this.currentLabelFilter && this.currentLabelFilter !== "Tất cả") {
          const selectedStat = this.statistic.find((item) => item.label === this.currentLabelFilter);
          this.filteredCount = selectedStat ? selectedStat.count : res.total;
        } else {
          this.filteredCount = res.total;
        }
        
        this.is_paging = res.is_paging;
        if (res.statistic) {
          const allStatistic = {
            label: "Tất cả",
            count: this.dataTimKiemThongThuong?.length || 0,
          };
          this.statistic = res.statistic.filter((item) => item.count > 0);
          if (allStatistic.count > 0) {
            this.statistic.unshift(allStatistic);
          }
        } else {
          this.statistic = [];
        }
      });
  }
  get paginatedData() {
    return this.paginationService.paginate(
      this.dataTimKiemThongThuongFilter,
      this.currentPage,
      this.itemsPerPage
    );
  }
  get totalPageThongThuong() {
    return this.paginationService.getTotalPages(
      this.dataTimKiemThongThuongFilter,
      this.itemsPerPage
    );
  }

  selectSort(sort: string) {
    this.selectedSort =
      sort === "loai_van_ban"
        ? "Hiệu lực pháp lý"
        : sort === "ngay_co_hieu_luc"
        ? "Ngày có hiệu lực"
        : sort === "score"
        ? "Tương đồng nội dung"
        : "Tình trạng hiệu lực";

    this.filterBody.sort = sort;
    this._timKiemThongMinh
      .searchDieuKhoan({
        limit: this.limit,
        filter: this.filterString,
        search: this.searchString,
        listId: this.listId,
        valueCoQuanBanHanh: this.valueCoQuanBanHanh,
        valueCoQuanQuanly: this.valueCoQuanQuanLy,
        valueLoaiVanBan: this.valueLoaiVanBan,
        valueTrangThai: this.valueTrangThai,
        page: this.page,
        search_norm: this.search_legal_term === this.SLT.DieuKhoan,
        search_only_legal_title:
          this.search_legal_term === this.SLT.TieuDe ? true : null,
        ngay_ban_hanh: this.valueNgayBanHanh,
        ngay_co_hieu_luc: this.valueNgayCoHieuLuc,
        must: this.must,
        should: this.should,
        not: this.not,
        ten_van_ban: this.ten_van_ban,
        so_hieu: this.so_hieu,
        threshold: this.threshold,
        sat_nhap_tinh: this.sat_nhap_tinh,
        loai_hinh_tai_lieu: Array.isArray(this.valueTheLoaiVanBan) ? this.valueTheLoaiVanBan.join(', ') : this.valueTheLoaiVanBan,
        boMoi: this.boMoi,
        linh_vuc: this.valueLinhVucVanBan,
        sort: this.filterBody.sort,
        order: this.filterBody.order,
      })
      .subscribe((res) => {
        this.currentPage = 1;
        this.table ? (this.table.offset = 0) : null;

        this.dataExport = res;
        this.dataTimKiemThongThuong = res.data;
        
        // Apply current filter if it's not "Tất cả"
        if (this.currentLabelFilter && this.currentLabelFilter !== "Tất cả") {
          this.dataTimKiemThongThuongFilter = res.data.filter(
            (item) => item.label === this.currentLabelFilter
          );
        } else {
          this.dataTimKiemThongThuongFilter = res.data;
        }
        
        this.termStates = new Array(
          this.dataTimKiemThongThuongFilter.length
        ).fill(false);
        this.totalItem = res.total;
        this.totalDieuKhoan = res.data ? res.data.length : 0;
        
        // Update filteredCount based on current filter
        if (this.currentLabelFilter && this.currentLabelFilter !== "Tất cả") {
          const selectedStat = this.statistic.find((item) => item.label === this.currentLabelFilter);
          this.filteredCount = selectedStat ? selectedStat.count : res.total;
        } else {
          this.filteredCount = res.total;
        }
      });
  }
  loaiSapXep() {
    if (this.typeSort == "arrow-down-circle") {
      this.typeSort = "arrow-up-circle";
      this.filterBody.order = "asc";
      this._timKiemThongMinh
        .searchDieuKhoan({
          limit: this.limit,
          filter: this.filterString,
          search: this.searchString,
          listId: this.listId,
          valueCoQuanBanHanh: this.valueCoQuanBanHanh,
          valueCoQuanQuanly: this.valueCoQuanQuanLy,
          valueLoaiVanBan: this.valueLoaiVanBan,
          valueTrangThai: this.valueTrangThai,
          page: this.page,
          search_norm: this.search_legal_term === this.SLT.DieuKhoan,
          search_only_legal_title:
            this.search_legal_term === this.SLT.TieuDe ? true : null,
          ngay_ban_hanh: this.valueNgayBanHanh,
          must: this.must,
          should: this.should,
          not: this.not,
          ten_van_ban: this.ten_van_ban,
          so_hieu: this.so_hieu,
          threshold: this.threshold,
          sat_nhap_tinh: this.sat_nhap_tinh,
          loai_hinh_tai_lieu: Array.isArray(this.valueTheLoaiVanBan) ? this.valueTheLoaiVanBan.join(', ') : this.valueTheLoaiVanBan,
          boMoi: this.boMoi,
          linh_vuc: this.valueLinhVucVanBan,
          sort: this.filterBody.sort,
          order: this.filterBody.order,
        })
        .subscribe((res) => {
          this.currentPage = 1;
          this.table ? (this.table.offset = 0) : null;

          this.dataExport = res;
          this.dataTimKiemThongThuong = res.data;
          
          // Apply current filter if it's not "Tất cả"
          if (this.currentLabelFilter && this.currentLabelFilter !== "Tất cả") {
            this.dataTimKiemThongThuongFilter = res.data.filter(
              (item) => item.label === this.currentLabelFilter
            );
          } else {
            this.dataTimKiemThongThuongFilter = res.data;
          }
          
          this.termStates = new Array(
            this.dataTimKiemThongThuongFilter.length
          ).fill(false);
          this.totalItem = res.total;
          this.totalDieuKhoan = res.data ? res.data.length : 0;
          
          // Update filteredCount based on current filter
          if (this.currentLabelFilter && this.currentLabelFilter !== "Tất cả") {
            const selectedStat = this.statistic.find((item) => item.label === this.currentLabelFilter);
            this.filteredCount = selectedStat ? selectedStat.count : res.total;
          } else {
            this.filteredCount = res.total;
          }
        });
    } else {
      this.typeSort = "arrow-down-circle";
      this.filterBody.order = "desc";
      this._timKiemThongMinh
        .searchDieuKhoan({
          limit: this.limit,
          filter: this.filterString,
          search: this.searchString,
          listId: this.listId,
          valueCoQuanBanHanh: this.valueCoQuanBanHanh,
          valueCoQuanQuanly: this.valueCoQuanQuanLy,
          valueLoaiVanBan: this.valueLoaiVanBan,
          valueTrangThai: this.valueTrangThai,
          page: this.page,
          search_norm: this.search_legal_term === this.SLT.DieuKhoan,
          search_only_legal_title:
            this.search_legal_term === this.SLT.TieuDe ? true : null,
          ngay_ban_hanh: this.valueNgayBanHanh,
          must: this.must,
          should: this.should,
          not: this.not,
          ten_van_ban: this.ten_van_ban,
          so_hieu: this.so_hieu,
          threshold: this.threshold,
          sat_nhap_tinh: this.sat_nhap_tinh,
          loai_hinh_tai_lieu: Array.isArray(this.valueTheLoaiVanBan) ? this.valueTheLoaiVanBan.join(', ') : this.valueTheLoaiVanBan,
          boMoi: this.boMoi,
          linh_vuc: this.valueLinhVucVanBan,
          sort: this.filterBody.sort,
          order: this.filterBody.order,
        })
        .subscribe((res) => {
          this.currentPage = 1;
          this.table ? (this.table.offset = 0) : null;
          this.dataExport = res;
          this.dataTimKiemThongThuong = res.data;
          
          // Apply current filter if it's not "Tất cả"
          if (this.currentLabelFilter && this.currentLabelFilter !== "Tất cả") {
            this.dataTimKiemThongThuongFilter = res.data.filter(
              (item) => item.label === this.currentLabelFilter
            );
          } else {
            this.dataTimKiemThongThuongFilter = res.data;
          }
          
          this.termStates = new Array(
            this.dataTimKiemThongThuongFilter.length
          ).fill(false);
          this.totalItem = res.total;
          this.totalDieuKhoan = res.data ? res.data.length : 0;
          
          // Update filteredCount based on current filter
          if (this.currentLabelFilter && this.currentLabelFilter !== "Tất cả") {
            const selectedStat = this.statistic.find((item) => item.label === this.currentLabelFilter);
            this.filteredCount = selectedStat ? selectedStat.count : res.total;
          } else {
            this.filteredCount = res.total;
          }
        });
    }
  }
  saveHistoryFiles(files?: any[]) {
    const workspaceId = this.route.snapshot.params.id;
    const sourceFiles = files ?? this.selectedFiles;
    if (!sourceFiles || sourceFiles.length === 0) {
      return;
    }
    const filesWithWorkspaceId = sourceFiles.map((file) => ({
      ...file,
      workspace_id: workspaceId,
      ...(typeof file.id === "string" ? { id: file.doc_id } : {}),
    }));

    console.log("filesWithWorkspaceId", filesWithWorkspaceId)

    this.quanlyvanban.addFileBySearch(filesWithWorkspaceId).subscribe(
      (response) => {
        const toastFn =
          response.status === "warning"
            ? this.toast.warning
            : this.toast.success;
        toastFn.call(this.toast, response.message, response.status_title, {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
          enableHtml: true,
        });
        this.workspace.isSaveFileFromSearch.next(true);
        // Only clear checkbox selections when saving from the selection bar (no explicit files passed)
        if (!files) {
          this.selectedFiles = [];
          this.cacheDataService.clearCheckbox();
          this.uncheckAll();
        }
      },
      (error) => {
        if (!files) {
          this.selectedFiles = [];
        }
        this.toast.error(error, "Thất bại", {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
        });
        // console.error("Lỗi khi lưu:", error);
      }
    );
  }

  filterResult(label) {
    this.currentPage = 1;
    if (this.table) this.table.offset = 0;
    this.selectedFiles = []; // Xóa danh sách các mục đã chọn
    this.currentLabelFilter = label;

    // Lọc dữ liệu theo label, nếu label không có giá trị thì hiển thị tất cả
    const filteredData = this.dataTimKiemThongThuong.filter(
      (item) => item.label === label
    );

    // Nếu không có dữ liệu hoặc label là null/undefined/"Tất cả", hiển thị tất cả
    this.dataTimKiemThongThuongFilter =
      filteredData.length > 0 ? filteredData : this.dataTimKiemThongThuong;

    // Cập nhật filteredCount dựa trên statistic count
    const selectedStat = this.statistic.find((item) => item.label === label);
    this.filteredCount = selectedStat ? selectedStat.count : this.totalItem;
  }
  onCheckboxChange(item: any, event: Event) {
    const isChecked = (event.target as HTMLInputElement).checked;

    if (isChecked) {
      this.selectedFiles.push(item); // Thêm item vào danh sách chọn
      this.cacheDataService.setCheckbox(this.selectedFiles);
    } else {
      this.selectedFiles = this.selectedFiles.filter((i) => i !== item); // Bỏ item khỏi danh sách chọn
    }
    // // console.log("Các mục được chọn:", this.selectedFiles);
  }
  uncheckAll() {
    this.selectedFiles = []; // Xóa danh sách các mục đã chọn
    const checkboxes = document.querySelectorAll(
      ".checkboxSelectedFile"
    ) as NodeListOf<HTMLInputElement>;
    checkboxes.forEach((checkbox) => (checkbox.checked = false)); // Bỏ chọn tất cả checkbox
  }
  changeLimitSize(event) {
    this.limit = event;
  }
  showTermClause(index) {
    this.termStates[index] = !this.termStates[index];
  }

  changeThreshHold(value: number) {
    this.threshold = value; // Gán vào biến nếu cần dùng
  }
  toggleNewDistrict(_event?: any) {
    this.sat_nhap_tinh = !this.sat_nhap_tinh;
    this.updateLists();
  }

  toggleBoMoi(_event?: any) {
    this.boMoi = !this.boMoi;
    this.updateLists();
  }
  // Hàm chung cập nhật dữ liệu theo tổ hợp 2 checkbox
  private updateLists() {
    // reset các giá trị đã chọn
    this.valueCoQuanBanHanh = [];
    this.valueCoQuanQuanLy = [];

    if (this.sat_nhap_tinh && this.boMoi) {
      // TinhMoi + BoMoi
      this.listCoQuanBanHanh = CoQuanBanHanhTinhMoiBoMoi;
      this.listCoQuanQuanLy = CoQuanQuanLyTinhMoiBoMoi;
    } else if (this.sat_nhap_tinh && !this.boMoi) {
      // TinhMoi + BoCu
      this.listCoQuanBanHanh = CoQuanBanHanhTinhMoiBoCu;
      this.listCoQuanQuanLy = CoQuanQuanLyTinhMoiBoCu;
    } else if (!this.sat_nhap_tinh && this.boMoi) {
      // TinhCu + BoMoi
      this.listCoQuanBanHanh = CoQuanBanHanhTinhCuBoMoi;
      this.listCoQuanQuanLy = CoQuanQuanLyTinhCuBoMoi;
    } else {
      // TinhCu + BoCu
      this.listCoQuanBanHanh = CoQuanBanHanhTinhCuBoCu;
      this.listCoQuanQuanLy = CoQuanQuanLyTinhCuBoCu;
    }
  }

  onCheckboxChangeLinhVuc(linhVuc: any) {
    this.totalValueThongKe = linhVuc.so_luong;
    this.page = 1;

    // Lấy danh sách lĩnh vực đã chọn theo trường linh_vuc nếu có, nếu không thì lấy theo key
    const checkedItems = this.listDanhSachLinhVuc.filter((i) => i.checked);
    let selectedLinhVuc = checkedItems.map((i) => i.linh_vuc);
    // const isAllChecked = checkedItems.some((i) => i.key === "Tất cả");
    this.valueLinhVucVanBan = selectedLinhVuc;

    if (linhVuc.checked || checkedItems.length > 0) {
      this.fetchSearchData();
    } else {
      this.valueLinhVucVanBan = [];

      if (this.valueLoaiVanBan.length > 0) {
        this.fetchSearchData();
      } else {
        this.resetData();
      }
    }
  }

  onCheckboxChangeLoaiVanBan(loaiVanBan: any) {
    this.totalValueThongKe = loaiVanBan.doc_count;
    this.page = 1;

    if (loaiVanBan.checked) {
      // CHECKED: Item được chọn
      if (loaiVanBan.key === "Tất cả") {
        // Nếu chọn "Tất cả", chọn toàn bộ mục
        this.listThongKeVanBan.forEach((item) => {
          item.checked = true;
        });
        this.valueLoaiVanBan = this.listThongKeVanBan
          .filter((item) => item.key !== "Tất cả")
          .map((item) => item.key);
      } else {
        // Nếu chọn mục khác, bỏ chọn "Tất cả"
        const allItem = this.listThongKeVanBan.find((i) => i.key === "Tất cả");
        if (allItem) allItem.checked = false;

        const selected = this.listThongKeVanBan
          .filter((item) => item.checked && item.key !== "Tất cả")
          .map((item) => item.key);
        this.valueLoaiVanBan = selected;
      }

      this.fetchSearchData();
    } else {
      // UNCHECKED: Item bị bỏ chọn
      if (loaiVanBan.key === "Tất cả") {
        // Nếu bỏ chọn "Tất cả", bỏ chọn toàn bộ mục
        this.listThongKeVanBan.forEach((item) => {
          item.checked = false;
        });
        this.valueLoaiVanBan = [];
      } else {
        // Nếu bỏ chọn mục khác, bỏ chọn "Tất cả" (nếu đã chọn)
        const allItem = this.listThongKeVanBan.find((i) => i.key === "Tất cả");
        if (allItem) allItem.checked = false;

        const selected = this.listThongKeVanBan
          .filter((item) => item.checked && item.key !== "Tất cả")
          .map((item) => item.key);
        this.valueLoaiVanBan = selected;
      }

      // Kiểm tra còn mục nào được chọn không
      if (this.valueLoaiVanBan.length > 0) {
        this.fetchSearchData();
      } else if (this.valueLinhVucVanBan.length > 0) {
        // Nếu không có loại văn bản nhưng có lĩnh vực, vẫn fetch
        this.fetchSearchData();
      } else {
        // Không có gì được chọn, reset data
        this.resetData();
      }
    }
  }

  
  clearAllRemainingItems(items: any[], clear: Function) {
    // Remove all items except the first 2
    const itemsToRemove = items.slice(2);
    itemsToRemove.forEach(item => {
      clear(item);
    });
  }

  private restoreOriginalSearch() {
    if (this.searchString && this.searchString.trim() !== '') {
      this._timKiemThongMinh
        .fastSearchDieuKhoan({
          limit: this.limit,
          filter: this.filterString,
          search: this.searchString,
          listId: this.listId,
          valueCoQuanBanHanh: this.valueCoQuanBanHanh,
          valueCoQuanQuanly: this.valueCoQuanQuanLy,
          valueLoaiVanBan: this.valueLoaiVanBan,
          valueTrangThai: this.valueTrangThai,
          page: this.page,
          search_norm: this.search_legal_term === this.SLT.DieuKhoan,
          search_only_legal_title: this.search_legal_term === this.SLT.TieuDe ? true : null,
          ngay_ban_hanh: this.valueNgayBanHanh,
          must: this.must,
          should: this.should,
          not: this.not,
          ten_van_ban: this.ten_van_ban,
          so_hieu: this.so_hieu,
          threshold: this.threshold,
          sat_nhap_tinh: this.sat_nhap_tinh,
          loai_hinh_tai_lieu: Array.isArray(this.valueTheLoaiVanBan) ? this.valueTheLoaiVanBan.join(', ') : this.valueTheLoaiVanBan,
          boMoi: this.boMoi,
          linh_vuc: [],
          sort: this.filterBody.sort,
          order: this.filterBody.order,
        })
        .subscribe((res) => {
          // Add noData handling
          if (res.total == 0) {
            this.noData = true;
          } else {
            this.noData = false;
          }

          this.dataTimKiemThongThuong = res.data;
          this.dataTimKiemThongThuongFilter = res.data;
          this.totalItem = res.total;
          this.totalDieuKhoan = res.totalDieuKhoan || 0;
          this.filteredCount = res.total;
          
          if (res.statistic) {
            const allStatistic = {
              label: "Tất cả",
              count: this.dataTimKiemThongThuong?.length || 0,
            };
            this.statistic = res.statistic.filter((item) => item.count > 0);
            if (allStatistic.count > 0) {
              this.statistic.unshift(allStatistic);
            }
            this.currentLabelFilter = "Tất cả";
          } else {
            this.statistic = [];
            this.currentLabelFilter = "Tất cả";
          }
        });
    } else {
      this.dataTimKiemThongThuong = [];
      this.dataTimKiemThongThuongFilter = [];
      this.totalItem = 0;
      this.totalDieuKhoan = 0;
      this.filteredCount = 0;
      this.noData = true;
    }
  }

  private resetData() {
    const checkedLinhVucItems = this.listDanhSachLinhVuc.filter(
      (i) => i.checked
    );
    const checkedLoaiVanBanItems = this.listThongKeVanBan.filter(
      (item) => item.checked
    );
    if (
      checkedLinhVucItems.length === 0 &&
      checkedLoaiVanBanItems.length == 0
    ) {
      this.restoreOriginalSearch()
    }
  }

  private fetchSearchData() {
    this.isSearching = true;
    this._timKiemThongMinh
      .fastSearchDieuKhoan({
        limit: this.limit,
        filter: this.filterString,
        search: this.searchString,
        listId: this.listId,
        valueCoQuanBanHanh: this.valueCoQuanBanHanh,
        valueCoQuanQuanly: this.valueCoQuanQuanLy,
        valueLoaiVanBan: this.valueLoaiVanBan,
        valueTrangThai: this.valueTrangThai,
        page: this.page,
        search_norm: this.search_legal_term === this.SLT.DieuKhoan,
        search_only_legal_title:
          this.search_legal_term === this.SLT.TieuDe ? true : null,
        ngay_ban_hanh: this.valueNgayBanHanh,
        ngay_co_hieu_luc: this.valueNgayCoHieuLuc,
        must: this.must,
        should: this.should,
        not: this.not,
        ten_van_ban: this.ten_van_ban,
        so_hieu: this.so_hieu,
        threshold: this.threshold,
        sat_nhap_tinh: this.sat_nhap_tinh,
        loai_hinh_tai_lieu: Array.isArray(this.valueTheLoaiVanBan) ? this.valueTheLoaiVanBan.join(', ') : this.valueTheLoaiVanBan,
        boMoi: this.boMoi,
        linh_vuc: this.valueLinhVucVanBan,
        sort: this.filterBody.sort,
        order: this.filterBody.order,
      })
      .pipe(finalize(() => { this.isSearching = false; }))
      .subscribe((res) => {
        this.currentPage = 1;
        if (this.table) this.table.offset = 0;

        // Add noData handling
        if (res.total == 0) {
          this.noData = true;
        } else {
          this.noData = false;
        }

        this.dataExport = res;
        this.dataTimKiemThongThuong = res.data;
        this.dataTimKiemThongThuongFilter = res.data;
        this.termStates = new Array(
          this.dataTimKiemThongThuongFilter.length
        ).fill(false);
        this.totalItem = res.total;
        this.totalDieuKhoan = res.data ? res.data.length : 0;
        this.filteredCount = res.total;
        this.is_paging = res.is_paging;

        if (res.statistic) {
          const allStatistic = {
            label: "Tất cả",
            count: this.dataTimKiemThongThuong?.length || 0,
          };
          this.statistic = res.statistic.filter((item) => item.count > 0);
          if (allStatistic.count > 0) {
            this.statistic.unshift(allStatistic);
          }
          // // console.log("listThongKeVanBan", this.listThongKeVanBan);
        } else {
          this.statistic = [];
        }
      });
  }

  changeTheLoaiVanBan(e) {
    this.valueTheLoaiVanBan = e.map((item) => {
      return item.value;
    });
  }
  clearAllFastSearch() {
    this.dataTimKiemThongThuongFilter = [];
    this.valueLinhVucVanBan = [];
    this.valueLoaiVanBan = [];
    this.page = 1;
    this.listDanhSachLinhVuc.forEach((item) => {
      item.checked = false;
    });
    this.listThongKeVanBan.forEach((item) => {
      item.checked = false;
    });

    this.restoreOriginalSearch();
  }
  ngOnDestroy() {
    this.workspace.isSaveFileFromSearch.next(false);
    this.unSubAll.next(null);
    this.unSubAll.complete();
  }
}
