import {
  Component,
  Input,
  OnInit,
  OnChanges,
  SimpleChanges,
  Output,
  EventEmitter,
  HostListener,
} from "@angular/core";
import { ActivatedRoute } from "@angular/router";
import { EChartsOption } from "echarts";
import { FlatpickrOptions } from "ng2-flatpickr";
import { ViewDetailFileService } from "../view-detail-file.service";
import { ToastrService } from "ngx-toastr";
import { DetailWorkSpaceService } from "app/main/apps/quan-ly-van-ban/detail-work-space/detail-work-space.service";
import {
  getVietnameseLabel,
  getVietnameseRelationshipLabel,
  mapStrengthToColor,
  getNodeIdFromApiNode,
  generateRelationshipKey,
  safeArray,
  formatDate,
} from "./helper/helper";
import {
  GraphApiResponse,
  ApiNode,
  ApiRelationship,
  GraphFormState,
  GraphRequestBody,
  DocumentData,
  ExpansionHistory,
  ContextMenuItem,
  NodeAttributes,
} from "./types/graph.types";
import {
  NODE_COLORS,
  NODE_SIZES,
  CHART_CONFIG,
  RELATIONSHIP_DIRECTIONS,
  RELATIONSHIP_TYPES,
  VALIDATION_LIMITS,
  TOAST_CONFIG,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  INFO_MESSAGES,
} from "./constants/graph.constants";

declare global {
  interface Window {
    echarts: any;
  }
}

export interface GraphNode {
  id: string;
  label: string;
  color?: string;
}

export interface GraphLink {
  id: string;
  source: string;
  target: string;
  label?: string;
  strength?: number;
  __curveness?: number;
  __isBaiBo?: boolean;
}

@Component({
  selector: "app-view-detail-file-graph",
  templateUrl: "./view-detail-file-graph.component.html",
  styleUrls: ["./view-detail-file-graph.component.scss"],
})
export class GraphComponent implements OnInit, OnChanges {
  @Input() graphData: GraphApiResponse | null = null;
  @Input() listDieuKhoan: any[] = [];
  @Input() currentDocumentEsId: string = "";
  @Input() formState: GraphFormState | null = null;
  @Input() isLoadingGraphExternal: boolean = false;
  @Output() documentClicked = new EventEmitter<DocumentData>();

  chartOption: EChartsOption = {};
  private chartInstance: any;
  nodes: GraphNode[] = [];
  links: GraphLink[] = [];
  private apiNodeMap: Map<string, ApiNode> = new Map();
  private rootNodeId: string = "";
  isLoadingGraph: boolean = false;
  showDocumentTable: boolean = true;
  contextMenuVisible: boolean = false;
  contextMenuPosition = { x: 0, y: 0 };
  contextMenuItem: ContextMenuItem | null = null;
  // Track which nodes and relationships came from each expansion
  private expansionHistory: Map<string, ExpansionHistory> = new Map();
  private hiddenNodeIds: Set<string> = new Set();
  
  // Cached computed maps for performance
  private cachedNeighborMap: Map<string, Set<string>> | null = null;
  private cachedAdjacencyMap: Map<string, Set<string>> | null = null;
  private cachedProtectedNodeIds: Set<string> | null = null;

  // Document list panel state
  documentListExpanded: boolean = false;
  selectAllDocuments: boolean = false;
  documentList: Array<{ id: string; title: string; selected: boolean; apiNode: ApiNode }> = [];
  selectedFiles: any[] = [];
  isFullTableView: boolean = false;
  fullTableSearch: string = "";
  page: number = 1;
  pageSize: number = 8;
  isSavingFiles: boolean = false;

  constructor(
    private viewDetailFile: ViewDetailFileService,
    private toast: ToastrService,
    private route: ActivatedRoute,
    private workspace: DetailWorkSpaceService
  ) {}

  dataFile: DocumentData = {} as DocumentData;
  private originalDocumentEsId: string = "";

  typeDocument = "csdl"; // could be 'csdl', 'search', 'upload', or 'searching'

  customYearOptions: FlatpickrOptions = {
    altFormat: "d/m/Y",
    dateFormat: "Y-m-d",
    enableTime: false,
    altInput: true,
    mode: "range",
    minDate: "1900-01-01",
    maxDate: "2100-12-31",
  };

  /* -------------------------------
   * Static Options
   * ----------------------------- */
  tinhTrangHieuLucOptions = [
    "Còn hiệu lực",
    "Hết hiệu lực toàn bộ",
    "Chưa có hiệu lực",
    "Bị đình chỉ thi hành",
  ].map((v) => ({ label: v, value: v }));

  boLocLoaiVanBanOptions = [
    "Hiến pháp",
    "Bộ luật",
    "Nghị quyết",
    "Quốc hội",
    "Lệnh",
    "Nghị định",
    "Thông tư",
    "Quyết định",
    "Luật",
    "Pháp lệnh",
  ].map((v) => ({ label: v, value: v }));

  coQuanBanHanhOptions = [
    "Quốc hội",
    "Uỷ ban thường vụ quốc hội",
    "Chính phủ",
    "Thủ tướng Chính Phủ",
    "Chủ Tịch Nước",
    "Bộ Trưởng",
    "Thứ Trưởng",
    "Hội đồng thẩm phán Toà án nhân dân tối cao",
    "Kiểm toán nhà nước",
    "Bộ Công An",
    "Bộ Quốc Phòng",
    "Bộ Lao Động - Thương Binh và Xã hội",
    "Bộ lao động",
    "Bộ Y Tế",
    "Bộ Tài Chính",
  ].map((v) => ({ label: v, value: v }));

  boLocMoiQuanHeOptions = [
    { label: "Bao gồm", value: "bao_gom" },
    { label: "Dẫn chiếu", value: "dan_chieu" },
    { label: "Bãi bỏ", value: "bai_bo" },
    { label: "Bổ sung", value: "bo_sung" },
    { label: "Đình chỉ", value: "dinh_chi" },
    { label: "Hướng dẫn", value: "huong_dan" },
    { label: "Quy định chi tiết", value: "quy_dinh_chi_tiet" },
    { label: "Sửa đổi", value: "sua_doi" },
    { label: "Sửa đổi bổ sung", value: "sua_doi_bo_sung" },
    { label: "Thay thế", value: "thay_the" },
    { label: "Căn cứ", value: "can_cu" },
  ];

  timKiemDieuKhoanOptions: any[] = [];

  ngOnInit(): void {
    // Store the original document ID before it gets overwritten
    this.originalDocumentEsId = this.currentDocumentEsId;
    
    // Initialize form state if not provided
    if (!this.formState) {
      this.formState = {
        viewGraphWithTerms: false,
        selectedTimKiemDieuKhoan: [],
        search_legal_term: "VAN_BAN",
        isSearchAdvance: false,
        selectedBoLocMoiQuanHe: [],
        selectedTinhTrangHieuLuc: [],
        selectedBoLocLoaiVanBan: [],
        selectedCoQuanBanHanh: [],
        depth: 1,
        global_limit: 1,
        limit_per_seed: 1,
        year_from: null,
        year_to: null,
        depthError: "",
        global_limitError: "",
        limit_per_seedError: ""
      };
    }

    this.updateTimKiemDieuKhoanOptions();
    
    // If graphData already present (e.g., pre-fetched), transform it
    if (this.graphData) {
      this.transformGraphData();
    }

    this.initializeChart();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes["listDieuKhoan"]?.currentValue) {
      this.updateTimKiemDieuKhoanOptions();
    }

    // Update originalDocumentEsId if currentDocumentEsId changes and we don't have one yet
    if (changes["currentDocumentEsId"]?.currentValue && !this.originalDocumentEsId) {
      this.originalDocumentEsId = this.currentDocumentEsId;
    }

    if (changes["graphData"]?.currentValue) {
      // // console.log("Received graphData in graph component:", this.graphData);
      // Don't overwrite currentDocumentEsId to preserve the original document ID
      // Persist graphData to service to maintain state across tab switches
      this.viewDetailFile.graphData.next(this.graphData);
      this.transformGraphData();
    }
  }

  /* -------------------------------
   * Form Logic
   * ----------------------------- */
  onViewGraphWithTermsChange(event: any): void {
    this.formState.viewGraphWithTerms = event.target.checked;
    if (!this.formState.viewGraphWithTerms) {
      this.formState.selectedTimKiemDieuKhoan = [];
    } else {
      // Set the first item as default when enabling the checkbox
      this.setDefaultClauseSelection();
    }
  }

  changeYear(event: any): void {
    const inputElement = event.target as HTMLInputElement;
    const selectedValue = inputElement.value;

    if (selectedValue) {
      let dates: string[] = [];

      // Check for Vietnamese separator first
      if (selectedValue.includes(" đến ")) {
        dates = selectedValue.split(" đến ");
      }
      // Check for English separator
      else if (selectedValue.includes(" to ")) {
        dates = selectedValue.split(" to ");
      }
      // Check for other possible separators
      else if (selectedValue.includes(" - ")) {
        dates = selectedValue.split(" - ");
      }

      if (dates.length === 2) {
        this.formState.year_from = new Date(dates[0]).getFullYear();
        this.formState.year_to = new Date(dates[1]).getFullYear();
      } else if (
        dates.length === 1 ||
        (!selectedValue.includes(" đến ") &&
          !selectedValue.includes(" to ") &&
          !selectedValue.includes(" - "))
      ) {
        // Single date
        const year = new Date(selectedValue).getFullYear();
        this.formState.year_from = this.formState.year_to = year;
      } else {
        this.formState.year_from = this.formState.year_to = null;
      }
    } else {
      this.formState.year_from = this.formState.year_to = null;
    }
  }

  validateNumberInput(event: any, min: number, max: number, propertyName: keyof GraphFormState, fieldName: string) {
    if (!this.formState) return;
    
    const value = Number(event.target.value);
    const errorProperty = `${propertyName}Error` as keyof GraphFormState;
    
    // Clear previous error
    (this.formState as any)[errorProperty] = '';
    
    if (value < min) {
      (this.formState as any)[errorProperty] = `${fieldName} tối thiểu là ${min}`;
    } else if (value > max) {
      (this.formState as any)[errorProperty] = `${fieldName} tối đa là ${max}`;
    } else {
      (this.formState as any)[propertyName] = value;
    }
  }

  // Wrapper methods for each field
  validateDepth(event: any) {
    this.validateNumberInput(event, VALIDATION_LIMITS.DEPTH.MIN, VALIDATION_LIMITS.DEPTH.MAX, "depth", "Số cấp liên kết");
  }

  validateGlobalLimit(event: any) {
    this.validateNumberInput(
      event,
      VALIDATION_LIMITS.GLOBAL_LIMIT.MIN,
      VALIDATION_LIMITS.GLOBAL_LIMIT.MAX,
      "global_limit",
      "Giới hạn số lượng tài liệu"
    );
  }

  validateLimitPerSeed(event: any) {
    this.validateNumberInput(
      event,
      VALIDATION_LIMITS.LIMIT_PER_SEED.MIN,
      VALIDATION_LIMITS.LIMIT_PER_SEED.MAX,
      "limit_per_seed",
      "Giới hạn số lượng tài liệu cho mỗi nhánh"
    );
  }

  onSubmitForm(): void {
    if (!this.formState) return;
    
    // Always use the original document ID for the API endpoint
    const targetId = this.originalDocumentEsId || this.currentDocumentEsId;

    // Validate that we have a document ID
    if (!targetId) {
      this.showErrorToast(ERROR_MESSAGES.NO_DOCUMENT_ID);
      return;
    }

    // Build node_ids array
    const nodeIds: string[] = [targetId];
    
    // Add selected clause IDs if "Xem đồ thị với điều khoản" is enabled
    if (this.formState.viewGraphWithTerms && this.formState.selectedTimKiemDieuKhoan.length > 0) {
      nodeIds.push(...this.formState.selectedTimKiemDieuKhoan);
    }

    // Build the request body
    const body: GraphRequestBody = {
      co_quan_ban_hanh: safeArray(this.formState.selectedCoQuanBanHanh),
      depth: this.formState.depth || 1,
      global_limit: this.formState.global_limit || 1,
      limit_per_seed: this.formState.limit_per_seed || 1,
      loai_van_ban: safeArray(this.formState.selectedBoLocLoaiVanBan),
      node_ids: nodeIds,
      relationship_types: safeArray(this.formState.selectedBoLocMoiQuanHe),
      target_node_type: this.formState.search_legal_term || "ALL",
      tinh_trang_hieu_luc: safeArray(this.formState.selectedTinhTrangHieuLuc),
      year_from: this.formState.year_from || null,
      year_to: this.formState.year_to || null,
    };

    // Make the API call
    this.isLoadingGraph = true;
    this.viewDetailFile.getDoThi(targetId, body).subscribe(
      (res) => {
        this.graphData = res;
        this.resetGraphState();
        this.viewDetailFile.graphData.next(this.graphData);
        this.transformGraphData();
        this.showSuccessToast(SUCCESS_MESSAGES.GRAPH_UPDATED);
        this.isLoadingGraph = false;
      },
      (error) => {
        this.handleApiError(error);
        this.isLoadingGraph = false;
      }
    );
  }

  /* -------------------------------
   * Chart Initialization
   * ----------------------------- */
  private initializeChart(): void {
    const echartsNodes = this.nodes.map((node) => {
      const symbolSize = node.id === this.rootNodeId ? NODE_SIZES.ROOT : NODE_SIZES.REGULAR;
      
      return {
        id: node.id,
        name: node.label,
        symbolSize: symbolSize,
        itemStyle: { color: node.color || NODE_COLORS.DEFAULT },
        label: {
          show: symbolSize > NODE_SIZES.MIN_LABEL_SIZE,
          position: "right" as const,
          formatter: "{b}",
        },
      };
    });

    const echartsLinks = this.links.map((link) => ({
      source: link.source,
      target: link.target,
      label: {
        show: !!link.label,
        formatter: link.label,
        fontSize: 10,
        color: "#333",
      },
      lineStyle: {
        color: link.__isBaiBo ? NODE_COLORS.ARTICLE : mapStrengthToColor(link.strength),
        width: CHART_CONFIG.LINE_STYLE.WIDTH,
        type: link.__isBaiBo ? ("dashed" as const) : ("solid" as const),
        curveness: link.__curveness ?? 0,
      },
    }));

    this.chartOption = {
      tooltip: {
        formatter: (params: any) =>
          params.dataType === "node"
            ? params.data.name
            : params.data.label?.formatter || "",
      },
      series: [
        {
          type: "graph",
          layout: "force",
          data: echartsNodes,
          links: echartsLinks,
          roam: true,
          label: {
            position: "right" as const,
            formatter: "{b}",
          },
          force: {
            repulsion: CHART_CONFIG.FORCE.REPULSION,
            gravity: CHART_CONFIG.FORCE.GRAVITY,
            edgeLength: CHART_CONFIG.FORCE.EDGE_LENGTH,
            layoutAnimation: true,
          },
          lineStyle: {
            color: "source",
            curveness: 0,
          },
          emphasis: {
            focus: "adjacency",
            lineStyle: {
              width: CHART_CONFIG.LINE_STYLE.WIDTH_EMPHASIS,
            },
          },
          edgeSymbol: [...CHART_CONFIG.EDGE_SYMBOL.TYPES],
          edgeSymbolSize: [...CHART_CONFIG.EDGE_SYMBOL.SIZE],
        },
      ],
    };
  }

  onChartInit(chart: any): void {
    this.chartInstance = chart;
    chart.on("click", (params: any) => {
      if (params.dataType === "node") {
        const apiNode = this.findApiNodeById(params.data.id);
        if (apiNode) {
          this.emitDocumentData(apiNode);
        }
      }
    });

    chart.on("contextmenu", (params: any) => {
      if (params.dataType === "node") {
        params.event.event.preventDefault();
        const apiNode = this.findApiNodeById(params.data.id);
        if (apiNode) {
          this.onRightClickNode(params.event.event, apiNode, params.data.id);
        }
      }
    });
  }

  onRightClickNode(event: MouseEvent, apiNode: ApiNode, nodeId: string): void {
    event.preventDefault();
    const hiddenDescendants = this.getHiddenDescendantsForNode(nodeId);
    const canRestore = hiddenDescendants.size > 0;
    this.contextMenuVisible = true;
    this.contextMenuPosition = { x: event.clientX, y: event.clientY };
    this.contextMenuItem = { apiNode, nodeId, hiddenDescendants, canRestore };
  }

  closeContextMenu(): void {
    this.contextMenuVisible = false;
    this.contextMenuItem = null;
  }

  toggleDocumentList(): void {
    this.documentListExpanded = !this.documentListExpanded;
  }

  @HostListener("document:click")
  onDocumentClick(): void {
    this.closeContextMenu();
  }

  onExpandNode(): void {
    if (!this.contextMenuItem || !this.formState) return;
    
    const contextItem = this.contextMenuItem;
    this.closeContextMenu();

    const clickedNodeId = contextItem.nodeId;
    const clickedApiNode = contextItem.apiNode;
    
    if (!clickedNodeId || !clickedApiNode) {
      this.showErrorToast(ERROR_MESSAGES.NO_NODE_INFO);
      return;
    }

    // Get the API node ID (the ID that the backend expects)
    const apiNodeId = getNodeIdFromApiNode(clickedApiNode);
    if (!apiNodeId) {
      this.showErrorToast(ERROR_MESSAGES.NO_NODE_ID);
      return;
    }

    // Build request body
    const body: GraphRequestBody = {
      co_quan_ban_hanh: safeArray(this.formState.selectedCoQuanBanHanh),
      depth: this.formState.depth,
      global_limit: this.formState.global_limit,
      limit_per_seed: this.formState.limit_per_seed,
      loai_van_ban: safeArray(this.formState.selectedBoLocLoaiVanBan),
      node_ids: [String(apiNodeId)],
      relationship_types: safeArray(this.formState.selectedBoLocMoiQuanHe),
      target_node_type: this.formState.search_legal_term,
      tinh_trang_hieu_luc: safeArray(this.formState.selectedTinhTrangHieuLuc),
      year_from: this.formState.year_from || null,
      year_to: this.formState.year_to || null,
    };

    // If this node was previously expanded, remove its previous expansion results first
    if (this.expansionHistory.has(apiNodeId)) {
      this.removePreviousExpansion(apiNodeId);
    }

    // Make the API call
    this.isLoadingGraph = true;
    this.viewDetailFile.getDoThi(apiNodeId, body).subscribe(
      (res) => {
        if (this.graphData && res) {
          this.mergeGraphData(res, apiNodeId);
        } else {
          this.graphData = res;
        }
        
        this.viewDetailFile.graphData.next(this.graphData);
        this.transformGraphData();
        this.showSuccessToast(SUCCESS_MESSAGES.EXPANSION_SUCCESS);
        this.isLoadingGraph = false;
      },
      (error) => {
        this.showErrorToast(ERROR_MESSAGES.EXPANSION_FAILED);
        this.isLoadingGraph = false;
      }
    );
  }

  private mergeGraphData(newData: GraphApiResponse, seedNodeId: string): void {
    if (!this.graphData || !newData) return;

    // Merge nodes - deduplicate by ID
    const existingNodeIds = new Set(
      safeArray(this.graphData.nodes).map((n: ApiNode) => getNodeIdFromApiNode(n))
    );
    
    const newNodes = safeArray(newData.nodes).filter((n: ApiNode) => {
      const nodeId = getNodeIdFromApiNode(n);
      return nodeId && !existingNodeIds.has(nodeId);
    });

    this.graphData.nodes = [...safeArray(this.graphData.nodes), ...newNodes];

    // Merge relationships - deduplicate using relationship key
    const existingRelKeys = new Set(
      safeArray(this.graphData.relationships).map((r: ApiRelationship) => generateRelationshipKey(r))
    );

    const newRelationships = safeArray(newData.relationships).filter((r: ApiRelationship) => {
      const relKey = generateRelationshipKey(r);
      return !existingRelKeys.has(relKey);
    });

    this.graphData.relationships = [...safeArray(this.graphData.relationships), ...newRelationships];

    // Update metadata if needed
    if (newData.seed_node_ids) {
      const existingSeedIds = new Set(safeArray(this.graphData.seed_node_ids).map(id => String(id)));
      const newSeedIds = safeArray(newData.seed_node_ids).filter((id: string | number) => !existingSeedIds.has(String(id)));
      this.graphData.seed_node_ids = [...safeArray(this.graphData.seed_node_ids), ...newSeedIds];
    }

    // Update totals based on actual merged data
    this.graphData.total_nodes = safeArray(this.graphData.nodes).length;
    this.graphData.total_relationships = safeArray(this.graphData.relationships).length;

    // Invalidate caches
    this.invalidateCaches();

    // Track this expansion - only track nodes and relationships that were actually added
    this.trackExpansion(seedNodeId, newNodes, newRelationships);
  }

  /**
   * Track which nodes and relationships came from this expansion
   * Only tracks nodes and relationships that were actually added (new)
   */
  private trackExpansion(seedNodeId: string, newNodes: ApiNode[], newRelationships: ApiRelationship[]): void {
    const nodeIds = new Set<string>();
    const relationshipKeys = new Set<string>();

    // Track all new nodes from this expansion (except the seed node itself)
    newNodes.forEach((node: ApiNode) => {
      const nodeId = getNodeIdFromApiNode(node);
      if (nodeId && nodeId !== seedNodeId) {
        nodeIds.add(nodeId);
      }
    });

    // Track all new relationships from this expansion
    newRelationships.forEach((rel: ApiRelationship) => {
      relationshipKeys.add(generateRelationshipKey(rel));
    });

    // Store in expansion history
    this.expansionHistory.set(seedNodeId, { nodeIds, relationshipKeys });
  }

  /**
   * Remove nodes and relationships from a previous expansion of a node
   */
  private removePreviousExpansion(seedNodeId: string): void {
    if (!this.graphData) return;

    const previousExpansion = this.expansionHistory.get(seedNodeId);
    if (!previousExpansion) return;

    // Get initial seed node IDs (root and original seeds) - these should never be removed
    const initialSeedIds = this.getProtectedNodeIds();

    // Find nodes that should be removed
    const nodesToRemove = new Set<string>();
    previousExpansion.nodeIds.forEach((nodeId: string) => {
      if (!initialSeedIds.has(nodeId)) {
        const isOnlyConnectedViaThisExpansion = this.isNodeOnlyConnectedViaExpansion(
          nodeId,
          seedNodeId,
          previousExpansion.relationshipKeys
        );
        if (isOnlyConnectedViaThisExpansion) {
          nodesToRemove.add(nodeId);
        }
      }
    });

    // Remove nodes
    if (nodesToRemove.size > 0) {
      this.graphData.nodes = safeArray(this.graphData.nodes).filter((node: ApiNode) => {
        const nodeId = getNodeIdFromApiNode(node);
        return !nodesToRemove.has(nodeId);
      });
      nodesToRemove.forEach((nodeId) => this.hiddenNodeIds.delete(nodeId));
    }

    // Remove relationships that were part of this expansion
    if (previousExpansion.relationshipKeys.size > 0) {
      this.graphData.relationships = safeArray(this.graphData.relationships).filter((rel: ApiRelationship) => {
        return !previousExpansion.relationshipKeys.has(generateRelationshipKey(rel));
      });
    }

    // Also remove relationships that involve removed nodes
    if (nodesToRemove.size > 0) {
      this.graphData.relationships = safeArray(this.graphData.relationships).filter((rel: ApiRelationship) => {
        const sourceId = String(rel.source_id);
        const targetId = String(rel.target_id);
        return !nodesToRemove.has(sourceId) && !nodesToRemove.has(targetId);
      });
    }

    // Update totals
    this.graphData.total_nodes = safeArray(this.graphData.nodes).length;
    this.graphData.total_relationships = safeArray(this.graphData.relationships).length;

    // Invalidate caches
    this.invalidateCaches();

    // Clear the expansion history entry (will be updated with new expansion)
    this.expansionHistory.delete(seedNodeId);
  }

  /**
   * Check if a node is only connected via relationships from a specific expansion
   * This helps determine if it's safe to remove the node
   */
  private isNodeOnlyConnectedViaExpansion(
    nodeId: string,
    seedNodeId: string,
    expansionRelationshipKeys: Set<string>
  ): boolean {
    if (!this.graphData) return true;

    const allRelationships = safeArray(this.graphData.relationships);
    
    // Find all relationships involving this node
    const nodeRelationships = allRelationships.filter((rel: ApiRelationship) => {
      const sourceId = String(rel.source_id);
      const targetId = String(rel.target_id);
      return sourceId === nodeId || targetId === nodeId;
    });

    // Check if all relationships involving this node are from this expansion
    for (const rel of nodeRelationships) {
      if (!expansionRelationshipKeys.has(generateRelationshipKey(rel))) {
        // This node has a relationship outside of this expansion, don't remove it
        return false;
      }
    }

    return true;
  }

  private getProtectedNodeIds(): Set<string> {
    if (this.cachedProtectedNodeIds) {
      return this.cachedProtectedNodeIds;
    }

    const protectedIds = new Set<string>();
    if (this.rootNodeId) {
      protectedIds.add(this.rootNodeId);
    }
    const seedIds = this.graphData?.seed_node_ids;
    if (Array.isArray(seedIds)) {
      seedIds.forEach((id: string | number) => {
        if (id != null) {
          protectedIds.add(String(id));
        }
      });
    }
    
    this.cachedProtectedNodeIds = protectedIds;
    return protectedIds;
  }

  private buildAdjacencyMap(): Map<string, Set<string>> {
    if (this.cachedAdjacencyMap) {
      return this.cachedAdjacencyMap;
    }

    const adjacency = new Map<string, Set<string>>();
    if (!this.graphData) {
      return adjacency;
    }

    const relationships = safeArray(this.graphData.relationships);
    for (const rel of relationships) {
      const rawSourceId = String(rel.source_id);
      const rawTargetId = String(rel.target_id);
      const directionIsOutgoing = rel.huong === RELATIONSHIP_DIRECTIONS.OUTGOING;
      const sourceId = directionIsOutgoing ? rawSourceId : rawTargetId;
      const targetId = directionIsOutgoing ? rawTargetId : rawSourceId;

      if (!adjacency.has(sourceId)) {
        adjacency.set(sourceId, new Set());
      }
      adjacency.get(sourceId)!.add(targetId);
    }

    this.cachedAdjacencyMap = adjacency;
    return adjacency;
  }

  private buildUndirectedNeighborMap(): Map<string, Set<string>> {
    if (this.cachedNeighborMap) {
      return this.cachedNeighborMap;
    }

    const neighbors = new Map<string, Set<string>>();
    if (!this.graphData) {
      return neighbors;
    }

    const relationships = safeArray(this.graphData.relationships);
    for (const rel of relationships) {
      const a = String(rel.source_id);
      const b = String(rel.target_id);

      if (!neighbors.has(a)) neighbors.set(a, new Set());
      if (!neighbors.has(b)) neighbors.set(b, new Set());

      neighbors.get(a)!.add(b);
      neighbors.get(b)!.add(a);
    }

    this.cachedNeighborMap = neighbors;
    return neighbors;
  }

  private getDescendantNodeIds(nodeId: string): Set<string> {
    const neighbors = this.buildUndirectedNeighborMap();
    const protectedNodes = this.getProtectedNodeIds();
    const visited = new Set<string>();
    const stack: string[] = [];

    const direct = neighbors.get(nodeId);
    if (direct) {
      direct.forEach((n) => stack.push(n));
    }

    while (stack.length > 0) {
      const current = stack.pop()!;
      if (visited.has(current)) {
        continue;
      }

      if (protectedNodes.has(current)) {
        // Do not hide or traverse beyond protected nodes (root, seed nodes)
        continue;
      }

      visited.add(current);
      const next = neighbors.get(current);
      if (next) {
        next.forEach((n) => {
          if (!visited.has(n)) {
            stack.push(n);
          }
        });
      }
    }

    return visited;
  }

  private getHiddenDescendantsForNode(nodeId: string): Set<string> {
    const hiddenNodes = new Set<string>();
    if (this.hiddenNodeIds.size === 0) {
      return hiddenNodes;
    }

    if (nodeId === this.rootNodeId) {
      this.hiddenNodeIds.forEach((id) => hiddenNodes.add(id));
      return hiddenNodes;
    }

    const neighbors = this.buildUndirectedNeighborMap();
    const visited = new Set<string>();
    const stack: string[] = [];
    const direct = neighbors.get(nodeId);
    if (direct) {
      direct.forEach((n) => stack.push(n));
    }

    while (stack.length > 0) {
      const current = stack.pop()!;
      if (visited.has(current)) {
        continue;
      }
      visited.add(current);

      if (this.hiddenNodeIds.has(current)) {
        hiddenNodes.add(current);
      }

      const next = neighbors.get(current);
      if (next) {
        next.forEach((n) => {
          if (!visited.has(n)) {
            stack.push(n);
          }
        });
      }
    }

    return hiddenNodes;
  }

  onCollapseNode(): void {
    if (!this.contextMenuItem) return;
    
    const contextItem = this.contextMenuItem;
    this.closeContextMenu();

    const nodeId = contextItem.nodeId;

    if (!nodeId) {
      this.showErrorToast(ERROR_MESSAGES.NO_NODE_INFO);
      return;
    }

    if (nodeId === this.rootNodeId) {
      this.showWarningToast(ERROR_MESSAGES.CANNOT_HIDE_ROOT);
      return;
    }

    const descendants = this.getDescendantNodeIds(nodeId);
    const nodesToHide = new Set<string>(descendants);
    nodesToHide.add(nodeId);

    let hiddenCount = 0;
    nodesToHide.forEach((id) => {
      if (id === this.rootNodeId) {
        return;
      }
      if (!this.hiddenNodeIds.has(id)) {
        this.hiddenNodeIds.add(id);
        hiddenCount++;
      }
    });

    if (hiddenCount === 0) {
      this.showInfoToast(INFO_MESSAGES.NO_NEW_NODES_HIDDEN);
      return;
    }

    this.transformGraphData();

    this.showSuccessToast(SUCCESS_MESSAGES.NODES_HIDDEN);
  }

  onRestoreNode(): void {
    if (!this.contextMenuItem) return;
    
    const contextItem = this.contextMenuItem;
    this.closeContextMenu();

    const hiddenDescendants: Set<string> = contextItem.hiddenDescendants || new Set();
    if (hiddenDescendants.size === 0) {
      this.showInfoToast(ERROR_MESSAGES.NO_NODES_TO_RESTORE);
      return;
    }

    hiddenDescendants.forEach((id) => this.hiddenNodeIds.delete(id));
    this.transformGraphData();

    this.showSuccessToast(SUCCESS_MESSAGES.NODES_RESTORED);
  }

  private findApiNodeById(nodeId: string): ApiNode | null {
    if (!nodeId) return null;
    if (this.apiNodeMap.has(nodeId)) return this.apiNodeMap.get(nodeId)!;

    // Fallback: search in current graphData.nodes if present
    const nodes = safeArray(this.graphData?.nodes);
    for (const n of nodes) {
      const idCandidate = getNodeIdFromApiNode(n);
      if (idCandidate === nodeId) return n;
    }
    return null;
  }

  private setDefaultClauseSelection(): void {
    if (this.formState?.viewGraphWithTerms && 
        this.timKiemDieuKhoanOptions.length > 0 && 
        (!Array.isArray(this.formState.selectedTimKiemDieuKhoan) || this.formState.selectedTimKiemDieuKhoan.length === 0)) {
      this.formState.selectedTimKiemDieuKhoan = [this.timKiemDieuKhoanOptions[0].value];
    }
  }

  getSelectedClauseLabel(): string {
    if (!this.formState?.selectedTimKiemDieuKhoan || this.formState.selectedTimKiemDieuKhoan.length === 0) {
      return '';
    }
    
    // Get labels for all selected clauses
    const selectedLabels = this.formState.selectedTimKiemDieuKhoan
      .map(value => {
        const option = this.timKiemDieuKhoanOptions.find(opt => opt.value === value);
        return option ? option.label : '';
      })
      .filter(label => label !== '');
    
    return selectedLabels.join(', ');
  }


  private updateTimKiemDieuKhoanOptions(): void {
    this.timKiemDieuKhoanOptions =
      this.listDieuKhoan?.map((d) => {
        // For external search, use currentDocumentEsId instead of d.doc_id when d.doc_id is null
        const docId = d.doc_id || this.currentDocumentEsId;
        return {
          label: d.position,
          value: d.clause_id + "#" + docId,
        };
      }) || [];

    // Set default selection if checkbox is enabled and options are available
    this.setDefaultClauseSelection();
  }


  private transformGraphData(): void {
    if (!this.graphData) return;

    const transformedNodes: GraphNode[] = [];
    const transformedLinks: GraphLink[] = [];
    const nodeMap = new Map<string, GraphNode>();
    const linkIdCounter = { value: 0 };
    this.apiNodeMap.clear();
    this.rootNodeId = "";

    // Invalidate caches when transforming
    this.invalidateCaches();

    // API format: graphData.nodes (first element is root) and relationships array
    const apiNodes = safeArray(this.graphData.nodes);
    if (apiNodes.length > 0) {

      // Build nodes and apiNodeMap
      apiNodes.forEach((apiNode: ApiNode, index: number) => {
        const id = getNodeIdFromApiNode(apiNode);
        if (!id) return;
        // Keep original api node keyed by our internal id
        this.apiNodeMap.set(id, { ...apiNode });

        const isHidden = this.hiddenNodeIds.has(id);
        const node = this.createNodeFromApiNode(apiNode, index === 0);
        if (!isHidden && !nodeMap.has(node.id)) {
          transformedNodes.push(node);
          nodeMap.set(node.id, node);
        }

        if (index === 0) {
          this.rootNodeId = node.id;
          if (!isHidden) {
            this.updateDataFileFromRootNode(apiNode);
          }
        }
      });

      // Build links from relationships array
      const relationships = safeArray(this.graphData.relationships);
      if (relationships.length > 0) {
        const parallelEdgeCount = new Map<string, number>();
        for (const rel of relationships) {
          const rawSourceId = String(rel.source_id);
          const rawTargetId = String(rel.target_id);
          const directionIsOutgoing = rel.huong === RELATIONSHIP_DIRECTIONS.OUTGOING;
          const sourceId = directionIsOutgoing ? rawSourceId : rawTargetId;
          const targetId = directionIsOutgoing ? rawTargetId : rawSourceId;

          if (this.hiddenNodeIds.has(sourceId) || this.hiddenNodeIds.has(targetId)) {
            continue;
          }

          // Ensure nodes exist in map (relationships may reference nodes not present in nodes list)
          this.ensureNodeExists(sourceId, nodeMap, transformedNodes);
          this.ensureNodeExists(targetId, nodeMap, transformedNodes);

          if (nodeMap.has(sourceId) && nodeMap.has(targetId)) {
            const key = `${sourceId}_${targetId}`;
            const idx = (parallelEdgeCount.get(key) || 0) + 1;
            parallelEdgeCount.set(key, idx);
            
            // Assign curveness to separate parallel edges: 1st: 0, then ±0.2, ±0.35, ...
            const curveness = this.calculateCurveness(idx);

            transformedLinks.push({
              id: `link_${linkIdCounter.value++}`,
              source: sourceId,
              target: targetId,
              label: getVietnameseRelationshipLabel(rel.loai_moi_quan_he || ''),
              strength: typeof rel.strength === 'number' ? rel.strength : undefined,
              __curveness: curveness,
              __isBaiBo: rel.loai_moi_quan_he === RELATIONSHIP_TYPES.BAI_BO,
            });
          }
        }
      }
    }

    this.nodes = transformedNodes;
    // Map links to ECharts format, carrying per-edge curveness and style
    this.links = transformedLinks;
    this.initializeChart();
    
    // Update document list whenever graph data is transformed
    this.updateDocumentList();
  }

  private createNodeFromApiNode(
    apiNode: ApiNode,
    isRoot: boolean = false
  ): GraphNode {
    const nodeId = getNodeIdFromApiNode(apiNode);
    let label = "";
    let color = NODE_COLORS.DEFAULT;

    // Concise label: "<nhan_ui> - <id_ui>" with fallbacks
    const typeUi = apiNode.nhan_ui || getVietnameseLabel(apiNode.nhan || "");
    const codeUi = apiNode.id_ui || apiNode.thuoc_tinh?.so_hieu || apiNode.thuoc_tinh?.vi_tri || nodeId;
    label = `${typeUi} - ${codeUi}`.trim();

    // Prefer API-provided color when available (many-seed responses)
    if (apiNode.metadata?.color) {
      color = apiNode.metadata.color;
    } else {
      // Fallback color based on node type
      if (apiNode.nhan_ui === "Văn bản") {
        color = NODE_COLORS.DOCUMENT;
      } else if (apiNode.nhan_ui === "Điều khoản") {
        color = NODE_COLORS.ARTICLE;
      }
    }

    return {
      id: nodeId,
      label: label,
      color: color,
    };
  }

  private updateDataFileFromRootNode(rootNode: ApiNode): void {
    if (!rootNode?.thuoc_tinh) return;

    const thuocTinh = rootNode.thuoc_tinh;

    this.dataFile = {
      ten_day_du: thuocTinh.ten_day_du || rootNode.ten_day_du || "",
      so_hieu: thuocTinh.so_hieu || "",
      ngay_ban_hanh: formatDate(thuocTinh.ngay_ban_hanh || ""),
      loai_van_ban: thuocTinh.loai_van_ban || "",
      ngay_co_hieu_luc: formatDate(thuocTinh.ngay_co_hieu_luc || ""),
      ngay_dang_cong_bao: formatDate(thuocTinh.ngay_dang_cong_bao || ""),
      co_quan_ban_hanh: thuocTinh.co_quan_ban_hanh || "",
      chuc_danh: thuocTinh.chuc_danh || "",
      nguoi_ky: thuocTinh.nguoi_ky || "",
      pham_vi: thuocTinh.pham_vi || "",
      trich_yeu: thuocTinh.trich_yeu || "",
      tinh_trang_hieu_luc: thuocTinh.tinh_trang_hieu_luc || "",
      thoi_gian_cap_nhat: thuocTinh.thoi_gian_cap_nhat || "",
    };
    this.showDocumentTable = true;
  }

  private emitDocumentData(apiNode: ApiNode): void {
    // Handle both cases: thuoc_tinh object and direct properties
    const nodeData: NodeAttributes = apiNode.thuoc_tinh || (apiNode as any);
    
    if (!nodeData) return;

    const documentData: DocumentData = {
      ten_day_du: nodeData.ten_day_du || apiNode.ten_day_du || "",
      so_hieu: nodeData.so_hieu || "",
      ngay_ban_hanh: formatDate(nodeData.ngay_ban_hanh || ""),
      loai_van_ban: nodeData.loai_van_ban || "",
      ngay_co_hieu_luc: formatDate(nodeData.ngay_co_hieu_luc || ""),
      ngay_dang_cong_bao: formatDate(nodeData.ngay_dang_cong_bao || ""),
      co_quan_ban_hanh: nodeData.co_quan_ban_hanh || "",
      chuc_danh: nodeData.chuc_danh || "",
      nguoi_ky: nodeData.nguoi_ky || "",
      pham_vi: nodeData.pham_vi || "",
      trich_yeu: nodeData.trich_yeu || "",
      tinh_trang_hieu_luc: nodeData.tinh_trang_hieu_luc || "",
      thoi_gian_cap_nhat: nodeData.thoi_gian_cap_nhat || "",
    };

    // Update local dataFile and emit to parent
    this.dataFile = documentData;
    this.documentClicked.emit(documentData);
    this.showDocumentTable = true;
  }

  onCloseDocumentTable(): void {
    this.showDocumentTable = false;
  }

  // Toggle between graph view and full table view
  toggleFullDocumentTable(): void {
    this.isFullTableView = !this.isFullTableView;
  }

  /**
   * Returns documentList filtered by fullTableSearch (matches title, loại văn bản, số hiệu)
   */
  getFilteredDocuments() {
    const query = (this.fullTableSearch || "").toLowerCase().trim();
    if (!query) return this.documentList;
    return this.documentList.filter((item) => {
      const attrs = item.apiNode?.thuoc_tinh || {};
      const title = (attrs.ten_day_du || item.title || "").toLowerCase();
      const loai = (attrs.loai_van_ban || "").toLowerCase();
      const soHieu = (attrs.so_hieu || "").toLowerCase();
      return (
        title.includes(query) ||
        loai.includes(query) ||
        soHieu.includes(query)
      );
    });
  }

  get totalItem(): number {
    return this.getFilteredDocuments().length;
  }

  getPaginatedDocuments() {
    const filtered = this.getFilteredDocuments();
    const start = (this.page - 1) * this.pageSize;
    const end = start + this.pageSize;
    return filtered.slice(start, end);
  }

  onPageChange(page: number): void {
    this.page = page;
  }
  onSelectAllDocumentsChange(event: any): void {
    const isChecked = event.target.checked;
    this.selectAllDocuments = isChecked;
    this.documentList.forEach(item => {
      item.selected = isChecked;
      this.updateSelectedFiles(item, isChecked);
    });
  }

  onDocumentSelectChange(event: any, item: any): void {
    const isChecked = event.target.checked;
    item.selected = isChecked;
    this.updateSelectedFiles(item, isChecked);
    // Update "select all" checkbox based on individual selections
    this.selectAllDocuments = this.documentList.every(doc => doc.selected);
  }

  /**
   * Update selectedFiles array when a document is selected/deselected
   */
  private updateSelectedFiles(item: any, isSelected: boolean): void {
    const rawId = item.apiNode?.thuoc_tinh?.ID
    const numericId = Number(rawId);

    if (Number.isNaN(numericId)) {
      return;
    }

    if (isSelected) {
      // Add to selectedFiles if not already present
      if (!this.selectedFiles.some(f => f.id === numericId)) {
        const documentData = this.convertApiNodeToDocumentData(item.apiNode);
        this.selectedFiles.push({
          id: numericId,
          ...documentData
        });
      }
    } else {
      // Remove from selectedFiles
      this.selectedFiles = this.selectedFiles.filter(f => f.id !== numericId);
    }
  }

  /**
   * Convert ApiNode to document data format for saving
   */
  private convertApiNodeToDocumentData(apiNode: ApiNode): any {
    const nodeData: NodeAttributes = apiNode.thuoc_tinh || (apiNode as any);
    
    return {
      ten_day_du: nodeData.ten_day_du || apiNode.ten_day_du || "",
      so_hieu: nodeData.so_hieu || "",
      ngay_ban_hanh: nodeData.ngay_ban_hanh || "",
      loai_van_ban: nodeData.loai_van_ban || "",
      ngay_co_hieu_luc: nodeData.ngay_co_hieu_luc || "",
      ngay_dang_cong_bao: nodeData.ngay_dang_cong_bao || "",
      co_quan_ban_hanh: nodeData.co_quan_ban_hanh || "",
      chuc_danh: nodeData.chuc_danh || "",
      nguoi_ky: nodeData.nguoi_ky || "",
      pham_vi: nodeData.pham_vi || "",
      trich_yeu: nodeData.trich_yeu || "",
      tinh_trang_hieu_luc: nodeData.tinh_trang_hieu_luc || "",
      thoi_gian_cap_nhat: nodeData.thoi_gian_cap_nhat || "",
    };
  }

  /**
   * Save selected documents to workspace
   */
  saveHistoryFiles(): void {
    const workspaceId = this.route.snapshot.params.id;
    if (!workspaceId) {
      this.showErrorToast("Không tìm thấy workspace ID");
      return;
    }

    if (!this.selectedFiles || this.selectedFiles.length === 0) {
      this.showErrorToast("Không có tài liệu nào được chọn");
      return;
    }

    const filesWithWorkspaceId = this.selectedFiles.map((file) => {
      const { ten_day_du, ...rest } = file || {};
      return {
        ...rest,
        title: ten_day_du || "",
        workspace_id: workspaceId,
      };
    });

    this.isSavingFiles = true;
    this.viewDetailFile.addFileBySearch(filesWithWorkspaceId).subscribe(
      (response) => {
        const toastFn =
          response.status === "warning"
            ? this.toast.warning
            : this.toast.success;
        toastFn.call(this.toast, response.message, response.status_title, {
          closeButton: true,
          positionClass: "toast-top-right",
          toastClass: "toast ngx-toastr",
          enableHtml: true,
        });
        this.workspace.isSaveFileFromSearch.next(true);
        // Clear selections after successful save
        this.selectedFiles = [];
        this.documentList.forEach(item => {
          item.selected = false;
        });
        this.selectAllDocuments = false;
        this.isSavingFiles = false;
      },
      (error) => {
        const errorMessage =
          error?.detail ||
          error?.error?.message ||
          error?.message ||
          "Không thể lưu tài liệu";
        this.showErrorToast(errorMessage);
        this.isSavingFiles = false;
      }
    );
  }

  /* -------------------------------
   * Helper Methods
   * ----------------------------- */

  /**
   * Clear all selected documents and uncheck in both views
   */
  clearSelectedDocuments(): void {
    this.selectedFiles = [];
    this.selectAllDocuments = false;
    // Uncheck items in documentList panel/table
    this.documentList.forEach((item) => (item.selected = false));
  }

  /**
   * Invalidate cached computed maps
   */
  private invalidateCaches(): void {
    this.cachedNeighborMap = null;
    this.cachedAdjacencyMap = null;
    this.cachedProtectedNodeIds = null;
  }

  /**
   * Ensure a node exists in the node map, creating a placeholder if needed
   */
  private ensureNodeExists(
    nodeId: string,
    nodeMap: Map<string, GraphNode>,
    transformedNodes: GraphNode[]
  ): void {
    if (!nodeMap.has(nodeId) && !this.hiddenNodeIds.has(nodeId)) {
      const placeholderNode: ApiNode = {
        nhan_ui: "Văn bản",
        id_ui: nodeId,
        thuoc_tinh: { ID: nodeId }
      };
      const placeholder = this.createNodeFromApiNode(placeholderNode, false);
      transformedNodes.push(placeholder);
      nodeMap.set(nodeId, placeholder);
      this.apiNodeMap.set(nodeId, placeholderNode);
    }
  }

  /**
   * Calculate curveness for parallel edges
   * 1st edge: 0, then ±0.2, ±0.35, ...
   */
  private calculateCurveness(index: number): number {
    if (index === 1) return 0;
    
    const k = index - 1;
    const base = k % 2 === 1 ? 0.2 : 0.35; // alternate distances
    const sign = (k % 4 < 2) ? 1 : -1; // alternate sides every two
    return sign * base;
  }

  /**
   * Reset graph state when loading new graph
   */
  private resetGraphState(): void {
    this.expansionHistory.clear();
    this.hiddenNodeIds.clear();
    this.invalidateCaches();
  }

  /**
   * Handle API errors consistently
   */
  private handleApiError(error: any): void {
    this.graphData = null;
    this.viewDetailFile.graphData.next(null);

    const errorMessage =
      error?.detail ||
      error?.error?.message ||
      error?.message ||
      ERROR_MESSAGES.GRAPH_UPDATE_FAILED;

    this.showErrorToast(errorMessage);
  }

  /**
   * Show success toast notification
   */
  private showSuccessToast(message: string): void {
    this.toast.success(message, "Thành công", TOAST_CONFIG.SUCCESS);
  }

  /**
   * Show error toast notification
   */
  private showErrorToast(message: string): void {
    this.toast.error(message, "Lỗi", TOAST_CONFIG.ERROR);
  }

  /**
   * Show warning toast notification
   */
  private showWarningToast(message: string): void {
    this.toast.warning(message, "Thông báo", TOAST_CONFIG.ERROR);
  }

  /**
   * Show info toast notification
   */
  private showInfoToast(message: string): void {
    this.toast.info(message, "Thông báo", TOAST_CONFIG.ERROR);
  }

  /**
   * Update document list from current graph data
   * Extracts all nodes with nhan_ui === "Văn bản" and creates document list items
   * Excludes hidden nodes
   */
  private updateDocumentList(): void {
    if (!this.graphData) {
      this.documentList = [];
      // Clear selectedFiles when graph data is cleared
      this.selectedFiles = [];
      return;
    }

    const apiNodes = safeArray(this.graphData.nodes);
    const documentNodes = apiNodes.filter((node: ApiNode) => {
      const nodeId = getNodeIdFromApiNode(node);
      // Filter only document nodes (not clauses) and exclude hidden nodes
      return node.nhan_ui === "Văn bản" && !this.hiddenNodeIds.has(nodeId);
    });

    // Get IDs of current document nodes
    const currentDocumentIds = new Set(
      documentNodes.map((node: ApiNode) => getNodeIdFromApiNode(node))
    );

    // Remove from selectedFiles any documents that are no longer in the list
    this.selectedFiles = this.selectedFiles.filter(f =>
      currentDocumentIds.has(String(f.id))
    );

    // Create a map of existing selections to preserve them
    const existingSelections = new Map<string, boolean>();
    this.documentList.forEach(doc => {
      existingSelections.set(doc.id, doc.selected);
    });

    // Build new document list
    this.documentList = documentNodes.map((node: ApiNode) => {
      const nodeId = getNodeIdFromApiNode(node);
      const thuocTinh = node.thuoc_tinh || {};
      
      // Create label: loai_van_ban + so_hieu
      const loaiVanBan = thuocTinh.loai_van_ban || "";
      const soHieu = thuocTinh.so_hieu || "";
      const title = loaiVanBan && soHieu 
        ? `${loaiVanBan} - ${soHieu}`
        : node.id_ui || nodeId || "Văn bản";

      // Preserve existing selection if node already exists, otherwise default to false
      const wasSelected = existingSelections.get(nodeId) || false;

      return {
        id: nodeId,
        title: title,
        selected: wasSelected,
        apiNode: node,
      };
    });

    // Update "select all" state
    this.selectAllDocuments = this.documentList.length > 0 && this.documentList.every(doc => doc.selected);
  }
}
