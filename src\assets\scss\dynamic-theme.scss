// Dynamic theme CSS custom properties
// This file will be imported to override Bootstrap's primary color at runtime

:root {
  --bs-primary: var(--primary-color, #008fe3);
  --bs-primary-rgb: var(--primary-color-rgb, 0, 143, 227);
}

// Override Bootstrap primary color classes
.btn-primary-theme {
  background-color: var(--primary-color, #008fe3) !important;
  border-color: var(--primary-color, #008fe3) !important;

  color: white;
  transition: background-color 0.2s ease, border-color 0.2s ease,
    box-shadow 0.2s ease;
}

.btn-primary-theme:hover {
  background-color: color-mix(
    in srgb,
    var(--primary-color, #008fe3),
    black 10%
  ) !important;
  border-color: color-mix(
    in srgb,
    var(--primary-color, #008fe3),
    black 12%
  ) !important;

  color: white;
}

.btn-primary-theme:focus,
.btn-primary-theme.focus {
  background-color: color-mix(
    in srgb,
    var(--primary-color, #008fe3),
    black 10%
  ) !important;
  border-color: color-mix(
    in srgb,
    var(--primary-color, #008fe3),
    black 12%
  ) !important;
  box-shadow: 0 0 0 0.2rem
    color-mix(in srgb, var(--primary-color, #008fe3), transparent 50%) !important;

  color: white;
}

.btn-primary-theme:not(:disabled):not(.disabled):active,
.btn-primary-theme:not(:disabled):not(.disabled).active {
  background-color: color-mix(
    in srgb,
    var(--primary-color, #008fe3),
    black 20%
  ) !important;
  border-color: color-mix(
    in srgb,
    var(--primary-color, #008fe3),
    black 22%
  ) !important;

  color: white;
}

// Override other primary color usages
.text-primary-theme {
  color: var(--primary-color, #008fe3) !important;
}

.gradient-text-primary-theme {
  background: linear-gradient(
    to left,
    var(--primary-color) 0%,
    rgba(var(--primary-color-rgb, 0, 143, 227), 0.8) 45%,
    rgba(var(--primary-color-rgb, 0, 143, 227), 0) 50%,
    rgba(var(--primary-color-rgb, 0, 143, 227), 0.8) 55%,
    var(--primary-color) 100%
  );
  background-size: 200% auto;
  -webkit-background-clip: text;
  color: transparent;
  -webkit-text-fill-color: transparent;
  text-fill-color: transparent;
  animation: textShine 2.5s;
}

.ng-select .ng-option.ng-option-marked {
  background-color: rgba(
    var(--primary-color-rgb, 0, 143, 227),
    0.12
  ) !important;
  color: var(--primary-color) !important;
}

.ng-select .ng-option.ng-option-selected {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.ng-select.ng-select-multiple .ng-value {
  background-color: var(--primary-color) !important;
}

.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  background-color: var(--primary-color) !important;
  color: white !important;
}

.nav-pills .nav-link.active {
  border-color: var(--primary-color, #008fe3);
  box-shadow: 0 4px 18px -4px rgba(var(--primary-color-rgb, 0, 143, 227), 0.65);
}

.badge-light-primary-theme {
  background-color: rgba(var(--primary-color-rgb, 0, 143, 227), 0.12) !important;
  color: var(--primary-color, #008fe3) !important;
}

.bg-primary-theme {
  background-color: var(--primary-color, #008fe3) !important;
}

.border-primary-theme {
  border: 1px solid var(--primary-color) !important
}

.btn-outline-primary-theme {
    border: 1px solid var(--primary-color) !important;
    background-color: transparent;
    color: rgb(33, 33, 33);
}

.btn-outline-primary-theme:hover {
  background-color: rgba(var(--primary-color-rgb, 0, 143, 227), 0.1) !important;
}

.btn-outline-primary-theme:focus {
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb, 0, 143, 227), 0.25) !important;
}

.btn-outline-primary-theme:active,
.btn-group > .btn.active {
  background-color: rgba(var(--primary-color-rgb, 0, 143, 227), 0.3) !important;
}

// Override navbar and other components that use primary color
.navbar-primary {
  background-color: var(--primary-color, #008fe3) !important;
}

// Override any other Bootstrap components that use primary color
.main-menu.menu-light .navigation > li.active > a {
  background: linear-gradient(
    118deg,
    var(--primary-color),
    rgba(var(--primary-color-rgb, 0, 143, 227), 0.7)
  ) !important;
  box-shadow: 0 0 10px 1px rgba(var(--primary-color-rgb, 0, 143, 227), 0.7) !important;
  color: #fff !important;
  font-weight: 400 !important;
  border-radius: 4px !important;
}

.alert-primary {
  color: #004085;
  background-color: rgba(var(--primary-color-rgb, 0, 143, 227), 0.1);
  border-color: rgba(var(--primary-color-rgb, 0, 143, 227), 0.2);
}

.badge-primary {
  background-color: var(--primary-color, #008fe3) !important;
}

// Override progress bars
.progress-bar {
  background-color: var(--primary-color, #008fe3) !important;
}

// Override pagination
.page-link {
  color: var(--primary-color, #008fe3) !important;
}

.page-item.active .page-link {
  background-color: var(--primary-color, #008fe3) !important;
  border-color: var(--primary-color, #008fe3) !important;
}

.page-item.disabled .page-link {
  color: color-mix(in srgb, var(--primary-color, #008fe3) 25%, #b9b9c3);
  background-color: color-mix(in srgb, var(--primary-color, #008fe3) 5%, #f3f2f7);
  border-color: color-mix(in srgb, var(--primary-color, #008fe3) 10%, #dae1e7);
  pointer-events: none;
  cursor: not-allowed;
  opacity: 0.6;
  transition: all 0.2s ease;
}

// Override form controls
.form-control:focus {
  border-color: var(--primary-color, #008fe3) !important;
  box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb, 0, 143, 227), 0.25) !important;
}

.custom-control-input:checked ~ .custom-control-label::before {
  background-color: var(--primary-color, #008fe3) !important;
  border-color: var(--primary-color, #008fe3) !important;
}
