/* ===== Variables riêng cho footer ===== */
:host {
  --brand-accent: #008FE3;
}

/* ===== Footer full-bleed (tràn chiều ngang) ===== */
.landing__footer {
  background: #0f3656;
  color: #dbe7f5;
  padding: 70px 0 50px;
  border-top: 1px solid rgba(255, 255, 255, 0.06);

  /* full-bleed ra mép màn hình */
  margin-left: calc(50% - 50vw);
  margin-right: calc(50% - 50vw);
  width: 100vw;
}

/* ===== Grid chính trong footer ===== */
.landing__footer-container {
  max-width: 1580px;
  margin: 0 auto;
  padding: 0 16px;

  display: grid;
  grid-template-columns: 1.35fr 1fr;
  grid-template-rows: auto auto;
  grid-template-areas:
    "info map"
    "news map";
  align-items: start;
  gap: 28px;
}

/* ===== Cột thông tin bên trái ===== */
.landing__footer-info {
  grid-area: info;
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 560px; 
}

/* ===== Thương hiệu: logo + CLS ===== */
.landing__footer-brand {
  display: flex;
  align-items: center; 
  gap: 16px;
  margin-bottom: 22px;
}
.landing__footer-logo {
  width: 150px;
  height: auto;
  display: block;
  object-fit: contain;
  transform: translateY(1px);  
}

.landing__footer-brand-text {
  font-weight: 700;
  letter-spacing: 0.3px;
  line-height: 1;
  color: var(--brand-accent);
  font-size: 32px;
}

/* Tagline */
.landing__footer-tagline {
  max-width: 720px;
  line-height: 1.55;
  margin: 0 0 12px 0;
}

.tagline-br-desktop {
  display: inline;
}
@media (min-width: 992px) {
  .tagline-br-desktop {
    display: block;
  }
}

/* Danh sách thông tin liên hệ */
.landing__footer-list {
  list-style: none;
  padding: 0;
  margin: 6px 0 0 0;
}

.landing__footer-list li {
  margin: 4px 0;
  color: #e7f0fb;
  font-size: 14px;
}

.landing__footer-row {
  display: grid;
  grid-template-columns: 90px 1fr;
  column-gap: 16px;
  align-items: start;
}

.landing__footer-label {
  color: #a7c0e8;
  font-weight: 600;
  display: inline-block;
  width: 90px;
}

.landing__footer-value a {
  color: #cfe0f7;
  text-decoration: none;
}
.landing__footer-value a:hover {
  text-decoration: underline;
}

/* ===== Cột bản đồ ===== */
.landing__footer-map {
  grid-area: map;
  min-height: 320px;
  align-self: stretch;
}

.landing__footer-map-frame {
  width: 100%;
  height: 100%;
  min-height: 320px;
  border: 0;
  border-radius: 12px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.18);
}

/* ===== Newsletter card ===== */
.landing__footer-newsletter {
  grid-area: news;
  align-self: end;

  background: #ffffff;
  border: 1px solid #e6ecf5;
  border-radius: 14px;
  padding: 36px;

  display: grid;
  grid-template-columns: 1.2fr minmax(220px, 0.7fr) auto;
  align-items: center;
  gap: 12px;
  max-width: 880px;

  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.landing__footer-newsletter-text {
  color: #1f2a37;
  font-size: 13.5px;
  line-height: 1.5;
  margin: 0;
}

.landing__footer-input {
  width: 100%;
  border: 0;
  outline: none;
  height: 42px;
  padding: 0 12px;
  border-radius: 8px;
  background: #f3f5f8;
}

.landing__footer-button {
  height: 42px;
  padding: 0 16px;
  border: 0;
  border-radius: 10px;
  background: #2f6fb5;
  color: #fff;
  font-weight: 600;
  cursor: pointer;
  min-width: 108px;
}
.landing__footer-button:hover {
  filter: brightness(1.05);
}

/* Link nhấn mạnh */
.landing__footer-link--accent {
  color: var(--brand-accent) !important;
  font-weight: 600;
}

/* ===== Bottom bar ===== */
.landing__footer-bottom {
  max-width: 1580px;
  margin: 20px auto 0;
  padding: 12px 16px 0;
  border-top: 1px solid rgba(255, 255, 255, 0.08);

  display: flex;
  align-items: center;
  justify-content: space-between;

  color: #b8cdea;
  font-size: 13px;
}

.landing__footer-bottom-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.landing__footer-bottom-label {
  color: #cfe0f7;
  font-weight: 600;
}

.landing__footer-link {
  color: #cfe0f7;
  text-decoration: none;
}
.landing__footer-link:hover {
  text-decoration: underline;
}

/* ===== Responsive ===== */
@media (max-width: 992px) {
  .landing__footer-container {
    grid-template-columns: 1fr;
    grid-template-areas:
      "info"
      "map"
      "news";
  }

  .landing__footer-map,
  .landing__footer-map-frame {
    min-height: 260px;
  }

  .landing__footer-newsletter {
    grid-template-columns: 1fr;
    gap: 10px;
    max-width: 100%;
    padding: 18px;
  }

  .landing__footer-button {
    width: 100%;
  }

  .landing__footer-brand {
    margin-bottom: 18px;
  }

  .landing__footer-logo {
    width: 120px;
  }

  .landing__footer-brand-text {
    font-size: 26px;
  }
}

@media (max-width: 576px) {
  .landing__footer {
    padding: 48px 0 32px;
  }

  .landing__footer-tagline,
  .landing__footer-list li {
    font-size: 13.5px;
  }

  .landing__footer-row {
    grid-template-columns: 1fr;
  }

  .landing__footer-logo {
    width: 100px;
  }

  .landing__footer-brand-text {
    font-size: 24px;
  }

  .landing__footer-bottom {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
