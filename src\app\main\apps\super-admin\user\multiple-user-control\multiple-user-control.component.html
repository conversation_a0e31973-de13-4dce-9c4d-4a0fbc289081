<!-- File Upload Section -->
<div class="d-flex align-items-center justify-content-between mb-1">
	<h4 class="mb-0">Tải lên file Excel</h4>
	<button type="button" class="btn btn-outline-success"
		(click)="downloadTemplate()">
		<i data-feather="download" class="mr-1"></i>
		Tải template mẫu
	</button>
</div>

<div class="upload-area" 
	[class.drag-over]="isDragOver"
	(click)="fileInput.click()"
	(dragover)="onDragOver($event)"
	(dragleave)="onDragLeave($event)"
	(drop)="onDrop($event)">
	<div class="upload-content">
		<i class="feather-upload-cloud upload-icon"></i>
		<h5 class="upload-title">Kéo thả file Excel vào đây hoặc click để chọn file</h5>
		<p class="upload-subtitle">Chỉ hỗ trợ file .xlsx, .xls</p>
		<div class="upload-file-info" *ngIf="selectedFile">
			<span>{{ selectedFile.name }}</span>
			<button type="button" class="btn btn-icon btn-outline-danger" rippleEffect (click)="removeFile()">
				<span data-feather="x"></span>
			</button>
		</div>
	</div>
	<input #fileInput 
		type="file"
		class="d-none" 
		accept=".xlsx,.xls" 
		(change)="onFileSelected($event)">
</div>

<div class="mt-2 d-flex justify-content-center" *ngIf="selectedFile && usersFromExcel.length === 0">
	<button type="button" 
		class="btn btn-primary" 
		(click)="processExcelFile()"
		[disabled]="isProcessing">
		<i class="feather-upload" *ngIf="!isProcessing"></i>
		<span class="spinner-border spinner-border-sm" *ngIf="isProcessing"></span>
		{{ isProcessing ? 'Đang xử lý...' : 'Xử lý file Excel' }}
	</button>
</div>

<!-- Success Message -->
<div class="mt-2" *ngIf="successMessage">
	<ngb-alert [type]="'success'" [dismissible]="false">
		<div class="alert-body">
			<span data-feather="check" class="mr-1"></span>
			{{ successMessage }}
		</div>
	</ngb-alert>
</div>

<!-- Warning Message -->
<div class="mt-2" *ngIf="warningMessage">
	<ngb-alert [type]="'warning'" [dismissible]="false">
		<div class="alert-body">
			<span data-feather="check" class="mr-1"></span>
			{{ warningMessage }}
			<div *ngIf="skippedEmails && skippedEmails.length > 0" class="mt-1">
				<span>Danh sách email bị bỏ qua:</span>
				<ul class="mb-0 ml-2">
					<li *ngFor="let email of skippedEmails">{{ email }}</li>
				</ul>
			</div>
		</div>
	</ngb-alert>
</div>

<!-- Error Message -->
<div class="mt-2" *ngIf="errorMessage">
	<ngb-alert [type]="'danger'" [dismissible]="false">
		<div class="alert-body">
			<span data-feather="x" class="mr-1"></span>
			{{ errorMessage }}
		</div>
	</ngb-alert>
</div>

<!-- Users Table -->
<div *ngIf="usersFromExcel.length > 0">
	<div class="row align-items-center mb-2">
		<div class="col-12 col-md-auto">
			<h4 class="mb-1 mb-md-0">Danh sách người dùng từ file Excel</h4>
		</div>
		<div class="col-12 col-md">
			<p class="mb-1 mb-md-0">Tổng số: {{ usersFromExcel.length }} người dùng</p>
		</div>
		<div class="col-12 col-md-auto mt-1 mt-md-0 d-flex justify-content-end">
			<button type="button" class="btn btn-outline-primary" (click)="addUserRow()">
				<span data-feather="plus"></span>
				Thêm dòng
			</button>
		</div>
	</div>

	<div class="mb-1 d-flex align-items-center">
		<label class="mb-0 mr-1 min-width-80px">Tổ chức: <span class="text-danger">*</span></label>
		<input
			class="form-control width-320px cursor-pointer"
			[ngClass]="{
				'is-valid': selectedOrganization,
				'is-invalid': !selectedOrganization
			}"
			[placeholder]="'Chọn tổ chức'"
			[value]="selectedOrganization?.name || ''"
			(click)="openOrganizationPicker()"
			readonly
		/>
		<!-- Info icon with tooltip -->
		<span 
			class="ml-1"
			ngbTooltip="Các người dùng bên dưới sẽ được thêm vào tổ chức này"
			container="body"
			triggers="hover"
			>
			<i data-feather="info" class="align-middle cursor-pointer"></i>
		</span>
		
	</div>

	<div class="table-responsive">
		<table class="table table-striped">
			<thead>
				<tr>
					<th class="px-25 width-50px min-width-50px text-center">STT</th>
					<th class="px-25 width-220px min-width-180px">Email <span class="text-danger">*</span></th>
					<th class="px-25 width-150px min-width-130px">Mật khẩu <span class="text-danger">*</span></th>
					<th class="px-25 width-180px min-width-140px">Họ và tên <span class="text-danger">*</span></th>
					<th class="px-25 width-140px min-width-120px">Số điện thoại</th>
					<th class="px-25 width-100px min-width-90px">Giới tính</th>
					<th class="px-25 width-130px min-width-110px">Ngày sinh</th>
					<th class="px-25 width-120px min-width-80px"></th>
					<th class="px-25 width-60px min-width-50px"></th>
				</tr>
			</thead>
			<tbody>
				<tr *ngFor="let user of usersFromExcel; let i = index">
					<td class="px-25 text-center">{{ i + 1 }}</td>
					<td class="px-25">
						<input class="form-control" [(ngModel)]="user.email" (ngModelChange)="onUserChange(i)" placeholder="Email"/>
					</td>
					<td class="px-25">
							<div class="input-group input-group-merge form-password-toggle">
								<input
									[type]="user.pwdShow ? 'text' : 'password'"
									class="form-control"
									[(ngModel)]="user.password"
									(ngModelChange)="onUserChange(i)"
									placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;"
									aria-describedby="basic-default-password1"
								/>
								<div class="input-group-append" (click)="user.pwdShow = !user.pwdShow">
									<span class="input-group-text cursor-pointer">
										<i
											class="feather"
											[ngClass]="{
												'icon-eye-off': user.pwdShow,
												'icon-eye': !user.pwdShow
											}"
										></i>
									</span>
								</div>
							</div>
					</td>
					<td class="px-25">
						<input class="form-control" [(ngModel)]="user.fullname" (ngModelChange)="onUserChange(i)" placeholder="Họ và tên"/>
					</td>
					<td class="px-25">
						<input class="form-control" [(ngModel)]="user.phone" (ngModelChange)="onUserChange(i)" placeholder="Số điện thoại"/>
					</td>
					<td class="px-25">
						<select class="form-control" [(ngModel)]="user.gender" (ngModelChange)="onUserChange(i)">
							<option value="">Để trống</option>
							<option value="Nam">Nam</option>
							<option value="Nữ">Nữ</option>
						</select>
					</td>
					<td class="px-25">
						<ng2-flatpickr
							#flatpickrRef
							[config]="basicDateOptions"
							name="customDate"
							placeholder="dd-mm-yyyy"
							[(ngModel)]="user.dob"
							(ngModelChange)="onUserChange(i)"
						></ng2-flatpickr>
					</td>
					<td class="px-25 text-center">
						<span class="badge badge-success" *ngIf="user.errors.length == 0">Hợp lệ</span>
						<span class="badge badge-danger" *ngIf="user.errors.length != 0">{{ user.errors[0] }}</span>
					</td>
					<td class="px-25 text-center">
						<button 
							type="button" 
							class="btn btn-icon btn-outline-danger" 
							(click)="deleteUserRow(i)" 
							[disabled]="usersFromExcel.length === 1"
							container="body"
							ngbTooltip="Xóa dòng"
						>
							<span data-feather="trash"></span>
						</button>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	
	<div class="mt-3 d-flex justify-content-end">
		<button type="button" 
						class="btn btn-secondary" 
						(click)="clearData()">
			<i class="feather-refresh-cw"></i>
			Làm mới
		</button>
		<button type="button" 
						class="btn btn-primary ml-2" 
						(click)="saveUsers()"
						[disabled]="!canSaveUsers()">
			<i class="feather-save"></i>
			Lưu danh sách người dùng
		</button>
	</div>
</div>

<!-- Modal chọn tổ chức -->
<ng-template #organizationModal let-modal>
  <div class="modal-header">
    <h4 class="modal-title">Chọn tổ chức</h4>
		<button type="button" class="close" (click)="modal.dismiss('Cross click')" aria-label="Close">
			<span aria-hidden="true">&times;</span>
		</button>
  </div>

  <div class="modal-body">
		<div class="text-primary fw-semibold ms-auto mb-25">
      Đang chọn: {{ tempSelectedOrganization?.name || 'Chưa chọn' }}
    </div>
    <div class="mb-1 position-relative">
      <div class="input-with-icon position-relative">
        <button type="button" class="btn-icon-inside left-icon">
          <i data-feather="search"></i>
        </button>
        <input
          type="text"
          class="form-control"
          placeholder="Tìm kiếm theo tên tổ chức"
          (input)="orgSearchText = $any($event.target).value || ''"
        />
      </div>
    </div>

    <div class="tree-wrap">
      <ng-container *ngFor="let node of treeOrg">
        <ng-container
          *ngTemplateOutlet="organizationTreeNodeTpl; context: { $implicit: node, level: 0, search: orgSearchText }"
        ></ng-container>
      </ng-container>
    </div>

    <ng-template #organizationTreeNodeTpl let-node let-level="level" let-search="search">
      <ng-container *ngIf="shouldShowNode(node, search)">
        <div
          class="tree-row cursor-pointer"
          [ngStyle]="{'padding-left.px': 12 + level * 20}"
          [class.has-children]="node.children?.length"
          [class.active]="isTempSelected(node)"
          (click)="setTempOrganization(node)"
          (dblclick)="confirmOrganization(modal, node)"
        >
          <button
            type="button"
            class="toggle"
            *ngIf="node.children?.length"
            (click)="$event.stopPropagation(); node.showChildren = !node.showChildren"
            [attr.aria-label]="node.showChildren ? 'Collapse' : 'Expand'"
          >
            <i
              class="feather"
              [ngClass]="{
                'icon-chevron-down': node.showChildren,
                'icon-chevron-right': !node.showChildren
              }"
            ></i>
          </button>
          <i class="feather node-icon icon-briefcase"></i>
          <div class="node-text">
            <span class="node-name fw-semibold">
              <strong>{{ node.name }}</strong>
            </span>
          </div>
          <span class="badge badge-light-primary ms-auto" *ngIf="isTempSelected(node)">Đang chọn</span>
        </div>
        <div *ngIf="node.children?.length && node.showChildren">
          <ng-container *ngFor="let child of node.children">
            <ng-container
              *ngTemplateOutlet="organizationTreeNodeTpl; context: { $implicit: child, level: level + 1, search: search }"
            ></ng-container>
          </ng-container>
        </div>
      </ng-container>
    </ng-template>
  </div>

  <div class="modal-footer border-0">
    <button type="button" class="btn btn-secondary" (click)="modal.dismiss()">
      Hủy
    </button>
    <button
      type="button"
      class="btn btn-primary-theme"
      [disabled]="!tempSelectedOrganization"
      (click)="confirmOrganization(modal)"
    >
      Chọn
    </button>
  </div>
</ng-template>
