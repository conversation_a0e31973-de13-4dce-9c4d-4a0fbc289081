<core-card
  id="tim-kiem"
  [isReload]="isReload"
  class="card m-0 tour-view-file height-auto position-relative overflow-x-hidden overflow-y-auto"
>
  <div class="px-1 py-50 d-flex align-items-center">
    <p class="mb-0 font-weight-bolder h4">Tìm kiếm</p>
    <button
      [ngClass]="{
        'btn-primary text-primary': isShowFastSearch,
        'btn-outline-secondary': !isShowFastSearch,
        'mr-3': type == 4
      }"
      type="button"
      class="btn btn-sm ml-auto"
      rippleEffect
      (click)="isShowFastSearch = !isShowFastSearch"
    >
      <span [data-feather]="'search'" [class]="'mr-25'"></span>Tra cứu nhanh
    </button>
  </div>
  <hr />
  <div class="d-flex row">
    <div
      class="content-body w-100"
      [ngClass]="isShowFastSearch ? 'col-9' : 'col-12'"
    >
      <div class="d-flex flex-shrink-0">
        <div class="card-body px-1 py-0">
          <form class="form form-vertical">
            <div class="row mt-1">
              <div class="col-xxl-11 col-lg-10 col-md-8 col-8">
                <!-- <label class="label font-weight-bolder">Tìm kiếm </label> -->
                <input
                  type="text"
                  [(ngModel)]="searchString"
                  class="form-control"
                  [ngModelOptions]="{ standalone: true }"
                  placeholder="Nhập nội dung cần tìm"
                  (keydown.enter)="searchDieuKhoan()"
                />
              </div>

              <button
                (click)="searchDieuKhoan()"
                type="button"
                class="btn btn-icon btn-primary-theme"
                rippleEffect
                [disabled]="searchString == ''"
              >
                <span [data-feather]="'search'"></span>
              </button>

              <div class="col-12 mt-1 d-flex justify-content-between mb-1">
                <div class="col-12 p-0">
                  <div
                    class="btn-group btn-group-toggle"
                    ngbRadioGroup
                    name="radioBasic"
                    [(ngModel)]="search_legal_term"
                    (ngModelChange)="onSearchLegalTermChange($event)"
                  >
                    <label
                      ngbButtonLabel
                      class="btn-outline-primary-theme btn-sm"
                      rippleEffect
                    >
                      <input ngbButton type="radio" [value]="SLT.TieuDe" /> Tiêu
                      đề
                    </label>
                    <label
                      ngbButtonLabel
                      class="btn-outline-primary-theme btn-sm"
                      rippleEffect
                    >
                      <input ngbButton type="radio" [value]="SLT.VanBan" /> Văn
                      bản
                    </label>
                    <label
                      ngbButtonLabel
                      class="btn-outline-primary-theme btn-sm"
                      rippleEffect
                    >
                      <input ngbButton type="radio" [value]="SLT.DieuKhoan" />
                      Điều khoản
                    </label>
                  </div>
                  <button
                    type="button"
                    class="btn btn-sm ml-1"
                    rippleEffect
                    (click)="isSearchAdvance = !isSearchAdvance"
                    [ngClass]="{
                      'btn-primary-theme': isSearchAdvance,
                      'border-primary-theme': !isSearchAdvance
                    }"
                  >
                    <i class="feather mr-25" [ngClass]="isSearchAdvance ? 'icon-chevrons-up' : 'icon-chevrons-down'"></i>
                    {{ isSearchAdvance ? 'Thu gọn' : 'Thêm điều kiện' }}
                  </button>
                </div>
              </div>
              <ng-container *ngIf="isSearchAdvance">
                <div class="row w-100 m-0">
                  <!-- HÀNG 1: Checkboxes (independent row) -->
                  <div class="col-12 mb-1 d-flex align-items-center">
                    <div class="custom-control custom-checkbox mr-3">
                      <input
                        type="checkbox"
                        class="custom-control-input"
                        id="customCheck1"
                        [checked]="sat_nhap_tinh"
                        (change)="toggleNewDistrict($event)"
                      />
                      <label class="custom-control-label" for="customCheck1"
                        >Địa giới sau sáp nhập</label
                      >
                    </div>

                    <div class="custom-control custom-checkbox">
                      <input
                        type="checkbox"
                        class="custom-control-input cursor-pointer"
                        id="checkBoMoi"
                        [checked]="boMoi"
                        (change)="toggleBoMoi($event)"
                      />
                      <label class="custom-control-label" for="checkBoMoi"
                        >Bộ sau sáp nhập</label
                      >
                    </div>
                  </div>

                  <!-- HÀNG 2: Thể loại văn bản | Ngày có hiệu lực -->
                  <div class="col-xl-6 col-lg-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >Thể loại văn bản</label
                      >
                      <div class="col">
                        <ng-select
                          class="ng-select-size-sm"
                          multiple="true"
                          [items]="listTheLoaiVanBan"
                          bindLabel="label"
                          bindValue="value"
                          placeholder="Tất cả"
                          [clearable]="false"
                          (change)="changeTheLoaiVanBan($event)"
                          [closeOnSelect]="true"
                          [(ngModel)]="valueTheLoaiVanBan"
                          [ngModelOptions]="{ standalone: true }"
                        >
                          <ng-template ng-option-tmp let-item="item">
                            <div>{{ item.label }}</div>
                          </ng-template>
                          <ng-template
                            ng-multi-label-tmp
                            let-items="items"
                            let-clear="clear"
                          >
                            <div
                              class="ng-value"
                              *ngFor="let item of items | slice : 0 : 2"
                            >
                              <span class="ng-value-label">{{
                                item.label
                              }}</span>
                              <span
                                class="ng-value-icon right"
                                (click)="clear(item)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                            <div class="ng-value" *ngIf="items.length > 2">
                              <span class="ng-value-label"
                                >{{ items.length - 2 }}
                                {{ "More" | translate }}</span
                              >
                              <span
                                class="ng-value-icon right"
                                (click)="clearAllRemainingItems(items, clear)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                          </ng-template>
                        </ng-select>
                      </div>
                    </div>
                  </div>

                  <div class="col-xl-6 col-lg-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >Ngày có hiệu lực</label
                      >
                      <div class="col">
                        <ng2-flatpickr
                          #datePickerCoHieuLuc
                          [config]="customDateOptionsCoHieuLuc"
                          name="customDateCoHieuLuc"
                          placeholder="Tất cả"
                          (change)="changeNgayCoHieuLuc($event)"
                          (keydown.enter)="dropdown.stopPropagation()"
                        ></ng2-flatpickr>
                      </div>
                    </div>
                  </div>

                  <!-- HÀNG 3: Ngày ban hành | Nơi ban hành -->
                  <div class="col-xl-6 col-lg-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >Ngày ban hành</label
                      >
                      <div class="col">
                        <ng2-flatpickr
                          #datePicker
                          [config]="customDateOptions"
                          name="customDate"
                          placeholder="Tất cả"
                          (change)="changeNgayBanHanh($event)"
                          (keydown.enter)="dropdown.stopPropagation()"
                        ></ng2-flatpickr>
                      </div>
                    </div>
                  </div>

                  <div class="col-xl-6 col-lg-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >Nơi ban hành</label
                      >
                      <div class="col">
                        <ng-select
                          class="ng-select-size-sm"
                          multiple="true"
                          [items]="listCoQuanBanHanh"
                          bindValue="value"
                          bindLabel="label"
                          [clearable]="false"
                          placeholder="Tất cả"
                          (change)="selectCoQuanBanHanh($event)"
                          [closeOnSelect]="true"
                          [(ngModel)]="valueCoQuanBanHanh"
                          [ngModelOptions]="{ standalone: true }"
                        >
                          <ng-template ng-option-tmp let-item="item">
                            <div
                              [ngbTooltip]="item.label"
                              placement="left"
                              container="body"
                            >
                              {{ item.label }}
                            </div>
                          </ng-template>
                          <ng-template
                            ng-multi-label-tmp
                            let-items="items"
                            let-clear="clear"
                          >
                            <div
                              class="ng-value"
                              *ngFor="let item of items | slice : 0 : 2"
                            >
                              <span class="ng-value-label">{{
                                item.label
                              }}</span>
                              <span
                                class="ng-value-icon right"
                                (click)="clear(item)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                            <div class="ng-value" *ngIf="items.length > 2">
                              <span class="ng-value-label"
                                >{{ items.length - 2 }}
                                {{ "More" | translate }}</span
                              >
                              <span
                                class="ng-value-icon right"
                                (click)="clearAllRemainingItems(items, clear)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                          </ng-template>
                        </ng-select>
                      </div>
                    </div>
                  </div>

                  <!-- HÀNG 3: Tình trạng hiệu lực | Cơ quan quản lý -->
                  <div class="col-xl-6 col-lg-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >Tình trạng hiệu lực</label
                      >
                      <div class="col">
                        <ng-select
                          class="ng-select-size-sm"
                          multiple="true"
                          [items]="listTrangThaiHieuLuc"
                          bindValue="value"
                          bindLabel="label"
                          [clearable]="false"
                          placeholder="Tất cả"
                          (change)="selectTrangThaiHieuLuc($event)"
                          [closeOnSelect]="true"
                          [(ngModel)]="valueTrangThai"
                          [ngModelOptions]="{ standalone: true }"
                        >
                          <ng-template ng-option-tmp let-item="item">
                            <div>{{ item.label }}</div>
                          </ng-template>
                          <ng-template
                            ng-multi-label-tmp
                            let-items="items"
                            let-clear="clear"
                          >
                            <div
                              class="ng-value"
                              *ngFor="let item of items | slice : 0 : 2"
                            >
                              <span class="ng-value-label">{{
                                item.label
                              }}</span>
                              <span
                                class="ng-value-icon right"
                                (click)="clear(item)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                            <div class="ng-value" *ngIf="items.length > 2">
                              <span class="ng-value-label"
                                >{{ items.length - 2 }}
                                {{ "More" | translate }}</span
                              >
                              <span
                                class="ng-value-icon right"
                                (click)="clearAllRemainingItems(items, clear)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                          </ng-template>
                        </ng-select>
                      </div>
                    </div>
                  </div>

                  <div class="col-xl-6 col-lg-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >Nơi quản lý</label
                      >
                      <div class="col">
                        <ng-select
                          class="ng-select-size-sm"
                          multiple="true"
                          [items]="listCoQuanQuanLy"
                          bindValue="value"
                          bindLabel="label"
                          [clearable]="false"
                          placeholder="Tất cả"
                          (change)="selectCoQuanQuanLy($event)"
                          [closeOnSelect]="true"
                          [(ngModel)]="valueCoQuanQuanLy"
                          [ngModelOptions]="{ standalone: true }"
                        >
                          <ng-template ng-option-tmp let-item="item">
                            <div
                              [ngbTooltip]="item.label"
                              placement="left"
                              container="body"
                            >
                              {{ item.label }}
                            </div>
                          </ng-template>
                          <ng-template
                            ng-multi-label-tmp
                            let-items="items"
                            let-clear="clear"
                          >
                            <div
                              class="ng-value"
                              *ngFor="let item of items | slice : 0 : 2"
                            >
                              <span class="ng-value-label">{{
                                item.label
                              }}</span>
                              <span
                                class="ng-value-icon right"
                                (click)="clear(item)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                            <div class="ng-value" *ngIf="items.length > 2">
                              <span class="ng-value-label"
                                >{{ items.length - 2 }}
                                {{ "More" | translate }}</span
                              >
                              <span
                                class="ng-value-icon right"
                                (click)="clearAllRemainingItems(items, clear)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                          </ng-template>
                        </ng-select>
                      </div>
                    </div>
                  </div>

                  <!-- HÀNG 4: Loại văn bản | Số hiệu -->
                  <div class="col-xl-6 col-lg-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >Loại văn bản</label
                      >
                      <div class="col">
                        <ng-select
                          class="ng-select-size-sm"
                          multiple="true"
                          [items]="listLoaiVanBan"
                          bindValue="value"
                          bindLabel="label"
                          [clearable]="false"
                          placeholder="Tất cả"
                          (change)="selectLoaiVanBan($event)"
                          [closeOnSelect]="true"
                          [(ngModel)]="valueLoaiVanBan"
                          [ngModelOptions]="{ standalone: true }"
                        >
                          <ng-template ng-option-tmp let-item="item">
                            <div>{{ item.label }}</div>
                          </ng-template>
                          <ng-template
                            ng-multi-label-tmp
                            let-items="items"
                            let-clear="clear"
                          >
                            <div
                              class="ng-value"
                              *ngFor="let item of items | slice : 0 : 2"
                            >
                              <span class="ng-value-label">{{
                                item.label
                              }}</span>
                              <span
                                class="ng-value-icon right"
                                (click)="clear(item)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                            <div class="ng-value" *ngIf="items.length > 2">
                              <span class="ng-value-label"
                                >{{ items.length - 2 }}
                                {{ "More" | translate }}</span
                              >
                              <span
                                class="ng-value-icon right"
                                (click)="clearAllRemainingItems(items, clear)"
                                aria-hidden="true"
                                >×</span
                              >
                            </div>
                          </ng-template>
                        </ng-select>
                      </div>
                    </div>
                  </div>
                  <div class="col-xl-6 col-lg-12">
                    <div class="form-group row no-gutters align-items-center">
                      <label
                        class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                        >Số lượng</label
                      >
                      <div class="col">
                        <ng-select
                          class="ng-select-size-sm"
                          [(ngModel)]="limit"
                          [items]="sizePage"
                          [clearable]="false"
                          placeholder="Tất cả"
                          (change)="changeLimitSize($event)"
                          [ngModelOptions]="{ standalone: true }"
                        ></ng-select>
                      </div>
                    </div>
                  </div>
                  <!-- HÀNG 5: Điều kiện (full width) -->
                  <div class="col-xl-6 col-lg-12">
                      <div class="form-group row no-gutters align-items-center">
                        <label
                          class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                          ngbTooltip="Nhập các từ bạn muốn xuất hiện trong văn bản. Hệ thống sẽ ưu tiên hiển thị các văn bản có chứa những từ này, nhưng không bắt buộc tất cả đều có."
                          container="body"
                          >Điều kiện (?)</label
                        >
                        <div class="col">
                          <input
                            [(ngModel)]="should"
                            type="text"
                            class="form-control form-control-sm"
                            [ngModelOptions]="{ standalone: true }"
                            placeholder="Các văn bản có từ khóa này sẽ được hiển thị ưu tiên"
                            (keydown.enter)="dropdown.stopPropagation()"
                          />
                        </div>
                      </div>
                    </div>

                    <!-- HÀNG 6: Bắt buộc (full width) -->
                    <div class="col-xl-6 col-lg-12">
                      <div class="form-group row no-gutters align-items-center">
                        <label
                          class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                          ngbTooltip="Nhập các từ văn bản nhất định phải có. Chỉ những văn bản chứa đầy đủ các từ này mới được hiển thị trong kết quả tìm kiếm."
                          container="body"
                          >Bắt buộc (?)</label
                        >
                        <div class="col">
                          <input
                            [(ngModel)]="must"
                            type="text"
                            class="form-control form-control-sm"
                            [ngModelOptions]="{ standalone: true }"
                            placeholder="Các văn bản không từ khóa này sẽ không hiển thị"
                            (keydown.enter)="dropdown.stopPropagation()"
                          />
                        </div>
                      </div>
                    </div>

                    <!-- HÀNG 7: Không (full width) -->
                    <div class="col-xl-6 col-lg-12">
                      <div class="form-group row no-gutters align-items-center">
                        <label
                          class="label col-xxl-4 col-xl-5 col-form-label pr-2"
                          ngbTooltip="Nhập các từ bạn muốn loại bỏ. Nếu văn bản có chứa những từ này thì sẽ bị loại khỏi kết quả tìm kiếm."
                          container="body"
                          >Không (?)</label
                        >
                        <div class="col">
                          <input
                            [(ngModel)]="not"
                            type="text"
                            class="form-control form-control-sm"
                            [ngModelOptions]="{ standalone: true }"
                            placeholder="Các văn bản có từ khóa này sẽ không hiển thị"
                            (keydown.enter)="dropdown.stopPropagation()"
                          />
                        </div>
                      </div>
                    </div>

                    <!-- Nếu cần, thêm Số lượng ở cuối -->

                    <!-- <div class="col-xl-6 col-lg-12">
                      <div class="form-group row no-gutters align-items-center">
                        <label
                          class="label col-xxl-4 col-xl-5 col-form-label pr-2 col-form-label-sm"
                          >Số hiệu</label
                        >
                        <div class="col">
                          <input
                            [(ngModel)]="so_hieu"
                            type="text"
                            class="form-control form-control-sm"
                            [ngModelOptions]="{ standalone: true }"
                            placeholder="Nhập"
                            (keydown.enter)="dropdown.stopPropagation()"
                          />
                        </div>
                      </div>
                    </div> -->
                </div>
              </ng-container>
              <hr />
            </div>
          </form>
        </div>
      </div>

      <div *ngIf="paginatedData?.length > 0 && !isSearching" class="flex-shrink-0">
        <div
          class="col-12 d-flex align-items-center justify-content-between mb-50"
        >
          <div>
            <h5 class="font-weight-bolder m-0">
              Kết quả tìm kiếm:
              {{
                !(search_legal_term === SLT.DieuKhoan)
                  ? totalItem + " văn bản"
                  : totalItem +
                    " điều khoản thuộc " +
                    totalDieuKhoan +
                    " văn bản"
              }}
            </h5>
          </div>
          <div ngbDropdown class="btn-group mt-50">
            <button
              ngbDropdownToggle
              type="button"
              class="btn btn-outline-secondary btn-sm"
              rippleEffect
            >
              {{ selectedSort }}
            </button>
            <div ngbDropdownMenu>
              <a
                ngbDropdownItem
                href="javascript:void(0)"
                [class.active]="selectedSort === 'Hiệu lực pháp lý'"
                (click)="selectSort('loai_van_ban')"
                >Hiệu lực pháp lý</a
              >

              <a
                ngbDropdownItem
                href="javascript:void(0)"
                [class.active]="selectedSort === 'Ngày có hiệu lực'"
                (click)="selectSort('ngay_co_hieu_luc')"
                >Ngày có hiệu lực</a
              >

              <a
                ngbDropdownItem
                href="javascript:void(0)"
                [class.active]="selectedSort === 'Tình trạng hiệu lực'"
                (click)="selectSort('hieu_luc')"
                >Tình trạng hiệu lực</a
              >
              <a
                ngbDropdownItem
                href="javascript:void(0)"
                [class.active]="selectedSort === 'Tương đồng nội dung'"
                (click)="selectSort('score')"
                >Tương đồng nội dung</a
              >
            </div>
            <span
              [ngbTooltip]="
                typeSort == 'arrow-down-circle' ? 'Giảm dần' : 'Tăng dần'
              "
              placement="left"
              class="cursor-pointer d-flex align-items-center"
              (click)="loaiSapXep()"
            >
              <img
                src="assets/images/icons/desc.svg"
                alt="desc"
                *ngIf="typeSort == 'arrow-down-circle'"
              />
              <img
                src="assets/images/icons/asc.svg"
                alt="desc"
                *ngIf="typeSort != 'arrow-down-circle'"
              />
            </span>
            <span
              [ngbTooltip]="
                isShowTable ? 'Hiển thị dạng danh sách' : 'Hiển thị dạng bảng'
              "
              placement="top"
              class="d-flex align-items-center cursor-pointer show-button-toggle mr-xxl-50 mr-lg-0"
              (click)="isShowTable = !isShowTable"
            >
              <img
                src="assets/images/icons/show-table.svg"
                alt="show-table"
                *ngIf="!isShowTable"
              />
              <img
                src="assets/images/icons/show-table-active.svg"
                alt="show-table-active"
                *ngIf="isShowTable"
              />
              <span class="show-button-label">{{
                isShowTable ? "Danh sách" : "Dạng bảng"
              }}</span>
            </span>
            <span
              ngbTooltip="Xuất Excel"
              placement="top"
              class="d-flex align-items-center cursor-pointer show-button-toggle"
              (click)="export()"
              [class.disabled]="isExporting"
            >
              <span class="spinner-border spinner-border-sm mr-2 ml-2" role="status" *ngIf="isExporting">
                <span class="sr-only">Loading...</span>
              </span>
              <img
                src="assets/images/icons/export.svg"
                alt="export"
                *ngIf="dataTimKiemThongThuong.length > 0 && !isExporting"
              />
              <span class="show-button-label" *ngIf="!isExporting">Xuất Excel</span>
            </span>
          </div>
        </div>

        <ng-container *ngFor="let item of statistic; let last = last">
          <span
            class="spacing"
            [ngClass]="{ active: currentLabelFilter === item.label }"
            (click)="filterResult(item.label)"
          >
            {{ item.label }} ({{ item.count }})
          </span>
        </ng-container>
      </div>
      <hr />
      <ng-container *ngIf="isShowTable && !isSearching">
        <div class="folder h-100">
          <ngx-datatable
            #tableRowDetails
            [rows]="dataTimKiemThongThuongFilter"
            [rowHeight]="58"
            class="bootstrap core-bootstrap cursor"
            [columnMode]="ColumnMode.force"
            [headerHeight]="40"
            [footerHeight]="50"
            [scrollbarH]="true"
            [limit]="limitTable"
            (activate)="onActivate($event)"
            [count]="filteredCount"
            [autoSizeColumn]="true"
            [externalPaging]="true"
            (page)="changePage($event)"
            [offset]="page - 1"
          >
            <ngx-datatable-row-detail [rowHeight]="'100%'">
              <ng-template
                let-row="row"
                let-expanded="expanded"
                ngx-datatable-row-detail-template
              >
                <div>
                  <div class="ml-75 pl-5 pt-75 text-wrap-pretty">
                    <div>
                      <span
                        ><strong>{{ "Details" | translate }}: </strong>
                        <ul>
                          <li>
                            {{ "OfficialNumber" | translate }}:
                            {{ row.so_hieu }}
                          </li>
                          <li>
                            {{ "IssuingBody" | translate }}:
                            {{ row.co_quan_ban_hanh }}
                          </li>
                          <li>
                            Tên văn bản:
                            <span [innerHTML]="row.trich_yeu"></span>
                          </li>
                        </ul>
                      </span>
                    </div>
                  </div>
                </div>
              </ng-template>
            </ngx-datatable-row-detail>
            <ngx-datatable-column
              [width]="50"
              [resizeable]="false"
              [sortable]="false"
              [draggable]="false"
              [canAutoResize]="false"
            >
              <ng-template
                let-row="row"
                let-expanded="expanded"
                ngx-datatable-cell-template
              >
                <a
                  href="javascript:void(0)"
                  [class.datatable-icon-right]="!expanded"
                  [class.datatable-icon-down]="expanded"
                  title="Expand/Collapse Row"
                  (click)="rowDetailsToggleExpand(row)"
                >
                </a>
              </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column
              name="Loại văn bản"
              prop="loai_van_ban"
              [width]="150"
            >
            </ngx-datatable-column>

            <ngx-datatable-column
              name="Trích yếu"
              prop="trich_yeu"
              [width]="700"
            >
            </ngx-datatable-column>
            <ngx-datatable-column
              name="Số ký hiệu"
              prop="so_hieu"
              [width]="200"
            >
            </ngx-datatable-column>
            <ngx-datatable-column
              name="Cơ quan ban hành"
              prop="co_quan_ban_hanh"
              [width]="300"
            >
            </ngx-datatable-column>
            <ngx-datatable-column
              *ngIf="ngay_ban_hanh"
              name="Ngày ban hành"
              prop="ngay_ban_hanh"
              [width]="150"
            >
              <ng-template
                let-status="value"
                let-row="row"
                ngx-datatable-cell-template
              >
                {{ row.ngay_ban_hanh | date : "dd-MM-yyyy" : "+0000" }}
              </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column
              name="Ngày có hiệu lực"
              prop="ngay_co_hieu_luc"
              [width]="190"
            >
              <ng-template
                let-value="value"
                let-row="row"
                ngx-datatable-cell-template
              >
                {{ value | date : "dd-MM-yyyy" : "+0000" }}
              </ng-template>
            </ngx-datatable-column>
            <ngx-datatable-column
              name="Trạng thái hiệu lực"
              prop="tinh_trang_hieu_luc"
              [width]="200"
            >
              <ng-template
                let-status="value"
                let-row="row"
                ngx-datatable-cell-template
              >
                <div
                  [ngClass]="[
                    'badge',
                    status === 'Còn hiệu lực'
                      ? 'badge-light-success'
                      : status === 'Hết hiệu lực một phần'
                      ? 'badge-light-warning'
                      : status === 'Hết hiệu lực toàn bộ'
                      ? 'badge-light-danger'
                      : status === 'Ngưng hiệu lực một phần'
                      ? 'badge-light-secondary'
                      : status === 'Không còn phù hợp'
                      ? 'badge-light-muted'
                      : status === 'Ngưng hiệu lực'
                      ? 'badge-light-muted'
                      : status === 'Chưa xác định'
                      ? 'badge-light-muted'
                      : 'badge-light-info'
                  ]"
                >
                  {{ status }}
                </div>
              </ng-template>
            </ngx-datatable-column>
          </ngx-datatable>
        </div>
      </ng-container>
      <ng-container *ngIf="!isShowTable && !isSearching">
        <div
          class="folder px-1 px-xl-0 d-flex flex-column flex-fill h-100"
          *ngIf="dataTimKiemThongThuongFilter?.length > 0"
        >
          <!-- kết quả tìm kiếm văn bản -->
          <ng-container
            *ngIf="
              !(search_legal_term === SLT.DieuKhoan) &&
              dataTimKiemThongThuongFilter?.length > 0
            "
          >
            <div class="virtual-scroll-viewport flex-fill">
              <ng-container
                *ngFor="
                  let item of dataTimKiemThongThuongFilter;
                  let i = index
                "
              >
                <div
                  class="d-flex align-items-center p-xxl-1 p-xl-50 folder-item"
                  (click)="viewDetailFile('Toàn văn', item)"
                >
                  <div class="align-self-start">
                    <span class="d-flex align-items-center">
                      <div
                        class="custom-control custom-checkbox"
                        *ngIf="type != FormType.Search"
                      >
                        <input
                          type="checkbox"
                          class="custom-control-input checkboxSelectedFile"
                          [id]="'customCheck_' + i"
                          (change)="onCheckboxChange(item, $event)"
                          (click)="$event.stopPropagation()"
                          [checked]="isChecked(item.id)"
                        />
                        <label
                          class="custom-control-label"
                          [for]="'customCheck_' + i"
                          (click)="$event.stopPropagation()"
                        ></label>
                      </div>
                      <b class="m-0">
                        <span *ngIf="i + (page - 1) * 20 + 1 < 9">0</span
                        >{{ i + (page - 1) * 20 + 1 }}.&nbsp;
                      </b>
                    </span>
                  </div>
                  <div class="w-90">
                    <div
                      class="d-flex align-items-center w-100 overflow-hidden font-weight-bolder"
                      [ngbTooltip]="item.trich_yeu | stripHtml"
                      container="body"
                      [openDelay]="300"
                    >
                      <!-- Loại văn bản -->
                      <span class="text-primary mr-50 flex-shrink-0">
                        {{ item.loai_van_ban }}
                      </span>

                      <!-- Số hiệu -->
                      <span class="mr-50 flex-shrink-0">
                        - {{ item.so_hieu }} -
                      </span>

                      <!-- Trích yếu -->
                      <span
                        class="text-truncate mr-50"
                        [innerHTML]="item.trich_yeu"
                        [attr.id]="'msg-' + i"
                      ></span>

                      <!-- Icon sao chép -->
                      <span
                        appcopy
                        [text]="
                          item.loai_van_ban +
                            ' - ' +
                            item.so_hieu +
                            ' - ' +
                            item.trich_yeu | stripHtml
                        "
                        class="text-primary cursor-pointer ml-1 flex-shrink-0 copy-align-right"
                        ngbTooltip="Sao chép"
                        container="body"
                      >
                        <i data-feather="copy"></i>
                      </span>
                    </div>

                    <i
                      appExpandableText
                      *ngIf="item.toan_van != ''"
                      [text]="item?.toan_van"
                    >
                    </i>

                    <div class="d-flex mt-xxl-1 mt-xl-50">
                      <p *ngIf="item.tinh_trang_hieu_luc" class="m-0 mr-50">
                        Hiệu lực:
                        <span
                          class="font-weight-bolder"
                          [ngClass]="{
                            'text-danger':
                              item.tinh_trang_hieu_luc ===
                              'Hết hiệu lực toàn bộ',
                            'text-success':
                              item.tinh_trang_hieu_luc === 'Còn hiệu lực',
                            'text-warning':
                              item.tinh_trang_hieu_luc ===
                              'Hết hiệu lực một phần',
                            'text-primary':
                              item.tinh_trang_hieu_luc === 'Chưa có hiệu lực',
                            'text-secondary':
                              item.tinh_trang_hieu_luc ===
                              'Ngưng hiệu lực một phần',
                            'text-muted':
                              item.tinh_trang_hieu_luc === 'Không còn phù hợp'
                          }"
                        >
                          {{ item.tinh_trang_hieu_luc }}
                        </span>
                      </p>
                      <p *ngIf="item.ngay_ban_hanh" class="m-0 mr-50">
                        Ban hành:
                        <span class="font-weight-bolder">
                          {{
                            item.ngay_ban_hanh | date : "dd-MM-yyyy" : "+0000"
                          }}
                        </span>
                      </p>
                      <p *ngIf="item.ngay_co_hieu_luc" class="m-0 mr-50">
                        Áp dụng:
                        <span class="font-weight-bolder">
                          {{
                            item.ngay_co_hieu_luc
                              | date : "dd-MM-yyyy" : "+0000"
                          }}
                        </span>
                      </p>
                    </div>
                    <div>
                      <span
                        (click)="
                          $event.stopPropagation(); viewDetailFile(action, item)
                        "
                        class="cursor-pointer action-files"
                        *ngFor="let action of actionFile; let j = index"
                        (mouseenter)="hoveredIndex[i] = j"
                        (mouseleave)="hoveredIndex[i] = null"
                        [ngClass]="{ 'text-primary': hoveredIndex[i] === j }"
                      >
                        {{ action }}
                      </span>
                    </div>
                  </div>
                </div>
                <hr />
              </ng-container>
            </div>
          </ng-container>
          <!-- kết quả tìm kiếm văn bản -->

          <!-- kết quả tìm kiếm điều khoản -->

          <ng-container
            *ngIf="
              search_legal_term === SLT.DieuKhoan &&
              dataTimKiemThongThuongFilter?.length > 0
            "
          >
            <div class="virtual-scroll-viewport flex-fill">
              <ng-container
                *ngFor="let item of dataTimKiemThongThuongFilter; let i = index"
              >
                <div
                  class="d-flex align-items-center p-xxl-1 p-xl-50 folder-item"
                  (click)="viewDetailFileDieuKhoan(item)"
                >
                  <div class="align-self-start">
                    <span class="d-flex align-items-center">
                      <div
                        class="custom-control custom-checkbox"
                        *ngIf="type != FormType.Search"
                      >
                        <input
                          type="checkbox"
                          class="custom-control-input checkboxSelectedFile"
                          [id]="'customCheck_' + i"
                          (change)="onCheckboxChange(item, $event)"
                          (click)="$event.stopPropagation()"
                          [checked]="isChecked(item.id)"
                        />
                        <label
                          class="custom-control-label"
                          [for]="'customCheck_' + i"
                          (click)="$event.stopPropagation()"
                        ></label>
                      </div>
                      <b class="m-0">
                        <span *ngIf="i + (page - 1) * 20 + 1 < 9">0</span
                        >{{ i + (page - 1) * 20 + 1 }}.&nbsp;
                      </b>
                    </span>
                  </div>
                  <div class="w-90">
                    <div class="wrap-text">
                      <div
                        class="d-flex align-items-center w-100 overflow-hidden font-weight-bolder"
                        [ngbTooltip]="item.trich_yeu | stripHtml"
                        container="body"
                        [openDelay]="300"
                      >
                        <!-- Loại văn bản -->
                        <span class="text-primary flex-shrink-0">
                          {{ item.loai_van_ban }}
                        </span>

                        <!-- Số hiệu -->
                        <span class="flex-shrink-0">
                          - {{ item.so_hieu }} -
                        </span>

                        <!-- Trích yếu -->
                        <span
                          class="text-truncate"
                          [innerHTML]="item.trich_yeu"
                          [attr.id]="'msg2-' + i"
                        ></span>

                        <!-- Icon sao chép -->
                        <span
                          appcopy
                          [text]="
                            item.loai_van_ban +
                              ' - ' +
                              item.so_hieu +
                              ' - ' +
                              item.trich_yeu | stripHtml
                          "
                          class="text-primary cursor-pointer ml-1 flex-shrink-0 copy-align-right"
                          ngbTooltip="Sao chép"
                          container="body"
                        >
                          <i data-feather="copy"></i>
                        </span>
                      </div>

                      <!-- để phân tách ra cho đều khoảng cách nếu không có nội dung -->

                      <div class="d-flex">
                        <p *ngIf="item.tinh_trang_hieu_luc" class="m-0">
                          Hiệu lực:
                          <span
                            class="font-weight-bolder"
                            [ngClass]="{
                              'text-danger':
                                item.tinh_trang_hieu_luc ===
                                'Hết hiệu lực toàn bộ',
                              'text-success':
                                item.tinh_trang_hieu_luc === 'Còn hiệu lực',
                              'text-warning':
                                item.tinh_trang_hieu_luc ===
                                'Hết hiệu lực một phần',
                              'text-primary':
                                item.tinh_trang_hieu_luc === 'Chưa có hiệu lực',
                              'text-secondary':
                                item.tinh_trang_hieu_luc ===
                                'Ngưng hiệu lực một phần',
                              'text-muted':
                                item.tinh_trang_hieu_luc === 'Không còn phù hợp'
                            }"
                          >
                            {{ item.tinh_trang_hieu_luc }}
                          </span>
                        </p>
                        <p *ngIf="item.ngay_ban_hanh" class="m-0">
                          Ban hành:
                          <span class="font-weight-bolder">
                            {{
                              item.ngay_ban_hanh | date : "dd-MM-yyyy" : "+0000"
                            }}
                          </span>
                        </p>
                        <p *ngIf="item.ngay_co_hieu_luc" class="m-0">
                          Áp dụng:
                          <span class="font-weight-bolder">
                            {{
                              item.ngay_co_hieu_luc
                                | date : "dd-MM-yyyy" : "+0000"
                            }}
                          </span>
                        </p>
                      </div>
                      <div>
                        <span
                          (click)="
                            $event.stopPropagation();
                            viewDetailFile(action, item)
                          "
                          class="cursor-pointer action-files"
                          *ngFor="let action of actionFile; let j = index"
                          (mouseenter)="hoveredIndex[i] = j"
                          (mouseleave)="hoveredIndex[i] = null"
                          [ngClass]="{ 'text-primary': hoveredIndex[i] === j }"
                        >
                          {{ action }}
                        </span>
                      </div>

                      <div
                        class="text-primary"
                        (click)="$event.stopPropagation(); showTermClause(i)"
                      >
                        {{ "Điều khoản" + "(" + item.terms?.length + ")" }}
                        <img
                          [src]="
                            termStates[i]
                              ? 'assets/images/icons/up-blue.svg'
                              : 'assets/images/icons/down-blue.svg'
                          "
                          alt="chevron"
                        />
                      </div>
                      <ng-container *ngIf="termStates[i]">
                        <ng-container
                          *ngFor="let term of item.terms; let last = last"
                        >
                          <div class="collapse-icon" id="detail-term">
                            <ngb-accordion
                              #acc="ngbAccordion"
                              (click)="$event.stopPropagation()"
                            >
                              <ngb-panel>
                                <ng-template ngbPanelTitle>
                                  <span
                                    class="lead w-90 collapse-title card-title d-flex align-items-center font-sm"
                                    ><p
                                      class="m-0 one-line"
                                      [ngbTooltip]="term.term_title"
                                      container="body"
                                    >
                                      <b>{{ term.position }} </b>
                                      - {{ term.term_title }}
                                    </p>
                                    <span
                                      appcopy
                                      class="text-primary cursor-pointer mr-1 flex-shrink-0 copy-align-right"
                                      [text]="
                                        term.position + ' - ' + term.term_title
                                      "
                                      ngbTooltip="Sao chép"
                                      container="body"
                                    >
                                      <i data-feather="copy"></i>
                                    </span>
                                  </span>
                                </ng-template>
                                <ng-template ngbPanelContent>
                                  <p
                                    (click)="
                                      viewDetailFileDieuKhoan(item, term);
                                      $event.stopPropagation()
                                    "
                                    appExpandableText
                                    [isShowFull]="true"
                                    [text]="
                                      term.raw_content
                                        ? term.raw_content
                                        : term.term_content
                                    "
                                  ></p>
                                </ng-template>
                              </ngb-panel>
                            </ngb-accordion>
                          </div>
                          <hr *ngIf="!last" />
                        </ng-container>
                      </ng-container>
                    </div>
                  </div>
                </div>
                <hr />
              </ng-container>
            </div>
          </ng-container>
          <!-- tìm kiếm điều khoản -->
          <span
            class="w-100 d-flex justify-content-center mt-auto"
            *ngIf="is_paging && totalItem > 20"
          >
            <ngb-pagination
              [maxSize]="3"
              [collectionSize]="totalItem"
              [(page)]="page"
              aria-label="Default pagination"
              [rotate]="true"
              [ellipses]="false"
              pageSize="20"
              [boundaryLinks]="false"
              (pageChange)="onPageChange($event)"
            >
            </ngb-pagination>
          </span>
        </div>
        <ng-container *ngIf="noData && !isSearching">
          <div class="mt-5">
            <app-no-data></app-no-data>
          </div>
        </ng-container>
      </ng-container>

      <div class="search-loading d-flex justify-content-center mt-5" *ngIf="isSearching">
        <span class="spinner-border text-primary position-absolute" role="status">
          <span class="sr-only">Loading...</span>
        </span>
      </div>

      <div class="save-file" *ngIf="selectedFiles.length > 0">
        <div
          class="selection-bar p-1 d-flex align-items-center justify-content-between"
        >
          <!-- Nội dung -->
          <span class="selected-text"
            >Đã chọn {{ selectedFiles.length }} tài liệu</span
          >
          <span class="height-30px mx-1"></span>
          <!-- Nút lưu tài liệu -->
          <button
            class="save-btn d-flex align-items-center"
            (click)="saveHistoryFiles()"
          >
            <img src="assets/images/icons/folder-star.svg" alt="folder-star" />
            <span class="ms-2">Lưu tài liệu</span>
          </button>
        </div>
      </div>
    </div>
    <div
      [ngClass]="isShowFastSearch ? 'active' : 'hidden'"
      class="col-3 fast-search p-xxl-1 p-lg-50 pl-2 collapse-icon"
      id="fast-search"
    >
      <div class="d-flex align-items-center">
        <p class="mb-1 font-weight-bolder h4">Danh sách tra cứu</p>
        <p
          class="mb-0 ml-auto text-decoration-underline cursor-pointer"
          (click)="clearAllFastSearch()"
        >
          Xoá hết
        </p>
      </div>
      <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-0">
        <ngb-panel id="ngb-panel-0">
          <ng-template ngbPanelTitle>
            <span class="lead collapse-title font-weight-bolder p-0">
              Lĩnh vực
            </span>
          </ng-template>
          <ng-template ngbPanelContent>
            <ng-container *ngIf="isFetchingLinhVuc">
              <div class="loading-linhvuc col-12 d-flex justify-content-center align-items-center mb-1">
                <span
                  class="spinner-border spinner-border-sm text-primary position-absolute"
                  role="status"
                >
                  <span class="sr-only">Loading...</span>
                </span>
              </div>
            </ng-container>
            <ng-container *ngIf="!isFetchingLinhVuc">
              <ng-container
                *ngFor="let item of listDanhSachLinhVuc; let i = index"
              >
                <div class="custom-control custom-checkbox mb-50">
                  <input
                    type="checkbox"
                    class="custom-control-input"
                    [id]="'linhvuc' + i"
                    [(ngModel)]="item.checked"
                    (change)="onCheckboxChangeLinhVuc(item)"
                  />
                  <label class="custom-control-label" [for]="'linhvuc' + i"
                    >{{ item.linh_vuc }} ({{
                      item.so_luong | number : "1.0-2"
                    }})</label
                  >
                </div>
              </ng-container>
              <ng-container *ngIf="!isFetchingLinhVuc && listDanhSachLinhVuc.length == 0">
                <div class="col-12 d-flex justify-content-center align-items-center mb-1">
                  <span class="text-muted">Không có dữ liệu lĩnh vực</span>
                </div>
              </ng-container>
            </ng-container>
          </ng-template>
        </ngb-panel>
      </ngb-accordion>
      <hr />
      <div class="mb-1"></div>
      <ngb-accordion #acc="ngbAccordion" activeIds="ngb-panel-1">
        <ngb-panel id="ngb-panel-1">
          <ng-template ngbPanelTitle>
            <span class="lead collapse-title font-weight-bolder p-0">
              Loại văn bản
            </span>
          </ng-template>
          <ng-template ngbPanelContent>
            <ng-container *ngFor="let item of listThongKeVanBan; let i = index">
              <div class="custom-control custom-checkbox mb-50">
                <input
                  type="checkbox"
                  class="custom-control-input"
                  [id]="'loaivanban' + i"
                  [(ngModel)]="item.checked"
                  (change)="onCheckboxChangeLoaiVanBan(item)"
                />
                <label class="custom-control-label" [for]="'loaivanban' + i"
                  >{{ item.key }} ({{
                    item.doc_count | number : "1.0-2"
                  }})</label
                >
              </div>
            </ng-container>
          </ng-template>
        </ngb-panel>
      </ngb-accordion>
    </div>
  </div>
  <span
    *ngIf="type == FormType.Search"
    class="x-button"
    (click)="modal.dismiss('Cross click')"
  >
    <img src="assets/images/icons/x.svg" alt="x" />
  </span>
</core-card>
<ng-template #viewFileModal let-modal>
  <app-view-detail-file [modal]="modal" [type]="types"></app-view-detail-file>
</ng-template>
