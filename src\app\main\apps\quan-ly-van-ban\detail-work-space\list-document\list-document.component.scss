@import "@core/scss/angular/libs/drag-drop.component.scss";
@import "@core/scss/angular/libs/select.component.scss";
.file-container {
  height: 92vh;
  display: flex;
  flex-direction: column;
}
.header-document {
  flex-shrink: 0;
}
.search-work-space {
  flex-shrink: 0;
}

.showFile .accordion .card {
  background-color: rgba(235, 244, 255, 1) !important;
}
.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .review-title {
    font-weight: bold;
    font-size: 14px;
  }

  .review-actions {
    display: flex;
    gap: 10px;
    justify-content: end;
  }
}

.review-list {
  max-height: 400px;
  overflow-y: auto;
}
.review-item:hover {
  background-color: #007bff;
  color: #fff;
}

.review-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px;
  padding: 14px 8px;

  &.item-selected {
    background-color: #007bff;
    color: #fff !important;
  }

  &.item-error .btn-error {
    background-color: #f8d7da;
    color: #721c24;
  }

  &.item-success .btn-success {
    background-color: #d4edda;
    color: #155724;
  }

  .item-title {
    max-width: 85%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .item-status {
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 70px;
    justify-content: end;
  }
}

.review-item-2:hover {
  background-color: #007bff;
  color: #fff;
}

.review-item-2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 8px;
  padding: 14px 8px;

  &.item-selected {
    background-color: #007bff;
    color: #fff !important;
  }

  &.item-error .btn-error {
    background-color: #f8d7da;
    color: #721c24;
  }

  &.item-success .btn-success {
    background-color: #d4edda;
    color: #155724;
  }

  .item-title-2 {
    max-width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .item-status {
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 70px;
    justify-content: end;
  }
}
.list-document {
  overflow: auto;
  position: relative;
  overflow-x: hidden;
  flex: 1;
}
.paginate-document {
  // position: absolute;
  // bottom: 0;
  flex-shrink: 0;
}
@media (max-width: 992px) {
  .paginate-document {
    position:absolute;
    bottom:50px;
  }
}
.swal-cancel-info {
  border: 2px solid #3085d6 !important;
  color: #3085d6 !important;
  background-color: white !important;
  padding: 8px 20px !important;
  border-radius: 5px !important;
  transition: all 0.3s;
}

.swal-cancel-info:hover {
  background-image: linear-gradient(
    rgba(0, 0, 0, 0),
    rgba(0, 0, 0, 0)
  ) !important;
  box-shadow: 0 8px 16px rgba(48, 133, 214, 0.4) !important;
  transform: translateY(-3px);
}

.swal-confirm-danger {
  transition: all 0.3s;
}

.swal-confirm-danger:hover {
  box-shadow: 0 8px 16px rgba(211, 51, 51, 0.4) !important;
  transform: translateY(-3px);
}
.cdk-virtual-scroll-content-wrapper {
  position: relative !important;
}
// .document-card-wrapper,
.document-card {
  padding: 16px 8px;
  position: relative;
  overflow: visible !important;
  z-index: auto;
  font-size: 13px !important;
}


.document-card:hover {
  background-color: #ebf4ff;
}
/* Small Laptop: 1024px – 1279px */

@media (min-width: 1024px) and (max-width: 1440px) {
  // .list-document {
  //   height: 66vh;
  // }
  .review-item {
    padding: 8px;
    .item-title {
      max-width: 80%;
    }
  }
  .review-item-2 {
    padding: 8px;
    .item-title {
      max-width: 100%;
    }
  }
  .document-card {
    padding: 8px;
  }
}

/* Laptop lớn hoặc màn hình ngoài */
@media (min-width: 1441px) {
  // .list-document {
  //   height: 72vh;
  // }
}

/* Từ tablet cho đến laptop */
@media (min-width: 1025px) and (max-width: 1440px) {
  // .list-document {
  //   height: 65vh;
  // }
}

/* Tablet */
@media (max-width: 1024px) {
  // .list-document {
  //   height: 61vh;
  // }
}
.circular-progress {
  --size: 250px;
  --half-size: calc(var(--size) / 2);
  --stroke-width: 20px;
  --radius: calc((var(--size) - var(--stroke-width)) / 2);
  --circumference: calc(var(--radius) * pi * 2);
  --dash: calc((var(--progress) * var(--circumference)) / 100);
  animation: progress-animation 5s linear 0s 1 forwards;
}

.circular-progress circle {
  cx: var(--half-size);
  cy: var(--half-size);
  r: var(--radius);
  stroke-width: var(--stroke-width);
  fill: none;
  stroke-linecap: round;
}

.circular-progress circle.bg {
  stroke: #ddd;
}

.circular-progress circle.fg {
  transform: rotate(-90deg);
  transform-origin: var(--half-size) var(--half-size);
  stroke-dasharray: var(--dash) calc(var(--circumference) - var(--dash));
  transition: stroke-dasharray 0.3s linear 0s;
  stroke: #5394fd;
}

@property --progress {
  syntax: "<number>";
  inherits: false;
  initial-value: 0;
}

@keyframes progress-animation {
  from {
    --progress: 0;
  }
  to {
    --progress: 100;
  }
}
.context-menu {
  position: fixed;
  background: #fff;
  z-index: 9999;
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.23);
  border-radius: 8px;
  padding: 4px 0;
}

.context-menu ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.context-menu li {
  padding: 8px 12px;
  cursor: pointer;
}

.context-menu li:hover {
  background-color: #f0f0f0;
}
.pagination {
  margin: 0;
}
.progress-wrapper {
  width: 50px;
}
.action-file {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  min-width: 36px;
  cursor: pointer;
  background-color: #fff;
  z-index: 999;
  width: 25px !important;
  padding-right: 0px;
}
.action-file-2 {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  min-width: 60px;
  cursor: pointer;
  background-color: #fff;
  z-index: 999;
  width: 25px !important;
  padding-right: 0px;
}
// .action-file:hover {
//   background-color: #ecf4fe;
// }

.document-card:hover .action-file {
  background-color: #ecf4fe;
}
.document-card:hover .action-file-2 {
  background-color: #ecf4fe;
}
.accordion .card .card-body {
  cursor: pointer;
}
.p-chips .p-chips-multiple-container .p-chips-token {
  color: #008fe3 !important;
}
.border-red {
  border-left: 2px solid #dc3545;
}
.border-yellow {
  border-left: 2px solid #ffc107;
}
.overlay-in-modal {
  z-index: 1000000 !important;

  .p-overlaypanel-content {
    padding: 0; 
  }

  .p-listbox {
    width: 100% !important; 
    max-height: 260px;
  }
}

.btn-custom{
  width: 38px !important;
  height: 38px !important;
}

.mr-custom-1{
  margin-right: 4px !important;
}


#file1 .ng-select-container {
  font-size: 12px !important;

}

.wid-260{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
@media (max-width: 1199.98px) {
  .wid-260 {
    width: 260px !important;
    
  }
}
@media (min-width: 1200px) 
  {
   .wid-260 {
    width: calc(90% - 36px);
  }
}

.wid-270{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
@media (max-width: 1199.98px) {
  .wid-270 {
    width: 260px !important;
    
  }
}
@media (min-width: 1200px) 
  {
   .wid-270 {
    width: calc(90% - 60px);
  }
}
// @media (min-width: 1200px) and (max-width: 1399.98px)
//   {
//   .wid-260 {
//     width: 60%;
//   }
// }
// @media (min-width: 1400px) and (max-width: 1699.98px)  {
//   .wid-260 {
//     width: 70%;
//   }
// }
// @media (min-width: 1700px)  {
//   .wid-260 {
//     width: 85%;

//   }
// }
.wid-280 {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@media (max-width: 1199.98px) {
  .wid-280 {
    width: 290px !important;
  }
}

@media (min-width: 1200px)  {
  .wid-280 {
    width: 90%;
  }
}

// @media (min-width: 1400px) and (max-width: 1699.98px) {
//   .wid-280 {
//     width: 85%;
//   }
// }

// @media (min-width: 1700px) {
//   .wid-280 {
//     width: 90%;
//   }
// }
.document-card {
  display: flex;
  align-items: flex-start;
}
.daterange-control [data-input] {
  display: none !important;
}

.document-card .icon {
  width: 24px; 
  height: auto;
  flex-shrink: 0;
  margin-right: 8px; 
}

.document-info {
  flex: 1;
  min-width: 0; 
  display: flex;
  flex-direction: column;
}

.document-title {
  display: flex;
  align-items: center;
  min-width: 0; 
}


.daterange-control {
  position: relative;
  display: block;       
}

.daterange-input.form-control {
  padding-right: 2.25rem;  /* ~36px */
}

.daterange-clear {
  position: absolute;
  right: .5rem;
  top: 50%;
  transform: translateY(-50%);
  padding: 0;
  line-height: 1;
  font-size: 1.25rem; 
  color: #9aa0a6; 
  text-decoration: none;
}

.daterange-clear:hover,
.daterange-clear:focus {
  color: #6b7280;  
  text-decoration: none;
}

.daterange-input:disabled ~ .daterange-clear,
.daterange-input[readonly] ~ .daterange-clear {
  opacity: .6;
  pointer-events: none;
}

.btn-spn-uploading {
  max-height: 36px;
}
