<div class="config-form-container">
  <div class="card-body graph-form-body">
    <form class="form form-vertical">
      <div class="row">
        <!-- Top section: Checkbox, Search and Button -->
        <div
          class="col-12 d-flex align-items-center justify-content-between mt-2"
        >
          <div class="d-flex align-items-center justify-content-between w-100">
            <div class="custom-control custom-checkbox">
              <input
                type="checkbox"
                class="custom-control-input"
                id="viewGraphWithTerms"
                name="viewGraphWithTerms"
                [(ngModel)]="formState.viewGraphWithTerms"
                (change)="onViewGraphWithTermsChange($event)"
              />
              <label class="custom-control-label" for="viewGraphWithTerms">
                Xem đồ thị với điều khoản
              </label>
            </div>

            <div class="graph-search-container">
              <ng-select
                class="ng-select-size-sm fixed-height-select"
                multiple="true"
                [items]="timKiemDieuKhoanOptions"
                bindLabel="label"
                bindValue="value"
                placeholder="Tìm kiếm điều khoản"
                [clearable]="true"
                [disabled]="!formState.viewGraphWithTerms"
                [closeOnSelect]="true"
                [(ngModel)]="formState.selectedTimKiemDieuKhoan"
                [ngModelOptions]="{ standalone: true }"
              >
                <ng-template ng-option-tmp let-item="item">
                  <div>{{ item.label }}</div>
                </ng-template>
                <ng-template
                  ng-multi-label-tmp
                  let-items="items"
                  let-clear="clear"
                >
                  <div
                    class="ng-value"
                    *ngFor="let item of items | slice : 0 : 2"
                  >
                    <span class="ng-value-label">{{ item.label }}</span>
                    <span
                      class="ng-value-icon right"
                      (click)="clear(item)"
                      aria-hidden="true"
                      >×</span
                    >
                  </div>
                  <div class="ng-value" *ngIf="items.length > 2">
                    <span class="ng-value-label"
                      >{{ items.length - 2 }} More</span
                    >
                    <span
                      class="ng-value-icon right"
                      (click)="clearAllRemainingItems(items, clear)"
                      aria-hidden="true"
                      >×</span
                    >
                  </div>
                </ng-template>
              </ng-select>
            </div>
          </div>
        </div>

        <!-- Tab buttons -->
        <div class="col-12 mt-1 mb-1">
          <div
            class="col-12 p-0 d-flex align-items-center justify-content-between"
          >
            <div
              class="btn-group btn-group-toggle"
              ngbRadioGroup
              name="radioBasic"
              [(ngModel)]="formState.search_legal_term"
            >
              <label
                ngbButtonLabel
                class="btn-outline-primary-theme btn-sm"
                rippleEffect
              >
                <input ngbButton type="radio" value="VAN_BAN" /> Văn bản
              </label>
              <label
                ngbButtonLabel
                class="btn-outline-primary-theme btn-sm"
                rippleEffect
              >
                <input ngbButton type="radio" value="DIEU_KHOAN" /> Điều khoản
              </label>
              <label
                ngbButtonLabel
                class="btn-outline-primary-theme btn-sm"
                rippleEffect
              >
                <input ngbButton type="radio" value="ALL" /> Văn bản & Điều
                khoản
              </label>
            </div>
            <button
              type="button"
              class="btn btn-sm ml-1"
              rippleEffect
              (click)="formState.isSearchAdvance = !formState.isSearchAdvance"
              [ngClass]="{
                'btn-primary-theme': formState.isSearchAdvance,
                'border-primary-theme': !formState.isSearchAdvance
              }"
            >
              <span class="mr-25" data-feather="chevrons-down"></span>
              Thêm điều kiện
            </button>
          </div>
        </div>

        <!-- Advanced search form -->
        <ng-container *ngIf="formState.isSearchAdvance">
          <div class="row w-100 m-0">
            <!-- Row 1: Loại mối quan hệ | Thời gian ban hành -->
            <div class="col-xl-6 col-lg-12">
              <div class="form-group row no-gutters align-items-center">
                <label class="label col-xl-6 col-xxl-4 col-form-label pr-0">
                  Loại mối quan hệ với tài liệu gốc
                </label>
                <fieldset class="form-group col-xxl-8 col-xl-6">
                  <ng-select
                    class="ng-select-size-sm fixed-height-select"
                    multiple="true"
                    [items]="boLocMoiQuanHeOptions"
                    bindLabel="label"
                    bindValue="value"
                    placeholder="Chọn mối quan hệ"
                    [clearable]="true"
                    [closeOnSelect]="true"
                    [(ngModel)]="formState.selectedBoLocMoiQuanHe"
                    [ngModelOptions]="{ standalone: true }"
                  >
                    <ng-template ng-option-tmp let-item="item">
                      <div>{{ item.label }}</div>
                    </ng-template>
                    <ng-template
                      ng-multi-label-tmp
                      let-items="items"
                      let-clear="clear"
                    >
                      <div
                        class="ng-value"
                        *ngFor="let item of items | slice : 0 : 2"
                      >
                        <span class="ng-value-label">{{ item.label }}</span>
                        <span
                          class="ng-value-icon right"
                          (click)="clear(item)"
                          aria-hidden="true"
                          >×</span
                        >
                      </div>
                      <div class="ng-value" *ngIf="items.length > 2">
                        <span class="ng-value-label"
                          >{{ items.length - 2 }} More</span
                        >
                        <span
                          class="ng-value-icon right"
                          (click)="clearAllRemainingItems(items, clear)"
                          aria-hidden="true"
                          >×</span
                        >
                      </div>
                    </ng-template>
                  </ng-select>
                </fieldset>
              </div>
            </div>

            <div class="col-xl-6 col-lg-12">
              <div class="form-group row no-gutters align-items-center">
                <label class="label col-xl-6 col-xxl-4 col-form-label pr-0">
                  Thời gian ban hành
                </label>
                <fieldset class="form-group col-xxl-8 col-xl-6">
                  <ng2-flatpickr
                    #yearPicker
                    [config]="customYearOptions"
                    name="year"
                    (change)="changeYear($event)"
                  ></ng2-flatpickr>
                </fieldset>
              </div>
            </div>

            <!-- Row 2: Số cấp liên kết | Tình trạng hiệu lực -->
            <div class="col-xl-6 col-lg-12">
              <div class="form-group row no-gutters">
                <label class="label col-xl-6 col-xxl-4 col-form-label pr-0">
                  Số cấp liên kết
                </label>
                <fieldset class="form-group col-xxl-8 col-xl-6">
                  <input
                    [(ngModel)]="formState.depth"
                    type="number"
                    class="form-control"
                    [ngModelOptions]="{ standalone: true }"
                    min="1"
                    max="3"
                    placeholder="Nhập số cấp"
                    (input)="validateDepth($event)"
                  />
                  <div
                    class="text-danger small mt-1 text-right"
                    *ngIf="formState.depthError"
                  >
                    {{ formState.depthError }}
                  </div>
                </fieldset>
              </div>
            </div>

            <div class="col-xl-6 col-lg-12">
              <div class="form-group row no-gutters align-items-center">
                <label class="label col-xl-6 col-xxl-4 col-form-label pr-0">
                  Tình trạng hiệu lực
                </label>
                <fieldset class="form-group col-xxl-8 col-xl-6">
                  <ng-select
                    class="ng-select-size-sm fixed-height-select"
                    multiple="true"
                    [items]="tinhTrangHieuLucOptions"
                    bindLabel="label"
                    bindValue="value"
                    placeholder="Chọn tình trạng"
                    [clearable]="true"
                    [closeOnSelect]="true"
                    [(ngModel)]="formState.selectedTinhTrangHieuLuc"
                    [ngModelOptions]="{ standalone: true }"
                  >
                    <ng-template ng-option-tmp let-item="item">
                      <div>{{ item.label }}</div>
                    </ng-template>
                    <ng-template
                      ng-multi-label-tmp
                      let-items="items"
                      let-clear="clear"
                    >
                      <div
                        class="ng-value"
                        *ngFor="let item of items | slice : 0 : 2"
                      >
                        <span class="ng-value-label">{{ item.label }}</span>
                        <span
                          class="ng-value-icon right"
                          (click)="clear(item)"
                          aria-hidden="true"
                          >×</span
                        >
                      </div>
                      <div class="ng-value" *ngIf="items.length > 2">
                        <span class="ng-value-label"
                          >{{ items.length - 2 }} More</span
                        >
                        <span
                          class="ng-value-icon right"
                          (click)="clearAllRemainingItems(items, clear)"
                          aria-hidden="true"
                          >×</span
                        >
                      </div>
                    </ng-template>
                  </ng-select>
                </fieldset>
              </div>
            </div>

            <!-- Row 3: Giới hạn số lượng tài liệu | Loại văn bản -->
            <div class="col-xl-6 col-lg-12">
              <div class="form-group row no-gutters">
                <label class="label col-xl-6 col-xxl-4 col-form-label pr-0">
                  Giới hạn số lượng tài liệu
                </label>
                <fieldset class="form-group col-xxl-8 col-xl-6">
                  <input
                    [(ngModel)]="formState.global_limit"
                    type="number"
                    class="form-control"
                    [ngModelOptions]="{ standalone: true }"
                    min="1"
                    max="50"
                    placeholder="Nhập số lượng"
                    (input)="validateGlobalLimit($event)"
                  />
                  <div
                    class="text-danger small mt-1 text-right"
                    *ngIf="formState.global_limitError"
                  >
                    {{ formState.global_limitError }}
                  </div>
                </fieldset>
              </div>
            </div>

            <div class="col-xl-6 col-lg-12">
              <div class="form-group row no-gutters align-items-center">
                <label class="label col-xl-6 col-xxl-4 col-form-label pr-0">
                  Loại văn bản
                </label>
                <fieldset class="form-group col-xxl-8 col-xl-6">
                  <ng-select
                    class="ng-select-size-sm fixed-height-select"
                    multiple="true"
                    [items]="boLocLoaiVanBanOptions"
                    bindLabel="label"
                    bindValue="value"
                    placeholder="Chọn loại văn bản"
                    [clearable]="true"
                    [closeOnSelect]="true"
                    [(ngModel)]="formState.selectedBoLocLoaiVanBan"
                    [ngModelOptions]="{ standalone: true }"
                  >
                    <ng-template ng-option-tmp let-item="item">
                      <div>{{ item.label }}</div>
                    </ng-template>
                    <ng-template
                      ng-multi-label-tmp
                      let-items="items"
                      let-clear="clear"
                    >
                      <div
                        class="ng-value"
                        *ngFor="let item of items | slice : 0 : 2"
                      >
                        <span class="ng-value-label">{{ item.label }}</span>
                        <span
                          class="ng-value-icon right"
                          (click)="clear(item)"
                          aria-hidden="true"
                          >×</span
                        >
                      </div>
                      <div class="ng-value" *ngIf="items.length > 2">
                        <span class="ng-value-label"
                          >{{ items.length - 2 }} More</span
                        >
                        <span
                          class="ng-value-icon right"
                          (click)="clearAllRemainingItems(items, clear)"
                          aria-hidden="true"
                          >×</span
                        >
                      </div>
                    </ng-template>
                  </ng-select>
                </fieldset>
              </div>
            </div>

            <!-- Row 4: Giới hạn số lượng tài liệu cho mỗi nhánh | Cơ quan ban hành -->
            <div class="col-xl-6 col-lg-12">
              <div class="form-group row no-gutters">
                <label class="label col-xl-6 col-xxl-4 col-form-label pr-0">
                  Giới hạn số lượng tài liệu cho mỗi nhánh
                </label>
                <fieldset class="form-group col-xxl-8 col-xl-6">
                  <input
                    [(ngModel)]="formState.limit_per_seed"
                    type="number"
                    class="form-control"
                    [ngModelOptions]="{ standalone: true }"
                    min="1"
                    max="10"
                    placeholder="Nhập số lượng"
                    (input)="validateLimitPerSeed($event)"
                  />
                  <div
                    class="text-danger small mt-1 text-right"
                    *ngIf="formState.limit_per_seedError"
                  >
                    {{ formState.limit_per_seedError }}
                  </div>
                </fieldset>
              </div>
            </div>

            <div class="col-xl-6 col-lg-12">
              <div class="form-group row no-gutters align-items-center">
                <label class="label col-xl-6 col-xxl-4 col-form-label pr-0">
                  Cơ quan ban hành
                </label>
                <fieldset class="form-group col-xxl-8 col-xl-6">
                  <ng-select
                    class="ng-select-size-sm fixed-height-select"
                    multiple="true"
                    [items]="coQuanBanHanhOptions"
                    bindLabel="label"
                    bindValue="value"
                    placeholder="Chọn cơ quan ban hành"
                    [clearable]="true"
                    [closeOnSelect]="true"
                    [(ngModel)]="formState.selectedCoQuanBanHanh"
                    [ngModelOptions]="{ standalone: true }"
                  >
                    <ng-template ng-option-tmp let-item="item">
                      <div>{{ item.label }}</div>
                    </ng-template>
                    <ng-template
                      ng-multi-label-tmp
                      let-items="items"
                      let-clear="clear"
                    >
                      <div
                        class="ng-value"
                        *ngFor="let item of items | slice : 0 : 2"
                      >
                        <span class="ng-value-label">{{ item.label }}</span>
                        <span
                          class="ng-value-icon right"
                          (click)="clear(item)"
                          aria-hidden="true"
                          >×</span
                        >
                      </div>
                      <div class="ng-value" *ngIf="items.length > 2">
                        <span class="ng-value-label"
                          >{{ items.length - 2 }} More</span
                        >
                        <span
                          class="ng-value-icon right"
                          (click)="clearAllRemainingItems(items, clear)"
                          aria-hidden="true"
                          >×</span
                        >
                      </div>
                    </ng-template>
                  </ng-select>
                </fieldset>
              </div>
            </div>

            <!-- Tìm kiếm/Lọc button -->
            <div class="col-12 d-flex justify-content-end mb-2">
              <button
                type="button"
                class="btn btn-primary-theme btn-sm"
                rippleEffect
                (click)="onSubmitForm()"
                [disabled]="
                  isLoadingGraph ||
                  isLoadingGraphExternal ||
                  formState.depthError ||
                  formState.global_limitError ||
                  formState.limit_per_seedError
                "
              >
                <ng-container *ngIf="!isLoadingGraph && !isLoadingGraphExternal; else loadingTpl">
                  Lọc
                </ng-container>
                <ng-template #loadingTpl>
                  <span class="simple-spinner mr-25"></span>
                  Đang tải dữ liệu...
                </ng-template>
              </button>
            </div>
          </div>
        </ng-container>
      </div>
    </form>
  </div>
</div>

<div *ngIf="!isFullTableView" class="graph-container mb-3">
  <div class="graph-content" [ngClass]="{ 'is-loading': isLoadingGraph || isLoadingGraphExternal }">
    <div
      *ngIf="chartOption"
      echarts
      [options]="chartOption"
      class="chart"
      [initOpts]="{ renderer: 'svg' }"
      (chartInit)="onChartInit($event)"
    ></div>
    <div *ngIf="!chartOption" class="loading-message">
      Đang cập nhật dữ liệu đồ thị...
    </div>
  </div>

  <!-- Document details table -->
  <div class="document-table-wrapper">
    <div *ngIf="showDocumentTable" class="document-table-container mt-1 p-1">
      <span class="mb-1 d-flex align-items-center justify-content-between">
        <b class="mb-0">{{ dataFile.ten_day_du || "" }}</b>
        <button
          type="button"
          class="close-btn"
          (click)="onCloseDocumentTable()"
          aria-label="Đóng"
        >
          &times;
        </button>
      </span>
      <table class="document-table">
        <tbody>
          <tr>
            <td class="document-table__label">Số ký hiệu</td>
            <td class="document-table__value">{{ dataFile.so_hieu }}</td>
            <td class="document-table__label">Ngày ban hành</td>
            <td class="document-table__value">{{ dataFile.ngay_ban_hanh }}</td>
          </tr>
          <tr>
            <td class="document-table__label">Loại văn bản</td>
            <td class="document-table__value">{{ dataFile.loai_van_ban }}</td>
            <td class="document-table__label">Ngày có hiệu lực</td>
            <td class="document-table__value">
              {{ dataFile.ngay_co_hieu_luc }}
            </td>
          </tr>
          <tr>
            <td class="document-table__label">
              Cơ quan ban hành/ Chức danh/ Người ký
            </td>
            <td class="document-table__value">
              {{ dataFile.co_quan_ban_hanh || "Không có" }} /
              {{ dataFile?.chuc_danh || "Không có" }} /
              {{ dataFile.nguoi_ky || "Không có" }}
            </td>
            <td class="document-table__label">Phạm vi</td>
            <!-- <td class="document-table__value">{{ dataFile.pham_vi }}</td> -->
            <td class="document-table__value">Toàn quốc</td>
          </tr>
          <tr>
            <td class="document-table__label">Trích yếu</td>
            <td
              class="document-table__value"
              [attr.colspan]="3"
              [innerHTML]="dataFile?.trich_yeu"
            ></td>
          </tr>
          <tr *ngIf="typeDocument !== 'upload'">
            <td class="document-table__label">Tình trạng hiệu lực</td>
            <td
              class="document-table__value document-status"
              [attr.colspan]="3"
              [ngClass]="{
                'document-status--active':
                  dataFile.tinh_trang_hieu_luc === 'Còn hiệu lực',
                'document-status--warning':
                  dataFile.tinh_trang_hieu_luc === 'Hết hiệu lực một phần',
                'document-status--danger':
                  dataFile.tinh_trang_hieu_luc === 'Hết hiệu lực toàn bộ',
                'document-status--info': ![
                  'Còn hiệu lực',
                  'Hết hiệu lực một phần',
                  'Hết hiệu lực toàn bộ'
                ].includes(dataFile.tinh_trang_hieu_luc)
              }"
            >
              {{ dataFile.tinh_trang_hieu_luc }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Document list -->
  <div
    class="document-list-container"
    [ngClass]="{ expanded: documentListExpanded }"
  >
    <div class="document-list-panel">
      <div
        class="document-list-header d-flex align-items-center justify-content-between"
      >
        <span class="font-weight-bolder">Danh sách tài liệu</span>
        <img
          class="cursor-pointer toggle-icon"
          *ngIf="documentListExpanded"
          src="assets/images/icons/collab.svg"
          (click)="toggleDocumentList()"
          [ngbTooltip]="'Thu gọn danh sách'"
          container="body"
          aria-label="Đóng"
        />
      </div>
      <div class="document-list-table-wrapper">
        <table class="document-list-table">
          <thead>
            <tr>
              <th
                colspan="2"
                class="d-flex align-items-center justify-content-between"
              >
                <div class="custom-control custom-checkbox">
                  <input
                    type="checkbox"
                    class="custom-control-input"
                    id="selectAllDocuments"
                    name="selectAllDocuments"
                    [(ngModel)]="selectAllDocuments"
                    (change)="onSelectAllDocumentsChange($event)"
                  />
                  <label class="custom-control-label" for="selectAllDocuments">
                    Chọn tất cả
                  </label>
                </div>
                <div
                  (click)="toggleFullDocumentTable()"
                  class="cursor-pointer show-button-toggle d-flex align-items-center"
                >
                  <img
                    src="assets/images/icons/show-table-active.svg"
                    alt="show-table"
                  />
                  <span class="show-button-label ml-1">Dạng bảng</span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of documentList; let i = index">
              <td class="d-flex align-items-center">
                <div class="custom-control custom-checkbox">
                  <input
                    type="checkbox"
                    class="custom-control-input mr-1"
                    [id]="'document-' + item.id"
                    [name]="'document-' + item.id"
                    [(ngModel)]="item.selected"
                    (change)="onDocumentSelectChange($event, item)"
                  />
                  <label
                    class="custom-control-label"
                    [for]="'document-' + item.id"
                  ></label>
                </div>
                <span>{{ item.title }}</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <img
      class="cursor-pointer toggle-icon"
      *ngIf="!documentListExpanded"
      src="assets/images/icons/expand.svg"
      (click)="toggleDocumentList()"
      [ngbTooltip]="'Mở rộng danh sách'"
      container="body"
      aria-label="Mở"
    />
  </div>
</div>

<div *ngIf="isFullTableView" class="card mb-3">
  <div class="d-flex align-items-center justify-content-between mb-1">
    <input
      type="text"
      class="form-control w-25"
      placeholder="Tìm kiếm tài liệu"
      [(ngModel)]="fullTableSearch"
      [ngModelOptions]="{ standalone: true }"
    />
    <button
      type="button"
      class="btn btn-outline-secondary btn-sm d-flex align-items-center"
      (click)="toggleFullDocumentTable()"
    >
      <img
        src="assets/images/icons/show-table-active.svg"
        alt="toggle"
        class="mr-50"
      />
      Dạng danh sách
    </button>
  </div>
  <div class="table-responsive">
    <table class="table table-hover align-middle mb-0">
      <thead class="thead-light">
        <tr class="text-nowrap">
          <th class="th-checkbox">
            <div class="custom-control custom-checkbox">
              <input
                type="checkbox"
                class="custom-control-input"
                id="selectAllDocumentsFull"
                name="selectAllDocumentsFull"
                [checked]="selectAllDocuments"
                (change)="onSelectAllDocumentsChange($event)"
              />
              <label
                class="custom-control-label"
                for="selectAllDocumentsFull"
              ></label>
            </div>
          </th>
          <th class="text-nowrap">Tên văn bản</th>
          <th class="text-nowrap">Loại văn bản</th>
          <th class="text-nowrap">Số hiệu</th>
          <th class="text-nowrap">Ngày ban hành</th>
          <th class="text-nowrap">Ngày có hiệu lực</th>
          <th class="text-nowrap">Trạng thái hiệu lực</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngIf="!documentList || documentList.length === 0">
          <td colspan="7" class="text-center py-3 text-muted">
            Không có tài liệu.
          </td>
        </tr>
        <tr *ngFor="let item of getPaginatedDocuments()">
          <td class="px-1">
            <div class="custom-control custom-checkbox">
              <input
                type="checkbox"
                class="custom-control-input"
                [id]="'full-doc-' + item.id"
                [(ngModel)]="item.selected"
                (change)="onDocumentSelectChange($event, item)"
              />
              <label
                class="custom-control-label"
                [for]="'full-doc-' + item.id"
              ></label>
            </div>
          </td>
          <td
            class="text-truncate"
            [ngbTooltip]="item.apiNode?.thuoc_tinh?.ten_day_du"
            container="body"
          >
            {{ item.apiNode?.thuoc_tinh?.ten_day_du || item.title }}
          </td>
          <td class="text-nowrap">
            {{ item.apiNode?.thuoc_tinh?.loai_van_ban || "" }}
          </td>
          <td class="text-nowrap">
            {{ item.apiNode?.thuoc_tinh?.so_hieu || "" }}
          </td>
          <td class="text-nowrap">
            {{
              item.apiNode?.thuoc_tinh?.ngay_ban_hanh
                | date : "dd/MM/yyyy" : "+0000"
            }}
          </td>
          <td class="text-nowrap">
            {{
              item.apiNode?.thuoc_tinh?.ngay_co_hieu_luc
                | date : "dd/MM/yyyy" : "+0000"
            }}
          </td>
          <td class="text-nowrap">
            <span
              class="badge badge-pill"
              [ngClass]="{
                'badge-light-success':
                  item.apiNode?.thuoc_tinh?.tinh_trang_hieu_luc ===
                  'Còn hiệu lực',
                'badge-light-warning':
                  item.apiNode?.thuoc_tinh?.tinh_trang_hieu_luc ===
                  'Hết hiệu lực một phần',
                'badge-light-danger':
                  item.apiNode?.thuoc_tinh?.tinh_trang_hieu_luc ===
                  'Hết hiệu lực toàn bộ',
                'badge-light-info': ![
                  'Còn hiệu lực',
                  'Hết hiệu lực một phần',
                  'Hết hiệu lực toàn bộ'
                ].includes(item.apiNode?.thuoc_tinh?.tinh_trang_hieu_luc || '')
              }"
            >
              {{ item.apiNode?.thuoc_tinh?.tinh_trang_hieu_luc || "" }}
            </span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <ngb-pagination
    class="d-flex justify-content-end mt-1"
    [collectionSize]="totalItem"
    [page]="page"
    [pageSize]="pageSize"
    [maxSize]="5"
    [rotate]="true"
    [ellipses]="false"
    [boundaryLinks]="true"
    (pageChange)="onPageChange($event)"
  >
    <ng-template ngbPaginationFirst>
      <i class="datatable-icon-prev font-size-14px"></i>
    </ng-template>
    <ng-template ngbPaginationPrevious>
      <i class="datatable-icon-left font-size-14px"></i>
    </ng-template>
    <ng-template ngbPaginationNext>
      <i class="datatable-icon-right font-size-14px"></i>
    </ng-template>
    <ng-template ngbPaginationLast>
      <i class="datatable-icon-skip font-size-14px"></i>
    </ng-template>
  </ngb-pagination>
</div>

<!-- Selection bar for saving documents (applies to both views) -->
<div class="save-file" *ngIf="selectedFiles.length > 0">
  <div
    class="selection-bar p-1 d-flex align-items-center justify-content-between"
  >
    <button
      class="close-btn mr-1"
      (click)="clearSelectedDocuments()"
      aria-label="Bỏ chọn tất cả"
    >
      &times;
    </button>
    <span class="height-30px mx-1"></span>
    <span class="selected-text"
      >Đã chọn {{ selectedFiles.length }} tài liệu</span
    >
    <span class="height-30px mx-1"></span>
    <button
      class="save-btn d-flex align-items-center"
      (click)="saveHistoryFiles()"
      [disabled]="isSavingFiles"
    >
      <img src="assets/images/icons/folder-star.svg" alt="folder-star" />
      <span class="ms-2">{{ isSavingFiles ? 'Đang lưu' : 'Lưu tài liệu' }}</span>
    </button>
  </div>
</div>

<!-- Context menu for graph nodes -->
<div
  class="context-menu"
  *ngIf="contextMenuVisible"
  [style.top.px]="contextMenuPosition.y"
  [style.left.px]="contextMenuPosition.x"
  (click)="$event.stopPropagation()"
>
  <ul>
    <li (click)="onExpandNode(); $event.stopPropagation()">
      <span data-feather="maximize-2" class="mr-25"></span> Mở rộng
    </li>
    <li
      [class.disabled]="!contextMenuItem?.canRestore"
      (click)="
        contextMenuItem?.canRestore && onRestoreNode(); $event.stopPropagation()
      "
    >
      <span data-feather="refresh-cw" class="mr-25"></span> Khôi phục
    </li>
    <li (click)="onCollapseNode(); $event.stopPropagation()">
      <span data-feather="eye-off" class="mr-25"></span> Ẩn
    </li>
  </ul>
</div>
