import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit, NgZone, ElementRef, ViewChild } from "@angular/core";
import { CoreConfigService } from "@core/services/config.service";
import { filter, takeUntil } from 'rxjs/operators';
import { Subject } from "rxjs";
import { WorkspaceFeature } from "./landing-workspace-card/landing-workspace-card.component";
import { Article } from "./landing-articles-carousel/landing-article/landing-article.component";
import { HttpClient } from "@angular/common/http";
// import { CountUp } from "countup.js";
import { environment } from "environments/environment";
import { ActivatedRoute, Router, NavigationEnd } from '@angular/router';

@Component({
  selector: "app-landing",
  templateUrl: "./landing.component.html",
  styleUrls: ["./landing.component.scss"],
})
export class LandingComponent implements OnInit, OnDestroy, AfterViewInit {

  @ViewChild('statsGrid', { static: false }) statsGrid?: ElementRef<HTMLDivElement>;
  private countupStarted = false;
  private CountUpCtor?: any;
  // Public
  public coreConfig: any;
  public isScrolled = false;

  // Private
  private _unsubscribeAll: Subject<any>;
  private readonly API_BASE = (environment.apiUrl || "").replace(/\/+$/, "");
  private readonly CMS_LIST = `${this.API_BASE}/cms/cms_content`;
  private readonly CMS_LANDING_ARTICLES = `${this.CMS_LIST}/landing_articles`;
  public activeSection: 'chatbot' | 'information' | 'mission' | 'articles' | 'contact' = 'information';
  private sectionIds = ['chatbot','information', 'mission', 'articles', 'contact'];
  private observer?: IntersectionObserver;
  private sidebarEl?: HTMLElement;
  private sidebarTop = 0;
  private readonly NAV_TOP_OFFSET = 96;
  public isNavStuck = false;
  public fullLoginUrl = window.location.origin + '/pages/authentication/login-v2';
  public urls = {
    url1: "https://drive.google.com/drive/folders/1kMQUNE7zD7K277-F6Kh7dnl6rh8NqpWC?usp=drive_link", // Replace with your first URL
    url2: "https://forms.gle/xB7ao9UjBKNPFt8h7", // Replace with your second URL
  }; 
  private statsIO?: IntersectionObserver;
  public isMenuOpen = false;
  public showBackToTop = false;
  private viewReady = false;
  private pendingFragment: string | null = null;

  toggleMenu(): void {
    this.isMenuOpen = !this.isMenuOpen;
    document.body.style.overflow = this.isMenuOpen ? 'hidden' : '';
  }

  closeMenu(): void {
    if (!this.isMenuOpen) return;
    this.isMenuOpen = false;
    document.body.style.overflow = '';
  }

  navTo(id: string): void {
    this.closeMenu();
    this.scrollTo(id);
  }

  gotoLogin(): void {
    this.closeMenu();
    window.location.href = this.fullLoginUrl;
  }
  private scrollToSectionSafe(id: string) {
    const el = document.getElementById(id);
    if (!el) return;

    // Cuộn dựa vào scroll-margin-top (đã khai trong SCSS)
    el.scrollIntoView({ behavior: 'auto', block: 'start' });

    // Lịch thêm 2 nhịp để tránh lệch do ảnh/carousel reflow
    requestAnimationFrame(() => {
      el.scrollIntoView({ behavior: 'smooth', block: 'start' });
    });
    setTimeout(() => {
      el.scrollIntoView({ behavior: 'auto', block: 'start' });
    }, 300);
  }
  @HostListener('window:resize')
  onResize(): void {
    if (window.innerWidth > 992 && this.isMenuOpen) {
      this.closeMenu();
    }
  }

  private animateNumberOnce(el: HTMLElement, end: number, suffix = '', duration = 1500) {
    if (el.dataset['animated'] === 'true') return;
    el.dataset['animated'] = 'true';

    const startTs = performance.now();
    const startVal = 0;
    const endVal = Math.max(0, Math.floor(end));
    const easeOutCubic = (t: number) => 1 - Math.pow(1 - t, 3);

    const tick = (now: number) => {
      const t = Math.min(1, (now - startTs) / duration);
      const eased = easeOutCubic(t);
      const val = Math.floor(startVal + (endVal - startVal) * eased);
      // hiển thị dạng 3.000 / 120.000 theo locale VN
      el.textContent = `${val.toLocaleString('vi-VN')}${suffix}`;
      if (t < 1) requestAnimationFrame(tick);
    };

    requestAnimationFrame(tick);
  }

  /**
   * Constructor
   *
   * @param {CoreConfigService} _coreConfigService
   */
  constructor(private _coreConfigService: CoreConfigService,
    private http: HttpClient,
    private router: Router,
    private zone: NgZone,
    private route: ActivatedRoute,
  ) {
    this._unsubscribeAll = new Subject();

    // Configure the layout
    this._coreConfigService.config = {
      layout: {
        navbar: {
          hidden: true,
        },
        menu: {
          hidden: true,
        },
        footer: {
          hidden: true,
        },
        customizer: false,
        enableLocalStorage: false,
      },
    };
  }

  // Workspace features
  workspaceFeatures: WorkspaceFeature[] = [
    {
      id: "create-workspace",
      title: "Tạo không gian mới",
      description:
        "Một không gian mà người dùng tự do làm việc với nhiều tính năng.",
      imageSrc: "assets/images/pages/landing/workspace_new.svg",
      imageAlt: "Create New Workspace",
    },
    {
      id: "document-lookup",
      title: "Tra cứu văn bản",
      description:
        "Nhiều công cụ hỗ trợ tìm kiếm, các văn bản pháp luật mới được cập nhật liên tục.",
      imageSrc: "assets/images/pages/landing/workspace_lookup.svg",
      imageAlt: "Document Lookup",
    },
    {
      id: "document-management",
      title: "Quản lý văn bản",
      description:
        "Lưu các văn bản tìm kiếm và tải lên các văn bản cá nhân để cùng xử lý.",
      imageSrc: "assets/images/pages/landing/workspace_management.svg",
      imageAlt: "Document Management",
    },
  ];

  // Database stats (for CountUp)
  databaseStats = [
    { id: "stat-decision",  endValue: 230, label: "Quyết định",  color: "blue-800", suffix: "K+" },
    { id: "stat-official1", endValue: 77,  label: "Công văn",    color: "blue-700", suffix: "K+" },
    { id: "stat-circular1", endValue: 14,  label: "Thông báo",    color: "blue-base", suffix: "K+" },
    { id: "stat-other",     endValue: 40,  label: "Văn bản khác",color: "blue-600", suffix: "K+" },

    // (5) giờ là Kế hoạch – ô 1 cột (col-3, hàng 2)
    { id: "stat-plan",      endValue: 29,  label: "Kế hoạch",    color: "blue-700", suffix: "K+" },

    // (6) giờ là Nghị quyết – ô rộng 2 cột (col-4..5, hàng 2)
    { id: "stat-official2", endValue: 42,  label: "Nghị quyết",  color: "blue-600", suffix: "K+" },

    { id: "stat-circular2", endValue: 18,  label: "Thông tư",   color: "blue-base", suffix: "K+" },
  ];


  // Sample articles data
  articles: Article[] = [
  ];

  // Lifecycle Hooks
  // -----------------------------------------------------------------------------------------------------

  /**
   * On init
   */
  ngOnInit(): void {
    this.scrollFromUrlState();
    this.router.events
      .pipe(filter(e => e instanceof NavigationEnd), takeUntil(this._unsubscribeAll))
      .subscribe(() => this.scrollFromUrlState());

    this._coreConfigService.config
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe((config) => { this.coreConfig = config; });

    this.loadPublishedArticles();
  }

  private loadPublishedArticles(): void {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayStr = new Date().toISOString().slice(0, 10);
    this.http.get<any[]>(
        `${this.CMS_LANDING_ARTICLES}?type=HOT`
      )
      .pipe(takeUntil(this._unsubscribeAll))
      .subscribe({
        next: (list) => {
          const apiOrigin = new URL(this.API_BASE).origin;

          const normalized = (list || []).map(a => {
            const publishedRaw = a.publish_date;
            const publishedDate = publishedRaw ? new Date(publishedRaw) : null;
            const dateStr = publishedDate
              ? `${('0'+publishedDate.getDate()).slice(-2)}/${('0'+(publishedDate.getMonth()+1)).slice(-2)}/${publishedDate.getFullYear()}`
              : "";

            const img = a.image
              ? (/^https?:\/\//.test(a.image) ? a.image : `${apiOrigin}${a.image}`)
              : null;

            return <Article>{
              id: a.id,
              slug: a.slug || a.id,
              title: a.title,
              description: a.description || "",
              date: dateStr,
              imageSrc: img || "assets/images/pages/landing/cls-article.svg",
              imageAlt: a.title,
              status: (a.status || "").toUpperCase(),
              type: (a.type || "NORMAL").toUpperCase(),
              coverImage: img,
              publishedAt: publishedRaw,
              // ❌ Landing không còn chứa `body` nữa
              link: `/cls/pages/articles/${a.slug || a.id}`,
            };
          });

          this.articles = normalized;

          const filtered = normalized.filter(x => {
            const isPublished = x.status === 'PUBLISHED';
            if (!isPublished) return false;
            const isFeatured = x.type === 'HOT';
            if (!isFeatured) return false;
            if (!x.publishedAt) return true;
            const d = new Date(x.publishedAt);
            d.setHours(0, 0, 0, 0);
            return d.getTime() <= today.getTime();
          });
          this.articles = filtered;

          const fragment = this.route.snapshot.fragment;
          const focusId = this.route.snapshot.queryParamMap.get('focus');
          if (fragment === 'articles') {
            requestAnimationFrame(() => {
              this.tryFocusArticle(focusId);
            });
          }
        },
        error: (err) => {
          console.error('[cms_content error]', err);
          this.articles = [];
        }
      });
  }

  private tryFocusArticle(focusId: string | null) {
    // Cuộn tới section
    const section = document.getElementById('articles');
    if (section) section.scrollIntoView({ behavior: 'auto', block: 'start' });

    if (focusId) {
      const card = document.getElementById(`article-${focusId}`);
      if (card) {
        card.scrollIntoView({ behavior: 'smooth', block: 'center' });
        card.classList.add('article-focus');
        setTimeout(() => card.classList.remove('article-focus'), 2000);
      }
    }
  }

  private scrollFromUrlState() {
    const fragment = this.route.snapshot.fragment;
    const focusId = this.route.snapshot.queryParamMap.get('focus');

    // Nếu là 1 trong các section => cuộn tới section
    if (fragment && this.sectionIds.includes(fragment)) {
      if (this.viewReady) {
        this.scrollToSectionSafe(fragment);
      } else {
        this.pendingFragment = fragment; // đợi view xong rồi cuộn
      }
    }

    // Logic cũ cho Bài viết + focus card (nếu có)
    if (fragment === 'articles') {
      this.zone.runOutsideAngular(() => {
        setTimeout(() => this.tryFocusArticle(focusId), 0);
      });
    }
  }

  ngAfterViewInit(): void {
    setTimeout(() => {
      this.initializeCountUp();

      const options: IntersectionObserverInit = {
        root: null,
        rootMargin: "-45% 0px -45% 0px",
        threshold: 0
      };
      this.viewReady = true;
      if (this.pendingFragment) {
        this.scrollToSectionSafe(this.pendingFragment);
        this.pendingFragment = null;
      }
      this.observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const id = entry.target.getAttribute('id');
            if (id && this.sectionIds.includes(id)) {
              this.activeSection = id as any;
            }
          }
        });
      }, options);

      const statEls = Array.from(document.querySelectorAll<HTMLElement>(
        '.landing__database-stat-number, .metric-value'
      ));
      this.statsIO = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (!entry.isIntersecting) return;
          const el = entry.target as HTMLElement;
          const end = Number(el.getAttribute('data-end') || '0');
          const suffix = String(el.getAttribute('data-suffix') || '');
          this.animateNumberOnce(el, end, suffix, 1500);
        });
      }, { root: null, rootMargin: '0px 0px -20% 0px', threshold: 0 });

      statEls.forEach(el => this.statsIO!.observe(el));

      this.sectionIds.forEach(id => {
        const el = document.getElementById(id);
        if (el) this.observer!.observe(el);
      });
    }, 0);
  }

  /**
   * On destroy
   */
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    document.body.style.overflow = '';
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
    this.observer?.disconnect();
    this.statsIO?.disconnect();
  }

  @HostListener('window:scroll')
  onWindowScroll(): void {
    this.isScrolled = window.scrollY > 4;

    this.showBackToTop =
      (window.pageYOffset ||
        document.documentElement.scrollTop ||
        document.body.scrollTop ||
        0) > 400;
  }

  scrollToTop(): void {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }

  scrollTo(id: string): void {
    const el = document.getElementById(id);
    if (!el) return;
    el.scrollIntoView({ behavior: 'smooth', block: 'start' });
  }

  private initializeCountUp(): void {
    this.databaseStats.forEach((stat) => {
      const el = document.getElementById(stat.id);
      if (el) {
        // const countUp = new CountUp(stat.id, stat.endValue, {
        //   duration: 5,
        //   separator: ",",
        //   suffix: stat.suffix,
        // });

        // if (!countUp.error) {
        //   countUp.start();
        // } else {
        //   console.error(`CountUp error on ${stat.id}:`, countUp.error);
        // }
      }
    });
  }

  onContactSubmit(event: Event): void {
    event.preventDefault();
    const form = event.target as HTMLFormElement;
    const formData = new FormData(form);
    const payload = {
      fullName: formData.get("fullName"),
      email: formData.get("email"),
      phone: formData.get("phone"),
      message: formData.get("message"),
    };
    // console.log("Contact form submit:", payload);
  }
  
  onSubscribeSubmit(event: Event): void {
    event.preventDefault();
    const form = event.target as HTMLFormElement;
    const formData = new FormData(form);
    const email = String(formData.get('email') || '').trim();
    if (!email) return;
    // console.log('[newsletter] subscribe email:', email);
    form.reset();
  }
}
